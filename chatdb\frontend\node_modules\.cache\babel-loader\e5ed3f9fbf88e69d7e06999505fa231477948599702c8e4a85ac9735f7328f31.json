{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\pages\\\\ConnectionsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, message, Popconfirm, Card, Typography } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, CheckCircleOutlined, DatabaseOutlined } from '@ant-design/icons';\nimport * as api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  Title\n} = Typography;\nconst ConnectionsPage = () => {\n  _s();\n  const [connections, setConnections] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingConnection, setEditingConnection] = useState(null);\n  const [discoveringSchema, setDiscoveringSchema] = useState(false);\n  const [selectedDbType, setSelectedDbType] = useState('mysql');\n  const [form] = Form.useForm();\n  useEffect(() => {\n    fetchConnections();\n  }, []);\n  const fetchConnections = async () => {\n    setLoading(true);\n    try {\n      const response = await api.getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取连接失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const showModal = connection => {\n    setEditingConnection(connection || null);\n    form.resetFields();\n    if (connection) {\n      form.setFieldsValue({\n        name: connection.name,\n        db_type: connection.db_type,\n        host: connection.host,\n        port: connection.port,\n        username: connection.username,\n        database_name: connection.database_name\n      });\n      setSelectedDbType(connection.db_type);\n    } else {\n      setSelectedDbType('mysql');\n    }\n    setModalVisible(true);\n  };\n  const handleCancel = () => {\n    setModalVisible(false);\n  };\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 为SQLite连接设置默认值\n      if (values.db_type === 'sqlite') {\n        values.host = values.host || 'localhost';\n        values.port = values.port || 0;\n        values.username = values.username || '';\n        values.password = values.password || '';\n      }\n      if (editingConnection) {\n        await api.updateConnection(editingConnection.id, values);\n        message.success('连接更新成功');\n        setModalVisible(false);\n        fetchConnections();\n      } else {\n        // 创建新连接\n        setModalVisible(false); // 先关闭模态框\n        const createResponse = await api.createConnection(values);\n        message.success('连接创建成功');\n        fetchConnections();\n\n        // 自动发现并保存数据库结构\n        await handleDiscoverSchema(createResponse.data.id);\n      }\n    } catch (error) {\n      message.error('保存连接失败');\n      console.error(error);\n    }\n  };\n\n  // 发现并保存数据库结构\n  const handleDiscoverSchema = async connectionId => {\n    try {\n      setDiscoveringSchema(true);\n      message.loading({\n        content: '正在分析数据库结构...',\n        key: 'discoverSchema',\n        duration: 0\n      });\n      const response = await api.discoverAndSaveSchema(connectionId);\n      if (response.data.status === 'success') {\n        message.success({\n          content: '数据库结构分析完成',\n          key: 'discoverSchema'\n        });\n        console.log('Discovered schema:', response.data);\n      } else {\n        message.error({\n          content: '数据库结构分析失败',\n          key: 'discoverSchema'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to discover schema:', error);\n      message.error({\n        content: '数据库结构分析失败',\n        key: 'discoverSchema'\n      });\n    } finally {\n      setDiscoveringSchema(false);\n    }\n  };\n  const handleDelete = async id => {\n    try {\n      await api.deleteConnection(id);\n      message.success('连接删除成功');\n      fetchConnections();\n    } catch (error) {\n      message.error('删除连接失败');\n      console.error(error);\n    }\n  };\n  const handleTest = async id => {\n    try {\n      const response = await api.testConnection(id);\n      if (response.data.status === 'success') {\n        message.success('连接测试成功');\n      } else {\n        message.error(`连接测试失败: ${response.data.message}`);\n      }\n    } catch (error) {\n      message.error('连接测试失败');\n      console.error(error);\n    }\n  };\n  const columns = [{\n    title: '名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '类型',\n    dataIndex: 'db_type',\n    key: 'db_type'\n  }, {\n    title: '主机',\n    dataIndex: 'host',\n    key: 'host'\n  }, {\n    title: '端口',\n    dataIndex: 'port',\n    key: 'port'\n  }, {\n    title: '数据库',\n    dataIndex: 'database_name',\n    key: 'database_name'\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleTest(record.id),\n        size: \"small\",\n        children: \"\\u6D4B\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDiscoverSchema(record.id),\n        size: \"small\",\n        loading: discoveringSchema,\n        children: \"\\u5206\\u6790\\u7ED3\\u6784\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 19\n        }, this),\n        onClick: () => showModal(record),\n        size: \"small\",\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u8FDE\\u63A5\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u662F\",\n        cancelText: \"\\u5426\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 19\n          }, this),\n          onClick: () => showModal(),\n          disabled: discoveringSchema,\n          children: \"\\u6DFB\\u52A0\\u8FDE\\u63A5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: connections,\n        rowKey: \"id\",\n        loading: loading || discoveringSchema\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingConnection ? '编辑连接' : '添加连接',\n      open: modalVisible,\n      onOk: handleSubmit,\n      onCancel: handleCancel,\n      width: 600,\n      confirmLoading: discoveringSchema,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          db_type: 'mysql',\n          port: 3306\n        },\n        disabled: discoveringSchema,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8FDE\\u63A5\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u6211\\u7684\\u6570\\u636E\\u5E93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"db_type\",\n          label: \"\\u6570\\u636E\\u5E93\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择数据库类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u6570\\u636E\\u5E93\\u7C7B\\u578B\",\n            onChange: value => {\n              setSelectedDbType(value);\n              // 当切换到SQLite时，清空用户名和密码字段\n              if (value === 'sqlite') {\n                form.setFieldsValue({\n                  username: '',\n                  password: '',\n                  host: 'localhost',\n                  port: 0\n                });\n              } else if (value === 'mysql') {\n                form.setFieldsValue({\n                  port: 3306\n                });\n              } else if (value === 'postgresql') {\n                form.setFieldsValue({\n                  port: 5432\n                });\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"mysql\",\n              children: \"MySQL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"postgresql\",\n              children: \"PostgreSQL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"sqlite\",\n              children: \"SQLite\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"host\",\n          label: \"\\u4E3B\\u673A\",\n          rules: [{\n            required: true,\n            message: '请输入主机'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"localhost\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"port\",\n          label: \"\\u7AEF\\u53E3\",\n          rules: [{\n            required: true,\n            message: '请输入端口'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"number\",\n            placeholder: \"3306\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          label: \"\\u7528\\u6237\\u540D\",\n          rules: [{\n            required: true,\n            message: '请输入用户名'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"root\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"\\u5BC6\\u7801\",\n          rules: [{\n            required: !editingConnection,\n            message: '请输入密码'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            placeholder: \"\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"database_name\",\n          label: \"\\u6570\\u636E\\u5E93\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入数据库名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u6211\\u7684\\u6570\\u636E\\u5E93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 227,\n    columnNumber: 5\n  }, this);\n};\n_s(ConnectionsPage, \"7bprerNenKkOqRljdYgB2YgezYk=\", false, function () {\n  return [Form.useForm];\n});\n_c = ConnectionsPage;\nexport default ConnectionsPage;\nvar _c;\n$RefreshReg$(_c, \"ConnectionsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Space", "message", "Popconfirm", "Card", "Typography", "PlusOutlined", "EditOutlined", "DeleteOutlined", "CheckCircleOutlined", "DatabaseOutlined", "api", "jsxDEV", "_jsxDEV", "Option", "Title", "ConnectionsPage", "_s", "connections", "setConnections", "loading", "setLoading", "modalVisible", "setModalVisible", "editingConnection", "setEditingConnection", "discoveringSchema", "setDiscoveringSchema", "selectedDbType", "setSelectedDbType", "form", "useForm", "fetchConnections", "response", "getConnections", "data", "error", "console", "showModal", "connection", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "db_type", "host", "port", "username", "database_name", "handleCancel", "handleSubmit", "values", "validateFields", "password", "updateConnection", "id", "success", "createResponse", "createConnection", "handleDiscoverSchema", "connectionId", "content", "key", "duration", "discoverAndSaveSchema", "status", "log", "handleDelete", "deleteConnection", "handleTest", "testConnection", "columns", "title", "dataIndex", "render", "_", "record", "size", "children", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onConfirm", "okText", "cancelText", "danger", "style", "display", "justifyContent", "marginBottom", "level", "disabled", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "width", "confirmLoading", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "value", "Password", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/pages/ConnectionsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table, Button, Modal, Form, Input, Select,\n  Space, message, Popconfirm, Card, Typography\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, CheckCircleOutlined, DatabaseOutlined } from '@ant-design/icons';\nimport * as api from '../services/api';\n\nconst { Option } = Select;\nconst { Title } = Typography;\n\ninterface Connection {\n  id: number;\n  name: string;\n  db_type: string;\n  host: string;\n  port: number;\n  username: string;\n  database_name: string;\n  created_at: string;\n  updated_at: string;\n}\n\nconst ConnectionsPage: React.FC = () => {\n  const [connections, setConnections] = useState<Connection[]>([]);\n  const [loading, setLoading] = useState<boolean>(false);\n  const [modalVisible, setModalVisible] = useState<boolean>(false);\n  const [editingConnection, setEditingConnection] = useState<Connection | null>(null);\n  const [discoveringSchema, setDiscoveringSchema] = useState<boolean>(false);\n  const [selectedDbType, setSelectedDbType] = useState<string>('mysql');\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchConnections();\n  }, []);\n\n  const fetchConnections = async () => {\n    setLoading(true);\n    try {\n      const response = await api.getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取连接失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const showModal = (connection?: Connection) => {\n    setEditingConnection(connection || null);\n    form.resetFields();\n    if (connection) {\n      form.setFieldsValue({\n        name: connection.name,\n        db_type: connection.db_type,\n        host: connection.host,\n        port: connection.port,\n        username: connection.username,\n        database_name: connection.database_name,\n      });\n      setSelectedDbType(connection.db_type);\n    } else {\n      setSelectedDbType('mysql');\n    }\n    setModalVisible(true);\n  };\n\n  const handleCancel = () => {\n    setModalVisible(false);\n  };\n\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 为SQLite连接设置默认值\n      if (values.db_type === 'sqlite') {\n        values.host = values.host || 'localhost';\n        values.port = values.port || 0;\n        values.username = values.username || '';\n        values.password = values.password || '';\n      }\n\n      if (editingConnection) {\n        await api.updateConnection(editingConnection.id, values);\n        message.success('连接更新成功');\n        setModalVisible(false);\n        fetchConnections();\n      } else {\n        // 创建新连接\n        setModalVisible(false); // 先关闭模态框\n        const createResponse = await api.createConnection(values);\n        message.success('连接创建成功');\n        fetchConnections();\n\n        // 自动发现并保存数据库结构\n        await handleDiscoverSchema(createResponse.data.id);\n      }\n    } catch (error) {\n      message.error('保存连接失败');\n      console.error(error);\n    }\n  };\n\n  // 发现并保存数据库结构\n  const handleDiscoverSchema = async (connectionId: number) => {\n    try {\n      setDiscoveringSchema(true);\n      message.loading({ content: '正在分析数据库结构...', key: 'discoverSchema', duration: 0 });\n\n      const response = await api.discoverAndSaveSchema(connectionId);\n\n      if (response.data.status === 'success') {\n        message.success({ content: '数据库结构分析完成', key: 'discoverSchema' });\n        console.log('Discovered schema:', response.data);\n      } else {\n        message.error({ content: '数据库结构分析失败', key: 'discoverSchema' });\n      }\n    } catch (error) {\n      console.error('Failed to discover schema:', error);\n      message.error({ content: '数据库结构分析失败', key: 'discoverSchema' });\n    } finally {\n      setDiscoveringSchema(false);\n    }\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await api.deleteConnection(id);\n      message.success('连接删除成功');\n      fetchConnections();\n    } catch (error) {\n      message.error('删除连接失败');\n      console.error(error);\n    }\n  };\n\n  const handleTest = async (id: number) => {\n    try {\n      const response = await api.testConnection(id);\n      if (response.data.status === 'success') {\n        message.success('连接测试成功');\n      } else {\n        message.error(`连接测试失败: ${response.data.message}`);\n      }\n    } catch (error) {\n      message.error('连接测试失败');\n      console.error(error);\n    }\n  };\n\n  const columns = [\n    {\n      title: '名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '类型',\n      dataIndex: 'db_type',\n      key: 'db_type',\n    },\n    {\n      title: '主机',\n      dataIndex: 'host',\n      key: 'host',\n    },\n    {\n      title: '端口',\n      dataIndex: 'port',\n      key: 'port',\n    },\n    {\n      title: '数据库',\n      dataIndex: 'database_name',\n      key: 'database_name',\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_: any, record: Connection) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"primary\"\n            icon={<CheckCircleOutlined />}\n            onClick={() => handleTest(record.id)}\n            size=\"small\"\n          >\n            测试\n          </Button>\n          <Button\n            icon={<DatabaseOutlined />}\n            onClick={() => handleDiscoverSchema(record.id)}\n            size=\"small\"\n            loading={discoveringSchema}\n          >\n            分析结构\n          </Button>\n          <Button\n            icon={<EditOutlined />}\n            onClick={() => showModal(record)}\n            size=\"small\"\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个连接吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"是\"\n            cancelText=\"否\"\n          >\n            <Button\n              danger\n              icon={<DeleteOutlined />}\n              size=\"small\"\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>\n          <Title level={4}>数据库连接</Title>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => showModal()}\n            disabled={discoveringSchema}\n          >\n            添加连接\n          </Button>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={connections}\n          rowKey=\"id\"\n          loading={loading || discoveringSchema}\n        />\n      </Card>\n\n      <Modal\n        title={editingConnection ? '编辑连接' : '添加连接'}\n        open={modalVisible}\n        onOk={handleSubmit}\n        onCancel={handleCancel}\n        width={600}\n        confirmLoading={discoveringSchema}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{ db_type: 'mysql', port: 3306 }}\n          disabled={discoveringSchema}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"连接名称\"\n            rules={[{ required: true, message: '请输入名称' }]}\n          >\n            <Input placeholder=\"我的数据库\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"db_type\"\n            label=\"数据库类型\"\n            rules={[{ required: true, message: '请选择数据库类型' }]}\n          >\n            <Select\n              placeholder=\"选择数据库类型\"\n              onChange={(value) => {\n                setSelectedDbType(value);\n                // 当切换到SQLite时，清空用户名和密码字段\n                if (value === 'sqlite') {\n                  form.setFieldsValue({\n                    username: '',\n                    password: '',\n                    host: 'localhost',\n                    port: 0\n                  });\n                } else if (value === 'mysql') {\n                  form.setFieldsValue({\n                    port: 3306\n                  });\n                } else if (value === 'postgresql') {\n                  form.setFieldsValue({\n                    port: 5432\n                  });\n                }\n              }}\n            >\n              <Option value=\"mysql\">MySQL</Option>\n              <Option value=\"postgresql\">PostgreSQL</Option>\n              <Option value=\"sqlite\">SQLite</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"host\"\n            label=\"主机\"\n            rules={[{ required: true, message: '请输入主机' }]}\n          >\n            <Input placeholder=\"localhost\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"port\"\n            label=\"端口\"\n            rules={[{ required: true, message: '请输入端口' }]}\n          >\n            <Input type=\"number\" placeholder=\"3306\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"username\"\n            label=\"用户名\"\n            rules={[{ required: true, message: '请输入用户名' }]}\n          >\n            <Input placeholder=\"root\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            label=\"密码\"\n            rules={[\n              {\n                required: !editingConnection,\n                message: '请输入密码'\n              }\n            ]}\n          >\n            <Input.Password placeholder=\"密码\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"database_name\"\n            label=\"数据库名称\"\n            rules={[{ required: true, message: '请输入数据库名称' }]}\n          >\n            <Input placeholder=\"我的数据库\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ConnectionsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EACzCC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,EAAEC,UAAU,QACvC,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,gBAAgB,QAAQ,mBAAmB;AACrH,OAAO,KAAKC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAM;EAAEC;AAAO,CAAC,GAAGd,MAAM;AACzB,MAAM;EAAEe;AAAM,CAAC,GAAGV,UAAU;AAc5B,MAAMW,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAoB,IAAI,CAAC;EACnF,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAU,KAAK,CAAC;EAC1E,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAS,OAAO,CAAC;EACrE,MAAM,CAACqC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;EAE7BrC,SAAS,CAAC,MAAM;IACdsC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMtB,GAAG,CAACuB,cAAc,CAAC,CAAC;MAC3Cf,cAAc,CAACc,QAAQ,CAACE,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,SAAS,GAAIC,UAAuB,IAAK;IAC7Cd,oBAAoB,CAACc,UAAU,IAAI,IAAI,CAAC;IACxCT,IAAI,CAACU,WAAW,CAAC,CAAC;IAClB,IAAID,UAAU,EAAE;MACdT,IAAI,CAACW,cAAc,CAAC;QAClBC,IAAI,EAAEH,UAAU,CAACG,IAAI;QACrBC,OAAO,EAAEJ,UAAU,CAACI,OAAO;QAC3BC,IAAI,EAAEL,UAAU,CAACK,IAAI;QACrBC,IAAI,EAAEN,UAAU,CAACM,IAAI;QACrBC,QAAQ,EAAEP,UAAU,CAACO,QAAQ;QAC7BC,aAAa,EAAER,UAAU,CAACQ;MAC5B,CAAC,CAAC;MACFlB,iBAAiB,CAACU,UAAU,CAACI,OAAO,CAAC;IACvC,CAAC,MAAM;MACLd,iBAAiB,CAAC,OAAO,CAAC;IAC5B;IACAN,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyB,YAAY,GAAGA,CAAA,KAAM;IACzBzB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAM0B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMpB,IAAI,CAACqB,cAAc,CAAC,CAAC;;MAE1C;MACA,IAAID,MAAM,CAACP,OAAO,KAAK,QAAQ,EAAE;QAC/BO,MAAM,CAACN,IAAI,GAAGM,MAAM,CAACN,IAAI,IAAI,WAAW;QACxCM,MAAM,CAACL,IAAI,GAAGK,MAAM,CAACL,IAAI,IAAI,CAAC;QAC9BK,MAAM,CAACJ,QAAQ,GAAGI,MAAM,CAACJ,QAAQ,IAAI,EAAE;QACvCI,MAAM,CAACE,QAAQ,GAAGF,MAAM,CAACE,QAAQ,IAAI,EAAE;MACzC;MAEA,IAAI5B,iBAAiB,EAAE;QACrB,MAAMb,GAAG,CAAC0C,gBAAgB,CAAC7B,iBAAiB,CAAC8B,EAAE,EAAEJ,MAAM,CAAC;QACxDhD,OAAO,CAACqD,OAAO,CAAC,QAAQ,CAAC;QACzBhC,eAAe,CAAC,KAAK,CAAC;QACtBS,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM;QACL;QACAT,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;QACxB,MAAMiC,cAAc,GAAG,MAAM7C,GAAG,CAAC8C,gBAAgB,CAACP,MAAM,CAAC;QACzDhD,OAAO,CAACqD,OAAO,CAAC,QAAQ,CAAC;QACzBvB,gBAAgB,CAAC,CAAC;;QAElB;QACA,MAAM0B,oBAAoB,CAACF,cAAc,CAACrB,IAAI,CAACmB,EAAE,CAAC;MACpD;IACF,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMsB,oBAAoB,GAAG,MAAOC,YAAoB,IAAK;IAC3D,IAAI;MACFhC,oBAAoB,CAAC,IAAI,CAAC;MAC1BzB,OAAO,CAACkB,OAAO,CAAC;QAAEwC,OAAO,EAAE,cAAc;QAAEC,GAAG,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAE,CAAC,CAAC;MAEhF,MAAM7B,QAAQ,GAAG,MAAMtB,GAAG,CAACoD,qBAAqB,CAACJ,YAAY,CAAC;MAE9D,IAAI1B,QAAQ,CAACE,IAAI,CAAC6B,MAAM,KAAK,SAAS,EAAE;QACtC9D,OAAO,CAACqD,OAAO,CAAC;UAAEK,OAAO,EAAE,WAAW;UAAEC,GAAG,EAAE;QAAiB,CAAC,CAAC;QAChExB,OAAO,CAAC4B,GAAG,CAAC,oBAAoB,EAAEhC,QAAQ,CAACE,IAAI,CAAC;MAClD,CAAC,MAAM;QACLjC,OAAO,CAACkC,KAAK,CAAC;UAAEwB,OAAO,EAAE,WAAW;UAAEC,GAAG,EAAE;QAAiB,CAAC,CAAC;MAChE;IACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDlC,OAAO,CAACkC,KAAK,CAAC;QAAEwB,OAAO,EAAE,WAAW;QAAEC,GAAG,EAAE;MAAiB,CAAC,CAAC;IAChE,CAAC,SAAS;MACRlC,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAMuC,YAAY,GAAG,MAAOZ,EAAU,IAAK;IACzC,IAAI;MACF,MAAM3C,GAAG,CAACwD,gBAAgB,CAACb,EAAE,CAAC;MAC9BpD,OAAO,CAACqD,OAAO,CAAC,QAAQ,CAAC;MACzBvB,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMgC,UAAU,GAAG,MAAOd,EAAU,IAAK;IACvC,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMtB,GAAG,CAAC0D,cAAc,CAACf,EAAE,CAAC;MAC7C,IAAIrB,QAAQ,CAACE,IAAI,CAAC6B,MAAM,KAAK,SAAS,EAAE;QACtC9D,OAAO,CAACqD,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACLrD,OAAO,CAACkC,KAAK,CAAC,WAAWH,QAAQ,CAACE,IAAI,CAACjC,OAAO,EAAE,CAAC;MACnD;IACF,CAAC,CAAC,OAAOkC,KAAK,EAAE;MACdlC,OAAO,CAACkC,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMkC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,SAAS;IACpBX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,eAAe;IAC1BX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,IAAI;IACXV,GAAG,EAAE,SAAS;IACdY,MAAM,EAAEA,CAACC,CAAM,EAAEC,MAAkB,kBACjC9D,OAAA,CAACZ,KAAK;MAAC2E,IAAI,EAAC,QAAQ;MAAAC,QAAA,gBAClBhE,OAAA,CAACjB,MAAM;QACLkF,IAAI,EAAC,SAAS;QACdC,IAAI,eAAElE,OAAA,CAACJ,mBAAmB;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC9BC,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAACO,MAAM,CAACrB,EAAE,CAAE;QACrCsB,IAAI,EAAC,OAAO;QAAAC,QAAA,EACb;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtE,OAAA,CAACjB,MAAM;QACLmF,IAAI,eAAElE,OAAA,CAACH,gBAAgB;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BC,OAAO,EAAEA,CAAA,KAAM1B,oBAAoB,CAACiB,MAAM,CAACrB,EAAE,CAAE;QAC/CsB,IAAI,EAAC,OAAO;QACZxD,OAAO,EAAEM,iBAAkB;QAAAmD,QAAA,EAC5B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtE,OAAA,CAACjB,MAAM;QACLmF,IAAI,eAAElE,OAAA,CAACN,YAAY;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,OAAO,EAAEA,CAAA,KAAM9C,SAAS,CAACqC,MAAM,CAAE;QACjCC,IAAI,EAAC,OAAO;QAAAC,QAAA,EACb;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtE,OAAA,CAACV,UAAU;QACToE,KAAK,EAAC,oEAAa;QACnBc,SAAS,EAAEA,CAAA,KAAMnB,YAAY,CAACS,MAAM,CAACrB,EAAE,CAAE;QACzCgC,MAAM,EAAC,QAAG;QACVC,UAAU,EAAC,QAAG;QAAAV,QAAA,eAEdhE,OAAA,CAACjB,MAAM;UACL4F,MAAM;UACNT,IAAI,eAAElE,OAAA,CAACL,cAAc;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBP,IAAI,EAAC,OAAO;UAAAC,QAAA,EACb;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEtE,OAAA;IAAAgE,QAAA,gBACEhE,OAAA,CAACT,IAAI;MAAAyE,QAAA,gBACHhE,OAAA;QAAK4E,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAf,QAAA,gBACjFhE,OAAA,CAACE,KAAK;UAAC8E,KAAK,EAAE,CAAE;UAAAhB,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9BtE,OAAA,CAACjB,MAAM;UACLkF,IAAI,EAAC,SAAS;UACdC,IAAI,eAAElE,OAAA,CAACP,YAAY;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEA,CAAA,KAAM9C,SAAS,CAAC,CAAE;UAC3BwD,QAAQ,EAAEpE,iBAAkB;UAAAmD,QAAA,EAC7B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtE,OAAA,CAAClB,KAAK;QACJ2E,OAAO,EAAEA,OAAQ;QACjByB,UAAU,EAAE7E,WAAY;QACxB8E,MAAM,EAAC,IAAI;QACX5E,OAAO,EAAEA,OAAO,IAAIM;MAAkB;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPtE,OAAA,CAAChB,KAAK;MACJ0E,KAAK,EAAE/C,iBAAiB,GAAG,MAAM,GAAG,MAAO;MAC3CyE,IAAI,EAAE3E,YAAa;MACnB4E,IAAI,EAAEjD,YAAa;MACnBkD,QAAQ,EAAEnD,YAAa;MACvBoD,KAAK,EAAE,GAAI;MACXC,cAAc,EAAE3E,iBAAkB;MAAAmD,QAAA,eAElChE,OAAA,CAACf,IAAI;QACHgC,IAAI,EAAEA,IAAK;QACXwE,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UAAE5D,OAAO,EAAE,OAAO;UAAEE,IAAI,EAAE;QAAK,CAAE;QAChDiD,QAAQ,EAAEpE,iBAAkB;QAAAmD,QAAA,gBAE5BhE,OAAA,CAACf,IAAI,CAAC0G,IAAI;UACR9D,IAAI,EAAC,MAAM;UACX+D,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzG,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA2E,QAAA,eAE9ChE,OAAA,CAACd,KAAK;YAAC6G,WAAW,EAAC;UAAO;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZtE,OAAA,CAACf,IAAI,CAAC0G,IAAI;UACR9D,IAAI,EAAC,SAAS;UACd+D,KAAK,EAAC,gCAAO;UACbC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzG,OAAO,EAAE;UAAW,CAAC,CAAE;UAAA2E,QAAA,eAEjDhE,OAAA,CAACb,MAAM;YACL4G,WAAW,EAAC,4CAAS;YACrBC,QAAQ,EAAGC,KAAK,IAAK;cACnBjF,iBAAiB,CAACiF,KAAK,CAAC;cACxB;cACA,IAAIA,KAAK,KAAK,QAAQ,EAAE;gBACtBhF,IAAI,CAACW,cAAc,CAAC;kBAClBK,QAAQ,EAAE,EAAE;kBACZM,QAAQ,EAAE,EAAE;kBACZR,IAAI,EAAE,WAAW;kBACjBC,IAAI,EAAE;gBACR,CAAC,CAAC;cACJ,CAAC,MAAM,IAAIiE,KAAK,KAAK,OAAO,EAAE;gBAC5BhF,IAAI,CAACW,cAAc,CAAC;kBAClBI,IAAI,EAAE;gBACR,CAAC,CAAC;cACJ,CAAC,MAAM,IAAIiE,KAAK,KAAK,YAAY,EAAE;gBACjChF,IAAI,CAACW,cAAc,CAAC;kBAClBI,IAAI,EAAE;gBACR,CAAC,CAAC;cACJ;YACF,CAAE;YAAAgC,QAAA,gBAEFhE,OAAA,CAACC,MAAM;cAACgG,KAAK,EAAC,OAAO;cAAAjC,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCtE,OAAA,CAACC,MAAM;cAACgG,KAAK,EAAC,YAAY;cAAAjC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9CtE,OAAA,CAACC,MAAM;cAACgG,KAAK,EAAC,QAAQ;cAAAjC,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZtE,OAAA,CAACf,IAAI,CAAC0G,IAAI;UACR9D,IAAI,EAAC,MAAM;UACX+D,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzG,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA2E,QAAA,eAE9ChE,OAAA,CAACd,KAAK;YAAC6G,WAAW,EAAC;UAAW;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAEZtE,OAAA,CAACf,IAAI,CAAC0G,IAAI;UACR9D,IAAI,EAAC,MAAM;UACX+D,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzG,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA2E,QAAA,eAE9ChE,OAAA,CAACd,KAAK;YAAC+E,IAAI,EAAC,QAAQ;YAAC8B,WAAW,EAAC;UAAM;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEZtE,OAAA,CAACf,IAAI,CAAC0G,IAAI;UACR9D,IAAI,EAAC,UAAU;UACf+D,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzG,OAAO,EAAE;UAAS,CAAC,CAAE;UAAA2E,QAAA,eAE/ChE,OAAA,CAACd,KAAK;YAAC6G,WAAW,EAAC;UAAM;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAEZtE,OAAA,CAACf,IAAI,CAAC0G,IAAI;UACR9D,IAAI,EAAC,UAAU;UACf+D,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YACEC,QAAQ,EAAE,CAACnF,iBAAiB;YAC5BtB,OAAO,EAAE;UACX,CAAC,CACD;UAAA2E,QAAA,eAEFhE,OAAA,CAACd,KAAK,CAACgH,QAAQ;YAACH,WAAW,EAAC;UAAI;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEZtE,OAAA,CAACf,IAAI,CAAC0G,IAAI;UACR9D,IAAI,EAAC,eAAe;UACpB+D,KAAK,EAAC,gCAAO;UACbC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzG,OAAO,EAAE;UAAW,CAAC,CAAE;UAAA2E,QAAA,eAEjDhE,OAAA,CAACd,KAAK;YAAC6G,WAAW,EAAC;UAAO;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAClE,EAAA,CAzUID,eAAyB;EAAA,QAOdlB,IAAI,CAACiC,OAAO;AAAA;AAAAiF,EAAA,GAPvBhG,eAAyB;AA2U/B,eAAeA,eAAe;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}