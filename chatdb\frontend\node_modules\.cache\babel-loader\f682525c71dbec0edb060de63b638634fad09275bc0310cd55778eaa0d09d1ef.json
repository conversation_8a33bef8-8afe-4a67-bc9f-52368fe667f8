{"ast": null, "code": "import Portal from \"./Portal\";\nimport { inlineMock } from \"./mock\";\nexport { inlineMock };\nexport default Portal;", "map": {"version": 3, "names": ["Portal", "inlineMock"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@rc-component/portal/es/index.js"], "sourcesContent": ["import Portal from \"./Portal\";\nimport { inlineMock } from \"./mock\";\nexport { inlineMock };\nexport default Portal;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,SAASC,UAAU,QAAQ,QAAQ;AACnC,SAASA,UAAU;AACnB,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}