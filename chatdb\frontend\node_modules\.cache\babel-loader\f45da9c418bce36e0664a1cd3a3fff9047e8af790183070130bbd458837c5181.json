{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nexport const offset = 4;\nfunction dropIndicatorRender(props) {\n  const {\n    dropPosition,\n    dropLevelOffset,\n    prefixCls,\n    indent,\n    direction = 'ltr'\n  } = props;\n  const startPosition = direction === 'ltr' ? 'left' : 'right';\n  const endPosition = direction === 'ltr' ? 'right' : 'left';\n  const style = {\n    [startPosition]: -dropLevelOffset * indent + offset,\n    [endPosition]: 0\n  };\n  switch (dropPosition) {\n    case -1:\n      style.top = -3;\n      break;\n    case 1:\n      style.bottom = -3;\n      break;\n    default:\n      // dropPosition === 0\n      style.bottom = -3;\n      style[startPosition] = indent + offset;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style,\n    className: `${prefixCls}-drop-indicator`\n  });\n}\nexport default dropIndicatorRender;", "map": {"version": 3, "names": ["React", "offset", "dropIndicatorRender", "props", "dropPosition", "dropLevelOffset", "prefixCls", "indent", "direction", "startPosition", "endPosition", "style", "top", "bottom", "createElement", "className"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/tree/utils/dropIndicator.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nexport const offset = 4;\nfunction dropIndicatorRender(props) {\n  const {\n    dropPosition,\n    dropLevelOffset,\n    prefixCls,\n    indent,\n    direction = 'ltr'\n  } = props;\n  const startPosition = direction === 'ltr' ? 'left' : 'right';\n  const endPosition = direction === 'ltr' ? 'right' : 'left';\n  const style = {\n    [startPosition]: -dropLevelOffset * indent + offset,\n    [endPosition]: 0\n  };\n  switch (dropPosition) {\n    case -1:\n      style.top = -3;\n      break;\n    case 1:\n      style.bottom = -3;\n      break;\n    default:\n      // dropPosition === 0\n      style.bottom = -3;\n      style[startPosition] = indent + offset;\n      break;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: style,\n    className: `${prefixCls}-drop-indicator`\n  });\n}\nexport default dropIndicatorRender;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,MAAM,GAAG,CAAC;AACvB,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,SAAS;IACTC,MAAM;IACNC,SAAS,GAAG;EACd,CAAC,GAAGL,KAAK;EACT,MAAMM,aAAa,GAAGD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;EAC5D,MAAME,WAAW,GAAGF,SAAS,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;EAC1D,MAAMG,KAAK,GAAG;IACZ,CAACF,aAAa,GAAG,CAACJ,eAAe,GAAGE,MAAM,GAAGN,MAAM;IACnD,CAACS,WAAW,GAAG;EACjB,CAAC;EACD,QAAQN,YAAY;IAClB,KAAK,CAAC,CAAC;MACLO,KAAK,CAACC,GAAG,GAAG,CAAC,CAAC;MACd;IACF,KAAK,CAAC;MACJD,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MACjB;IACF;MACE;MACAF,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MACjBF,KAAK,CAACF,aAAa,CAAC,GAAGF,MAAM,GAAGN,MAAM;MACtC;EACJ;EACA,OAAO,aAAaD,KAAK,CAACc,aAAa,CAAC,KAAK,EAAE;IAC7CH,KAAK,EAAEA,KAAK;IACZI,SAAS,EAAE,GAAGT,SAAS;EACzB,CAAC,CAAC;AACJ;AACA,eAAeJ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}