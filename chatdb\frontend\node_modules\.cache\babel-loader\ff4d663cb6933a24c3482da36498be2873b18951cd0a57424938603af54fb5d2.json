{"ast": null, "code": "/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map(x => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: IRPF90\nAuthor: <PERSON> <<EMAIL>>\nDescription: IRPF90 is an open-source Fortran code generator\nWebsite: http://irpf90.ups-tlse.fr\nCategory: scientific\n*/\n\n/** @type LanguageFn */\nfunction irpf90(hljs) {\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n\n  // regex in both fortran and irpf90 should match\n  const OPTIONAL_NUMBER_SUFFIX = /(_[a-z_\\d]+)?/;\n  const OPTIONAL_NUMBER_EXP = /([de][+-]?\\d+)?/;\n  const NUMBER = {\n    className: 'number',\n    variants: [{\n      begin: concat(/\\b\\d+/, /\\.(\\d*)/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n    }, {\n      begin: concat(/\\b\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n    }, {\n      begin: concat(/\\.\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n    }],\n    relevance: 0\n  };\n  const F_KEYWORDS = {\n    literal: '.False. .True.',\n    keyword: 'kind do while private call intrinsic where elsewhere ' + 'type endtype endmodule endselect endinterface end enddo endif if forall endforall only contains default return stop then ' + 'public subroutine|10 function program .and. .or. .not. .le. .eq. .ge. .gt. .lt. ' + 'goto save else use module select case ' + 'access blank direct exist file fmt form formatted iostat name named nextrec number opened rec recl sequential status unformatted unit ' + 'continue format pause cycle exit ' + 'c_null_char c_alert c_backspace c_form_feed flush wait decimal round iomsg ' + 'synchronous nopass non_overridable pass protected volatile abstract extends import ' + 'non_intrinsic value deferred generic final enumerator class associate bind enum ' + 'c_int c_short c_long c_long_long c_signed_char c_size_t c_int8_t c_int16_t c_int32_t c_int64_t c_int_least8_t c_int_least16_t ' + 'c_int_least32_t c_int_least64_t c_int_fast8_t c_int_fast16_t c_int_fast32_t c_int_fast64_t c_intmax_t C_intptr_t c_float c_double ' + 'c_long_double c_float_complex c_double_complex c_long_double_complex c_bool c_char c_null_ptr c_null_funptr ' + 'c_new_line c_carriage_return c_horizontal_tab c_vertical_tab iso_c_binding c_loc c_funloc c_associated  c_f_pointer ' + 'c_ptr c_funptr iso_fortran_env character_storage_size error_unit file_storage_size input_unit iostat_end iostat_eor ' + 'numeric_storage_size output_unit c_f_procpointer ieee_arithmetic ieee_support_underflow_control ' + 'ieee_get_underflow_mode ieee_set_underflow_mode newunit contiguous recursive ' + 'pad position action delim readwrite eor advance nml interface procedure namelist include sequence elemental pure ' + 'integer real character complex logical dimension allocatable|10 parameter ' + 'external implicit|10 none double precision assign intent optional pointer ' + 'target in out common equivalence data ' +\n    // IRPF90 special keywords\n    'begin_provider &begin_provider end_provider begin_shell end_shell begin_template end_template subst assert touch ' + 'soft_touch provide no_dep free irp_if irp_else irp_endif irp_write irp_read',\n    built_in: 'alog alog10 amax0 amax1 amin0 amin1 amod cabs ccos cexp clog csin csqrt dabs dacos dasin datan datan2 dcos dcosh ddim dexp dint ' + 'dlog dlog10 dmax1 dmin1 dmod dnint dsign dsin dsinh dsqrt dtan dtanh float iabs idim idint idnint ifix isign max0 max1 min0 min1 sngl ' + 'algama cdabs cdcos cdexp cdlog cdsin cdsqrt cqabs cqcos cqexp cqlog cqsin cqsqrt dcmplx dconjg derf derfc dfloat dgamma dimag dlgama ' + 'iqint qabs qacos qasin qatan qatan2 qcmplx qconjg qcos qcosh qdim qerf qerfc qexp qgamma qimag qlgama qlog qlog10 qmax1 qmin1 qmod ' + 'qnint qsign qsin qsinh qsqrt qtan qtanh abs acos aimag aint anint asin atan atan2 char cmplx conjg cos cosh exp ichar index int log ' + 'log10 max min nint sign sin sinh sqrt tan tanh print write dim lge lgt lle llt mod nullify allocate deallocate ' + 'adjustl adjustr all allocated any associated bit_size btest ceiling count cshift date_and_time digits dot_product ' + 'eoshift epsilon exponent floor fraction huge iand ibclr ibits ibset ieor ior ishft ishftc lbound len_trim matmul ' + 'maxexponent maxloc maxval merge minexponent minloc minval modulo mvbits nearest pack present product ' + 'radix random_number random_seed range repeat reshape rrspacing scale scan selected_int_kind selected_real_kind ' + 'set_exponent shape size spacing spread sum system_clock tiny transpose trim ubound unpack verify achar iachar transfer ' + 'dble entry dprod cpu_time command_argument_count get_command get_command_argument get_environment_variable is_iostat_end ' + 'ieee_arithmetic ieee_support_underflow_control ieee_get_underflow_mode ieee_set_underflow_mode ' + 'is_iostat_eor move_alloc new_line selected_char_kind same_type_as extends_type_of ' + 'acosh asinh atanh bessel_j0 bessel_j1 bessel_jn bessel_y0 bessel_y1 bessel_yn erf erfc erfc_scaled gamma log_gamma hypot norm2 ' + 'atomic_define atomic_ref execute_command_line leadz trailz storage_size merge_bits ' + 'bge bgt ble blt dshiftl dshiftr findloc iall iany iparity image_index lcobound ucobound maskl maskr ' + 'num_images parity popcnt poppar shifta shiftl shiftr this_image ' +\n    // IRPF90 special built_ins\n    'IRP_ALIGN irp_here'\n  };\n  return {\n    name: 'IRPF90',\n    case_insensitive: true,\n    keywords: F_KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [hljs.inherit(hljs.APOS_STRING_MODE, {\n      className: 'string',\n      relevance: 0\n    }), hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      className: 'string',\n      relevance: 0\n    }), {\n      className: 'function',\n      beginKeywords: 'subroutine function program',\n      illegal: '[${=\\\\n]',\n      contains: [hljs.UNDERSCORE_TITLE_MODE, PARAMS]\n    }, hljs.COMMENT('!', '$', {\n      relevance: 0\n    }), hljs.COMMENT('begin_doc', 'end_doc', {\n      relevance: 10\n    }), NUMBER]\n  };\n}\nmodule.exports = irpf90;", "map": {"version": 3, "names": ["source", "re", "concat", "args", "joined", "map", "x", "join", "irpf90", "hljs", "PARAMS", "className", "begin", "end", "OPTIONAL_NUMBER_SUFFIX", "OPTIONAL_NUMBER_EXP", "NUMBER", "variants", "relevance", "F_KEYWORDS", "literal", "keyword", "built_in", "name", "case_insensitive", "keywords", "illegal", "contains", "inherit", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "beginKeywords", "UNDERSCORE_TITLE_MODE", "COMMENT", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/irpf90.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: IRPF90\nAuthor: <PERSON> <<EMAIL>>\nDescription: IRPF90 is an open-source Fortran code generator\nWebsite: http://irpf90.ups-tlse.fr\nCategory: scientific\n*/\n\n/** @type LanguageFn */\nfunction irpf90(hljs) {\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n\n  // regex in both fortran and irpf90 should match\n  const OPTIONAL_NUMBER_SUFFIX = /(_[a-z_\\d]+)?/;\n  const OPTIONAL_NUMBER_EXP = /([de][+-]?\\d+)?/;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      {\n        begin: concat(/\\b\\d+/, /\\.(\\d*)/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n      },\n      {\n        begin: concat(/\\b\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n      },\n      {\n        begin: concat(/\\.\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n      }\n    ],\n    relevance: 0\n  };\n\n  const F_KEYWORDS = {\n    literal: '.False. .True.',\n    keyword: 'kind do while private call intrinsic where elsewhere ' +\n      'type endtype endmodule endselect endinterface end enddo endif if forall endforall only contains default return stop then ' +\n      'public subroutine|10 function program .and. .or. .not. .le. .eq. .ge. .gt. .lt. ' +\n      'goto save else use module select case ' +\n      'access blank direct exist file fmt form formatted iostat name named nextrec number opened rec recl sequential status unformatted unit ' +\n      'continue format pause cycle exit ' +\n      'c_null_char c_alert c_backspace c_form_feed flush wait decimal round iomsg ' +\n      'synchronous nopass non_overridable pass protected volatile abstract extends import ' +\n      'non_intrinsic value deferred generic final enumerator class associate bind enum ' +\n      'c_int c_short c_long c_long_long c_signed_char c_size_t c_int8_t c_int16_t c_int32_t c_int64_t c_int_least8_t c_int_least16_t ' +\n      'c_int_least32_t c_int_least64_t c_int_fast8_t c_int_fast16_t c_int_fast32_t c_int_fast64_t c_intmax_t C_intptr_t c_float c_double ' +\n      'c_long_double c_float_complex c_double_complex c_long_double_complex c_bool c_char c_null_ptr c_null_funptr ' +\n      'c_new_line c_carriage_return c_horizontal_tab c_vertical_tab iso_c_binding c_loc c_funloc c_associated  c_f_pointer ' +\n      'c_ptr c_funptr iso_fortran_env character_storage_size error_unit file_storage_size input_unit iostat_end iostat_eor ' +\n      'numeric_storage_size output_unit c_f_procpointer ieee_arithmetic ieee_support_underflow_control ' +\n      'ieee_get_underflow_mode ieee_set_underflow_mode newunit contiguous recursive ' +\n      'pad position action delim readwrite eor advance nml interface procedure namelist include sequence elemental pure ' +\n      'integer real character complex logical dimension allocatable|10 parameter ' +\n      'external implicit|10 none double precision assign intent optional pointer ' +\n      'target in out common equivalence data ' +\n      // IRPF90 special keywords\n      'begin_provider &begin_provider end_provider begin_shell end_shell begin_template end_template subst assert touch ' +\n      'soft_touch provide no_dep free irp_if irp_else irp_endif irp_write irp_read',\n    built_in: 'alog alog10 amax0 amax1 amin0 amin1 amod cabs ccos cexp clog csin csqrt dabs dacos dasin datan datan2 dcos dcosh ddim dexp dint ' +\n      'dlog dlog10 dmax1 dmin1 dmod dnint dsign dsin dsinh dsqrt dtan dtanh float iabs idim idint idnint ifix isign max0 max1 min0 min1 sngl ' +\n      'algama cdabs cdcos cdexp cdlog cdsin cdsqrt cqabs cqcos cqexp cqlog cqsin cqsqrt dcmplx dconjg derf derfc dfloat dgamma dimag dlgama ' +\n      'iqint qabs qacos qasin qatan qatan2 qcmplx qconjg qcos qcosh qdim qerf qerfc qexp qgamma qimag qlgama qlog qlog10 qmax1 qmin1 qmod ' +\n      'qnint qsign qsin qsinh qsqrt qtan qtanh abs acos aimag aint anint asin atan atan2 char cmplx conjg cos cosh exp ichar index int log ' +\n      'log10 max min nint sign sin sinh sqrt tan tanh print write dim lge lgt lle llt mod nullify allocate deallocate ' +\n      'adjustl adjustr all allocated any associated bit_size btest ceiling count cshift date_and_time digits dot_product ' +\n      'eoshift epsilon exponent floor fraction huge iand ibclr ibits ibset ieor ior ishft ishftc lbound len_trim matmul ' +\n      'maxexponent maxloc maxval merge minexponent minloc minval modulo mvbits nearest pack present product ' +\n      'radix random_number random_seed range repeat reshape rrspacing scale scan selected_int_kind selected_real_kind ' +\n      'set_exponent shape size spacing spread sum system_clock tiny transpose trim ubound unpack verify achar iachar transfer ' +\n      'dble entry dprod cpu_time command_argument_count get_command get_command_argument get_environment_variable is_iostat_end ' +\n      'ieee_arithmetic ieee_support_underflow_control ieee_get_underflow_mode ieee_set_underflow_mode ' +\n      'is_iostat_eor move_alloc new_line selected_char_kind same_type_as extends_type_of ' +\n      'acosh asinh atanh bessel_j0 bessel_j1 bessel_jn bessel_y0 bessel_y1 bessel_yn erf erfc erfc_scaled gamma log_gamma hypot norm2 ' +\n      'atomic_define atomic_ref execute_command_line leadz trailz storage_size merge_bits ' +\n      'bge bgt ble blt dshiftl dshiftr findloc iall iany iparity image_index lcobound ucobound maskl maskr ' +\n      'num_images parity popcnt poppar shifta shiftl shiftr this_image ' +\n      // IRPF90 special built_ins\n      'IRP_ALIGN irp_here'\n  };\n  return {\n    name: 'IRPF90',\n    case_insensitive: true,\n    keywords: F_KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [\n      hljs.inherit(hljs.APOS_STRING_MODE, {\n        className: 'string',\n        relevance: 0\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        className: 'string',\n        relevance: 0\n      }),\n      {\n        className: 'function',\n        beginKeywords: 'subroutine function program',\n        illegal: '[${=\\\\n]',\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          PARAMS\n        ]\n      },\n      hljs.COMMENT('!', '$', {\n        relevance: 0\n      }),\n      hljs.COMMENT('begin_doc', 'end_doc', {\n        relevance: 10\n      }),\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = irpf90;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAAC,GAAGC,IAAI,EAAE;EACvB,MAAMC,MAAM,GAAGD,IAAI,CAACE,GAAG,CAAEC,CAAC,IAAKN,MAAM,CAACM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASI,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE;EACP,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAG,eAAe;EAC9C,MAAMC,mBAAmB,GAAG,iBAAiB;EAC7C,MAAMC,MAAM,GAAG;IACbL,SAAS,EAAE,QAAQ;IACnBM,QAAQ,EAAE,CACR;MACEL,KAAK,EAAEV,MAAM,CAAC,OAAO,EAAE,SAAS,EAAEa,mBAAmB,EAAED,sBAAsB;IAC/E,CAAC,EACD;MACEF,KAAK,EAAEV,MAAM,CAAC,OAAO,EAAEa,mBAAmB,EAAED,sBAAsB;IACpE,CAAC,EACD;MACEF,KAAK,EAAEV,MAAM,CAAC,OAAO,EAAEa,mBAAmB,EAAED,sBAAsB;IACpE,CAAC,CACF;IACDI,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,UAAU,GAAG;IACjBC,OAAO,EAAE,gBAAgB;IACzBC,OAAO,EAAE,uDAAuD,GAC9D,2HAA2H,GAC3H,kFAAkF,GAClF,wCAAwC,GACxC,wIAAwI,GACxI,mCAAmC,GACnC,6EAA6E,GAC7E,qFAAqF,GACrF,kFAAkF,GAClF,gIAAgI,GAChI,oIAAoI,GACpI,8GAA8G,GAC9G,sHAAsH,GACtH,sHAAsH,GACtH,kGAAkG,GAClG,+EAA+E,GAC/E,mHAAmH,GACnH,4EAA4E,GAC5E,4EAA4E,GAC5E,wCAAwC;IACxC;IACA,mHAAmH,GACnH,6EAA6E;IAC/EC,QAAQ,EAAE,kIAAkI,GAC1I,wIAAwI,GACxI,uIAAuI,GACvI,qIAAqI,GACrI,sIAAsI,GACtI,iHAAiH,GACjH,oHAAoH,GACpH,mHAAmH,GACnH,uGAAuG,GACvG,iHAAiH,GACjH,yHAAyH,GACzH,2HAA2H,GAC3H,iGAAiG,GACjG,oFAAoF,GACpF,iIAAiI,GACjI,qFAAqF,GACrF,sGAAsG,GACtG,kEAAkE;IAClE;IACA;EACJ,CAAC;EACD,OAAO;IACLC,IAAI,EAAE,QAAQ;IACdC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAEN,UAAU;IACpBO,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,CACRlB,IAAI,CAACmB,OAAO,CAACnB,IAAI,CAACoB,gBAAgB,EAAE;MAClClB,SAAS,EAAE,QAAQ;MACnBO,SAAS,EAAE;IACb,CAAC,CAAC,EACFT,IAAI,CAACmB,OAAO,CAACnB,IAAI,CAACqB,iBAAiB,EAAE;MACnCnB,SAAS,EAAE,QAAQ;MACnBO,SAAS,EAAE;IACb,CAAC,CAAC,EACF;MACEP,SAAS,EAAE,UAAU;MACrBoB,aAAa,EAAE,6BAA6B;MAC5CL,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE,CACRlB,IAAI,CAACuB,qBAAqB,EAC1BtB,MAAM;IAEV,CAAC,EACDD,IAAI,CAACwB,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;MACrBf,SAAS,EAAE;IACb,CAAC,CAAC,EACFT,IAAI,CAACwB,OAAO,CAAC,WAAW,EAAE,SAAS,EAAE;MACnCf,SAAS,EAAE;IACb,CAAC,CAAC,EACFF,MAAM;EAEV,CAAC;AACH;AAEAkB,MAAM,CAACC,OAAO,GAAG3B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}