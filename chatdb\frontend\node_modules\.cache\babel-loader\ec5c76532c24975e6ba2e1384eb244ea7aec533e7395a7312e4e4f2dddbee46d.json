{"ast": null, "code": "'use strict';\n\nmodule.exports = mizar;\nmizar.displayName = 'mizar';\nmizar.aliases = [];\nfunction mizar(Prism) {\n  Prism.languages.mizar = {\n    comment: /::.+/,\n    keyword: /@proof\\b|\\b(?:according|aggregate|all|and|antonym|are|as|associativity|assume|asymmetry|attr|be|begin|being|by|canceled|case|cases|clusters?|coherence|commutativity|compatibility|connectedness|consider|consistency|constructors|contradiction|correctness|def|deffunc|define|definitions?|defpred|do|does|end|environ|equals|ex|exactly|existence|for|from|func|given|hence|hereby|holds|idempotence|identity|iff?|implies|involutiveness|irreflexivity|is|it|let|means|mode|non|not|notations?|now|of|or|otherwise|over|per|pred|prefix|projectivity|proof|provided|qua|reconsider|redefine|reduce|reducibility|reflexivity|registrations?|requirements|reserve|sch|schemes?|section|selector|set|sethood|st|struct|such|suppose|symmetry|synonym|take|that|the|then|theorems?|thesis|thus|to|transitivity|uniqueness|vocabular(?:ies|y)|when|where|with|wrt)\\b/,\n    parameter: {\n      pattern: /\\$(?:10|\\d)/,\n      alias: 'variable'\n    },\n    variable: /\\b\\w+(?=:)/,\n    number: /(?:\\b|-)\\d+\\b/,\n    operator: /\\.\\.\\.|->|&|\\.?=/,\n    punctuation: /\\(#|#\\)|[,:;\\[\\](){}]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "mizar", "displayName", "aliases", "Prism", "languages", "comment", "keyword", "parameter", "pattern", "alias", "variable", "number", "operator", "punctuation"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/mizar.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = mizar\nmizar.displayName = 'mizar'\nmizar.aliases = []\nfunction mizar(Prism) {\n  Prism.languages.mizar = {\n    comment: /::.+/,\n    keyword:\n      /@proof\\b|\\b(?:according|aggregate|all|and|antonym|are|as|associativity|assume|asymmetry|attr|be|begin|being|by|canceled|case|cases|clusters?|coherence|commutativity|compatibility|connectedness|consider|consistency|constructors|contradiction|correctness|def|deffunc|define|definitions?|defpred|do|does|end|environ|equals|ex|exactly|existence|for|from|func|given|hence|hereby|holds|idempotence|identity|iff?|implies|involutiveness|irreflexivity|is|it|let|means|mode|non|not|notations?|now|of|or|otherwise|over|per|pred|prefix|projectivity|proof|provided|qua|reconsider|redefine|reduce|reducibility|reflexivity|registrations?|requirements|reserve|sch|schemes?|section|selector|set|sethood|st|struct|such|suppose|symmetry|synonym|take|that|the|then|theorems?|thesis|thus|to|transitivity|uniqueness|vocabular(?:ies|y)|when|where|with|wrt)\\b/,\n    parameter: {\n      pattern: /\\$(?:10|\\d)/,\n      alias: 'variable'\n    },\n    variable: /\\b\\w+(?=:)/,\n    number: /(?:\\b|-)\\d+\\b/,\n    operator: /\\.\\.\\.|->|&|\\.?=/,\n    punctuation: /\\(#|#\\)|[,:;\\[\\](){}]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE,MAAM;IACfC,OAAO,EACL,q0BAAq0B;IACv0BC,SAAS,EAAE;MACTC,OAAO,EAAE,aAAa;MACtBC,KAAK,EAAE;IACT,CAAC;IACDC,QAAQ,EAAE,YAAY;IACtBC,MAAM,EAAE,eAAe;IACvBC,QAAQ,EAAE,kBAAkB;IAC5BC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}