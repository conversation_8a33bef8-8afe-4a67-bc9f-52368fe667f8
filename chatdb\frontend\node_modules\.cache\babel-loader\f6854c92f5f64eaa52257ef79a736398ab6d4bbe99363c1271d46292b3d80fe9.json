{"ast": null, "code": "import CalendarLocale from \"rc-picker/es/locale/en_US\";\nimport TimePickerLocale from '../../time-picker/locale/en_US';\n// Merge into a locale object\nconst locale = {\n  lang: Object.assign({\n    placeholder: 'Select date',\n    yearPlaceholder: 'Select year',\n    quarterPlaceholder: 'Select quarter',\n    monthPlaceholder: 'Select month',\n    weekPlaceholder: 'Select week',\n    rangePlaceholder: ['Start date', 'End date'],\n    rangeYearPlaceholder: ['Start year', 'End year'],\n    rangeQuarterPlaceholder: ['Start quarter', 'End quarter'],\n    rangeMonthPlaceholder: ['Start month', 'End month'],\n    rangeWeekPlaceholder: ['Start week', 'End week']\n  }, CalendarLocale),\n  timePickerLocale: Object.assign({}, TimePickerLocale)\n};\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nexport default locale;", "map": {"version": 3, "names": ["CalendarLocale", "TimePickerLocale", "locale", "lang", "Object", "assign", "placeholder", "yearPlaceholder", "quarterPlaceholder", "monthPlaceholder", "weekPlaceholder", "rangePlaceholder", "rangeYearPlaceholder", "rangeQuarterPlaceholder", "rangeMonthPlaceholder", "rangeWeekPlaceholder", "timePickerLocale"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/date-picker/locale/en_US.js"], "sourcesContent": ["import CalendarLocale from \"rc-picker/es/locale/en_US\";\nimport TimePickerLocale from '../../time-picker/locale/en_US';\n// Merge into a locale object\nconst locale = {\n  lang: Object.assign({\n    placeholder: 'Select date',\n    yearPlaceholder: 'Select year',\n    quarterPlaceholder: 'Select quarter',\n    monthPlaceholder: 'Select month',\n    weekPlaceholder: 'Select week',\n    rangePlaceholder: ['Start date', 'End date'],\n    rangeYearPlaceholder: ['Start year', 'End year'],\n    rangeQuarterPlaceholder: ['Start quarter', 'End quarter'],\n    rangeMonthPlaceholder: ['Start month', 'End month'],\n    rangeWeekPlaceholder: ['Start week', 'End week']\n  }, CalendarLocale),\n  timePickerLocale: Object.assign({}, TimePickerLocale)\n};\n// All settings at:\n// https://github.com/ant-design/ant-design/blob/master/components/date-picker/locale/example.json\nexport default locale;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D;AACA,MAAMC,MAAM,GAAG;EACbC,IAAI,EAAEC,MAAM,CAACC,MAAM,CAAC;IAClBC,WAAW,EAAE,aAAa;IAC1BC,eAAe,EAAE,aAAa;IAC9BC,kBAAkB,EAAE,gBAAgB;IACpCC,gBAAgB,EAAE,cAAc;IAChCC,eAAe,EAAE,aAAa;IAC9BC,gBAAgB,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;IAC5CC,oBAAoB,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;IAChDC,uBAAuB,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;IACzDC,qBAAqB,EAAE,CAAC,aAAa,EAAE,WAAW,CAAC;IACnDC,oBAAoB,EAAE,CAAC,YAAY,EAAE,UAAU;EACjD,CAAC,EAAEf,cAAc,CAAC;EAClBgB,gBAAgB,EAAEZ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,gBAAgB;AACtD,CAAC;AACD;AACA;AACA,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}