{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = smarty;\nsmarty.displayName = 'smarty';\nsmarty.aliases = [];\nfunction smarty(Prism) {\n  Prism.register(refractorMarkupTemplating);\n  (function (Prism) {\n    Prism.languages.smarty = {\n      comment: {\n        pattern: /^\\{\\*[\\s\\S]*?\\*\\}/,\n        greedy: true\n      },\n      'embedded-php': {\n        pattern: /^\\{php\\}[\\s\\S]*?\\{\\/php\\}/,\n        greedy: true,\n        inside: {\n          smarty: {\n            pattern: /^\\{php\\}|\\{\\/php\\}$/,\n            inside: null // see below\n          },\n          php: {\n            pattern: /[\\s\\S]+/,\n            alias: 'language-php',\n            inside: Prism.languages.php\n          }\n        }\n      },\n      string: [{\n        pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /\\{[^{}]*\\}|`[^`]*`/,\n            inside: {\n              'interpolation-punctuation': {\n                pattern: /^[{`]|[`}]$/,\n                alias: 'punctuation'\n              },\n              expression: {\n                pattern: /[\\s\\S]+/,\n                inside: null // see below\n              }\n            }\n          },\n          variable: /\\$\\w+/\n        }\n      }, {\n        pattern: /'(?:\\\\.|[^'\\\\\\r\\n])*'/,\n        greedy: true\n      }],\n      keyword: {\n        pattern: /(^\\{\\/?)[a-z_]\\w*\\b(?!\\()/i,\n        lookbehind: true,\n        greedy: true\n      },\n      delimiter: {\n        pattern: /^\\{\\/?|\\}$/,\n        greedy: true,\n        alias: 'punctuation'\n      },\n      number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n      variable: [/\\$(?!\\d)\\w+/, /#(?!\\d)\\w+#/, {\n        pattern: /(\\.|->|\\w\\s*=)(?!\\d)\\w+\\b(?!\\()/,\n        lookbehind: true\n      }, {\n        pattern: /(\\[)(?!\\d)\\w+(?=\\])/,\n        lookbehind: true\n      }],\n      function: {\n        pattern: /(\\|\\s*)@?[a-z_]\\w*|\\b[a-z_]\\w*(?=\\()/i,\n        lookbehind: true\n      },\n      'attr-name': /\\b[a-z_]\\w*(?=\\s*=)/i,\n      boolean: /\\b(?:false|no|off|on|true|yes)\\b/,\n      punctuation: /[\\[\\](){}.,:`]|->/,\n      operator: [/[+\\-*\\/%]|==?=?|[!<>]=?|&&|\\|\\|?/, /\\bis\\s+(?:not\\s+)?(?:div|even|odd)(?:\\s+by)?\\b/, /\\b(?:and|eq|gt?e|gt|lt?e|lt|mod|neq?|not|or)\\b/]\n    };\n    Prism.languages.smarty['embedded-php'].inside.smarty.inside = Prism.languages.smarty;\n    Prism.languages.smarty.string[0].inside.interpolation.inside.expression.inside = Prism.languages.smarty;\n    var string = /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|'(?:\\\\.|[^'\\\\\\r\\n])*'/;\n    var smartyPattern = RegExp(\n    // comments\n    /\\{\\*[\\s\\S]*?\\*\\}/.source + '|' +\n    // php tags\n    /\\{php\\}[\\s\\S]*?\\{\\/php\\}/.source + '|' +\n    // smarty blocks\n    /\\{(?:[^{}\"']|<str>|\\{(?:[^{}\"']|<str>|\\{(?:[^{}\"']|<str>)*\\})*\\})*\\}/.source.replace(/<str>/g, function () {\n      return string.source;\n    }), 'g'); // Tokenize all inline Smarty expressions\n    Prism.hooks.add('before-tokenize', function (env) {\n      var smartyLiteralStart = '{literal}';\n      var smartyLiteralEnd = '{/literal}';\n      var smartyLiteralMode = false;\n      Prism.languages['markup-templating'].buildPlaceholders(env, 'smarty', smartyPattern, function (match) {\n        // Smarty tags inside {literal} block are ignored\n        if (match === smartyLiteralEnd) {\n          smartyLiteralMode = false;\n        }\n        if (!smartyLiteralMode) {\n          if (match === smartyLiteralStart) {\n            smartyLiteralMode = true;\n          }\n          return true;\n        }\n        return false;\n      });\n    }); // Re-insert the tokens after tokenizing\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'smarty');\n    });\n  })(Prism);\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "smarty", "displayName", "aliases", "Prism", "register", "languages", "comment", "pattern", "greedy", "inside", "php", "alias", "string", "interpolation", "expression", "variable", "keyword", "lookbehind", "delimiter", "number", "function", "boolean", "punctuation", "operator", "smartyPattern", "RegExp", "source", "replace", "hooks", "add", "env", "smartyLiteralStart", "smartyLiteralEnd", "smartyLiteralMode", "buildPlaceholders", "match", "tokenizePlaceholders"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/smarty.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = smarty\nsmarty.displayName = 'smarty'\nsmarty.aliases = []\nfunction smarty(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.smarty = {\n      comment: {\n        pattern: /^\\{\\*[\\s\\S]*?\\*\\}/,\n        greedy: true\n      },\n      'embedded-php': {\n        pattern: /^\\{php\\}[\\s\\S]*?\\{\\/php\\}/,\n        greedy: true,\n        inside: {\n          smarty: {\n            pattern: /^\\{php\\}|\\{\\/php\\}$/,\n            inside: null // see below\n          },\n          php: {\n            pattern: /[\\s\\S]+/,\n            alias: 'language-php',\n            inside: Prism.languages.php\n          }\n        }\n      },\n      string: [\n        {\n          pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n          greedy: true,\n          inside: {\n            interpolation: {\n              pattern: /\\{[^{}]*\\}|`[^`]*`/,\n              inside: {\n                'interpolation-punctuation': {\n                  pattern: /^[{`]|[`}]$/,\n                  alias: 'punctuation'\n                },\n                expression: {\n                  pattern: /[\\s\\S]+/,\n                  inside: null // see below\n                }\n              }\n            },\n            variable: /\\$\\w+/\n          }\n        },\n        {\n          pattern: /'(?:\\\\.|[^'\\\\\\r\\n])*'/,\n          greedy: true\n        }\n      ],\n      keyword: {\n        pattern: /(^\\{\\/?)[a-z_]\\w*\\b(?!\\()/i,\n        lookbehind: true,\n        greedy: true\n      },\n      delimiter: {\n        pattern: /^\\{\\/?|\\}$/,\n        greedy: true,\n        alias: 'punctuation'\n      },\n      number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n      variable: [\n        /\\$(?!\\d)\\w+/,\n        /#(?!\\d)\\w+#/,\n        {\n          pattern: /(\\.|->|\\w\\s*=)(?!\\d)\\w+\\b(?!\\()/,\n          lookbehind: true\n        },\n        {\n          pattern: /(\\[)(?!\\d)\\w+(?=\\])/,\n          lookbehind: true\n        }\n      ],\n      function: {\n        pattern: /(\\|\\s*)@?[a-z_]\\w*|\\b[a-z_]\\w*(?=\\()/i,\n        lookbehind: true\n      },\n      'attr-name': /\\b[a-z_]\\w*(?=\\s*=)/i,\n      boolean: /\\b(?:false|no|off|on|true|yes)\\b/,\n      punctuation: /[\\[\\](){}.,:`]|->/,\n      operator: [\n        /[+\\-*\\/%]|==?=?|[!<>]=?|&&|\\|\\|?/,\n        /\\bis\\s+(?:not\\s+)?(?:div|even|odd)(?:\\s+by)?\\b/,\n        /\\b(?:and|eq|gt?e|gt|lt?e|lt|mod|neq?|not|or)\\b/\n      ]\n    }\n    Prism.languages.smarty['embedded-php'].inside.smarty.inside =\n      Prism.languages.smarty\n    Prism.languages.smarty.string[0].inside.interpolation.inside.expression.inside =\n      Prism.languages.smarty\n    var string = /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|'(?:\\\\.|[^'\\\\\\r\\n])*'/\n    var smartyPattern = RegExp(\n      // comments\n      /\\{\\*[\\s\\S]*?\\*\\}/.source +\n        '|' + // php tags\n        /\\{php\\}[\\s\\S]*?\\{\\/php\\}/.source +\n        '|' + // smarty blocks\n        /\\{(?:[^{}\"']|<str>|\\{(?:[^{}\"']|<str>|\\{(?:[^{}\"']|<str>)*\\})*\\})*\\}/.source.replace(\n          /<str>/g,\n          function () {\n            return string.source\n          }\n        ),\n      'g'\n    ) // Tokenize all inline Smarty expressions\n    Prism.hooks.add('before-tokenize', function (env) {\n      var smartyLiteralStart = '{literal}'\n      var smartyLiteralEnd = '{/literal}'\n      var smartyLiteralMode = false\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'smarty',\n        smartyPattern,\n        function (match) {\n          // Smarty tags inside {literal} block are ignored\n          if (match === smartyLiteralEnd) {\n            smartyLiteralMode = false\n          }\n          if (!smartyLiteralMode) {\n            if (match === smartyLiteralStart) {\n              smartyLiteralMode = true\n            }\n            return true\n          }\n          return false\n        }\n      )\n    }) // Re-insert the tokens after tokenizing\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'smarty')\n    })\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB,CAAC;EACxC,CAAC,UAAUO,KAAK,EAAE;IACjBA,KAAK,CAACE,SAAS,CAACL,MAAM,GAAG;MACvBM,OAAO,EAAE;QACPC,OAAO,EAAE,mBAAmB;QAC5BC,MAAM,EAAE;MACV,CAAC;MACD,cAAc,EAAE;QACdD,OAAO,EAAE,2BAA2B;QACpCC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNT,MAAM,EAAE;YACNO,OAAO,EAAE,qBAAqB;YAC9BE,MAAM,EAAE,IAAI,CAAC;UACf,CAAC;UACDC,GAAG,EAAE;YACHH,OAAO,EAAE,SAAS;YAClBI,KAAK,EAAE,cAAc;YACrBF,MAAM,EAAEN,KAAK,CAACE,SAAS,CAACK;UAC1B;QACF;MACF,CAAC;MACDE,MAAM,EAAE,CACN;QACEL,OAAO,EAAE,uBAAuB;QAChCC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;UACNI,aAAa,EAAE;YACbN,OAAO,EAAE,oBAAoB;YAC7BE,MAAM,EAAE;cACN,2BAA2B,EAAE;gBAC3BF,OAAO,EAAE,aAAa;gBACtBI,KAAK,EAAE;cACT,CAAC;cACDG,UAAU,EAAE;gBACVP,OAAO,EAAE,SAAS;gBAClBE,MAAM,EAAE,IAAI,CAAC;cACf;YACF;UACF,CAAC;UACDM,QAAQ,EAAE;QACZ;MACF,CAAC,EACD;QACER,OAAO,EAAE,uBAAuB;QAChCC,MAAM,EAAE;MACV,CAAC,CACF;MACDQ,OAAO,EAAE;QACPT,OAAO,EAAE,4BAA4B;QACrCU,UAAU,EAAE,IAAI;QAChBT,MAAM,EAAE;MACV,CAAC;MACDU,SAAS,EAAE;QACTX,OAAO,EAAE,YAAY;QACrBC,MAAM,EAAE,IAAI;QACZG,KAAK,EAAE;MACT,CAAC;MACDQ,MAAM,EAAE,8DAA8D;MACtEJ,QAAQ,EAAE,CACR,aAAa,EACb,aAAa,EACb;QACER,OAAO,EAAE,iCAAiC;QAC1CU,UAAU,EAAE;MACd,CAAC,EACD;QACEV,OAAO,EAAE,qBAAqB;QAC9BU,UAAU,EAAE;MACd,CAAC,CACF;MACDG,QAAQ,EAAE;QACRb,OAAO,EAAE,uCAAuC;QAChDU,UAAU,EAAE;MACd,CAAC;MACD,WAAW,EAAE,sBAAsB;MACnCI,OAAO,EAAE,kCAAkC;MAC3CC,WAAW,EAAE,mBAAmB;MAChCC,QAAQ,EAAE,CACR,kCAAkC,EAClC,gDAAgD,EAChD,gDAAgD;IAEpD,CAAC;IACDpB,KAAK,CAACE,SAAS,CAACL,MAAM,CAAC,cAAc,CAAC,CAACS,MAAM,CAACT,MAAM,CAACS,MAAM,GACzDN,KAAK,CAACE,SAAS,CAACL,MAAM;IACxBG,KAAK,CAACE,SAAS,CAACL,MAAM,CAACY,MAAM,CAAC,CAAC,CAAC,CAACH,MAAM,CAACI,aAAa,CAACJ,MAAM,CAACK,UAAU,CAACL,MAAM,GAC5EN,KAAK,CAACE,SAAS,CAACL,MAAM;IACxB,IAAIY,MAAM,GAAG,6CAA6C;IAC1D,IAAIY,aAAa,GAAGC,MAAM;IACxB;IACA,kBAAkB,CAACC,MAAM,GACvB,GAAG;IAAG;IACN,0BAA0B,CAACA,MAAM,GACjC,GAAG;IAAG;IACN,sEAAsE,CAACA,MAAM,CAACC,OAAO,CACnF,QAAQ,EACR,YAAY;MACV,OAAOf,MAAM,CAACc,MAAM;IACtB,CACF,CAAC,EACH,GACF,CAAC,EAAC;IACFvB,KAAK,CAACyB,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;MAChD,IAAIC,kBAAkB,GAAG,WAAW;MACpC,IAAIC,gBAAgB,GAAG,YAAY;MACnC,IAAIC,iBAAiB,GAAG,KAAK;MAC7B9B,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAAC6B,iBAAiB,CACpDJ,GAAG,EACH,QAAQ,EACRN,aAAa,EACb,UAAUW,KAAK,EAAE;QACf;QACA,IAAIA,KAAK,KAAKH,gBAAgB,EAAE;UAC9BC,iBAAiB,GAAG,KAAK;QAC3B;QACA,IAAI,CAACA,iBAAiB,EAAE;UACtB,IAAIE,KAAK,KAAKJ,kBAAkB,EAAE;YAChCE,iBAAiB,GAAG,IAAI;UAC1B;UACA,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CACF,CAAC;IACH,CAAC,CAAC,EAAC;IACH9B,KAAK,CAACyB,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;MAC/C3B,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAAC+B,oBAAoB,CAACN,GAAG,EAAE,QAAQ,CAAC;IAC1E,CAAC,CAAC;EACJ,CAAC,EAAE3B,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}