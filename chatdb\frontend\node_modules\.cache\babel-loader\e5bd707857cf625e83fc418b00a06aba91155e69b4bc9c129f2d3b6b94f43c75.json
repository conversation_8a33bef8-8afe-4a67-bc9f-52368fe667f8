{"ast": null, "code": "'use strict';\n\nmodule.exports = openqasm;\nopenqasm.displayName = 'openqasm';\nopenqasm.aliases = ['qasm'];\nfunction openqasm(Prism) {\n  // https://qiskit.github.io/openqasm/grammar/index.html\n  Prism.languages.openqasm = {\n    comment: /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*/,\n    string: {\n      pattern: /\"[^\"\\r\\n\\t]*\"|'[^'\\r\\n\\t]*'/,\n      greedy: true\n    },\n    keyword: /\\b(?:CX|OPENQASM|U|barrier|boxas|boxto|break|const|continue|ctrl|def|defcal|defcalgrammar|delay|else|end|for|gate|gphase|if|in|include|inv|kernel|lengthof|let|measure|pow|reset|return|rotary|stretchinf|while)\\b|#pragma\\b/,\n    'class-name': /\\b(?:angle|bit|bool|creg|fixed|float|int|length|qreg|qubit|stretch|uint)\\b/,\n    function: /\\b(?:cos|exp|ln|popcount|rotl|rotr|sin|sqrt|tan)\\b(?=\\s*\\()/,\n    constant: /\\b(?:euler|pi|tau)\\b|π|𝜏|ℇ/,\n    number: {\n      pattern: /(^|[^.\\w$])(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?(?:dt|ns|us|µs|ms|s)?/i,\n      lookbehind: true\n    },\n    operator: /->|>>=?|<<=?|&&|\\|\\||\\+\\+|--|[!=<>&|~^+\\-*/%]=?|@/,\n    punctuation: /[(){}\\[\\];,:.]/\n  };\n  Prism.languages.qasm = Prism.languages.openqasm;\n}", "map": {"version": 3, "names": ["module", "exports", "openqasm", "displayName", "aliases", "Prism", "languages", "comment", "string", "pattern", "greedy", "keyword", "function", "constant", "number", "lookbehind", "operator", "punctuation", "qasm"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/openqasm.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = openqasm\nopenqasm.displayName = 'openqasm'\nopenqasm.aliases = ['qasm']\nfunction openqasm(Prism) {\n  // https://qiskit.github.io/openqasm/grammar/index.html\n  Prism.languages.openqasm = {\n    comment: /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*/,\n    string: {\n      pattern: /\"[^\"\\r\\n\\t]*\"|'[^'\\r\\n\\t]*'/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:CX|OPENQASM|U|barrier|boxas|boxto|break|const|continue|ctrl|def|defcal|defcalgrammar|delay|else|end|for|gate|gphase|if|in|include|inv|kernel|lengthof|let|measure|pow|reset|return|rotary|stretchinf|while)\\b|#pragma\\b/,\n    'class-name':\n      /\\b(?:angle|bit|bool|creg|fixed|float|int|length|qreg|qubit|stretch|uint)\\b/,\n    function: /\\b(?:cos|exp|ln|popcount|rotl|rotr|sin|sqrt|tan)\\b(?=\\s*\\()/,\n    constant: /\\b(?:euler|pi|tau)\\b|π|𝜏|ℇ/,\n    number: {\n      pattern:\n        /(^|[^.\\w$])(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?(?:dt|ns|us|µs|ms|s)?/i,\n      lookbehind: true\n    },\n    operator: /->|>>=?|<<=?|&&|\\|\\||\\+\\+|--|[!=<>&|~^+\\-*/%]=?|@/,\n    punctuation: /[(){}\\[\\];,:.]/\n  }\n  Prism.languages.qasm = Prism.languages.openqasm\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,CAAC,MAAM,CAAC;AAC3B,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvB;EACAA,KAAK,CAACC,SAAS,CAACJ,QAAQ,GAAG;IACzBK,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE;MACNC,OAAO,EAAE,6BAA6B;MACtCC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EACL,8NAA8N;IAChO,YAAY,EACV,4EAA4E;IAC9EC,QAAQ,EAAE,6DAA6D;IACvEC,QAAQ,EAAE,6BAA6B;IACvCC,MAAM,EAAE;MACNL,OAAO,EACL,wEAAwE;MAC1EM,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE,mDAAmD;IAC7DC,WAAW,EAAE;EACf,CAAC;EACDZ,KAAK,CAACC,SAAS,CAACY,IAAI,GAAGb,KAAK,CAACC,SAAS,CAACJ,QAAQ;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}