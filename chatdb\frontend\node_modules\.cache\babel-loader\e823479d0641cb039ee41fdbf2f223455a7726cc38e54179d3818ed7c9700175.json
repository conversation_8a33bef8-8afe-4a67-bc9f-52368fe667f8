{"ast": null, "code": "import classNames from 'classnames';\nimport React from 'react';\nvar ColorBlock = function ColorBlock(_ref) {\n  var color = _ref.color,\n    prefixCls = _ref.prefixCls,\n    className = _ref.className,\n    style = _ref.style,\n    onClick = _ref.onClick;\n  var colorBlockCls = \"\".concat(prefixCls, \"-color-block\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(colorBlockCls, className),\n    style: style,\n    onClick: onClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(colorBlockCls, \"-inner\"),\n    style: {\n      background: color\n    }\n  }));\n};\nexport default ColorBlock;", "map": {"version": 3, "names": ["classNames", "React", "ColorBlock", "_ref", "color", "prefixCls", "className", "style", "onClick", "colorBlockCls", "concat", "createElement", "background"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@rc-component/color-picker/es/components/ColorBlock.js"], "sourcesContent": ["import classNames from 'classnames';\nimport React from 'react';\nvar ColorBlock = function ColorBlock(_ref) {\n  var color = _ref.color,\n    prefixCls = _ref.prefixCls,\n    className = _ref.className,\n    style = _ref.style,\n    onClick = _ref.onClick;\n  var colorBlockCls = \"\".concat(prefixCls, \"-color-block\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(colorBlockCls, className),\n    style: style,\n    onClick: onClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(colorBlockCls, \"-inner\"),\n    style: {\n      background: color\n    }\n  }));\n};\nexport default ColorBlock;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,IAAI,EAAE;EACzC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,KAAK,GAAGJ,IAAI,CAACI,KAAK;IAClBC,OAAO,GAAGL,IAAI,CAACK,OAAO;EACxB,IAAIC,aAAa,GAAG,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,cAAc,CAAC;EACxD,OAAO,aAAaJ,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IAC7CL,SAAS,EAAEN,UAAU,CAACS,aAAa,EAAEH,SAAS,CAAC;IAC/CC,KAAK,EAAEA,KAAK;IACZC,OAAO,EAAEA;EACX,CAAC,EAAE,aAAaP,KAAK,CAACU,aAAa,CAAC,KAAK,EAAE;IACzCL,SAAS,EAAE,EAAE,CAACI,MAAM,CAACD,aAAa,EAAE,QAAQ,CAAC;IAC7CF,KAAK,EAAE;MACLK,UAAU,EAAER;IACd;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}