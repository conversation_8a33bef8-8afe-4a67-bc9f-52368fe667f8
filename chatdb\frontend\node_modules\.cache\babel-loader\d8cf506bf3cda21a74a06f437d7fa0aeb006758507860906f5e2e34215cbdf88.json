{"ast": null, "code": "'use strict';\n\nmodule.exports = maxscript;\nmaxscript.displayName = 'maxscript';\nmaxscript.aliases = [];\nfunction maxscript(Prism) {\n  ;\n  (function (Prism) {\n    var keywords = /\\b(?:about|and|animate|as|at|attributes|by|case|catch|collect|continue|coordsys|do|else|exit|fn|for|from|function|global|if|in|local|macroscript|mapped|max|not|of|off|on|or|parameters|persistent|plugin|rcmenu|return|rollout|set|struct|then|throw|to|tool|try|undo|utility|when|where|while|with)\\b/i;\n    Prism.languages.maxscript = {\n      comment: {\n        pattern: /\\/\\*[\\s\\S]*?(?:\\*\\/|$)|--.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /(^|[^\"\\\\@])(?:\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"|@\"[^\"]*\")/,\n        lookbehind: true,\n        greedy: true\n      },\n      path: {\n        pattern: /\\$(?:[\\w/\\\\.*?]|'[^']*')*/,\n        greedy: true,\n        alias: 'string'\n      },\n      'function-call': {\n        pattern: RegExp('((?:' + (\n        // start of line\n        /^/.source + '|' +\n        // operators and other language constructs\n        /[;=<>+\\-*/^({\\[]/.source + '|' +\n        // keywords as part of statements\n        /\\b(?:and|by|case|catch|collect|do|else|if|in|not|or|return|then|to|try|where|while|with)\\b/.source) + ')[ \\t]*)' + '(?!' + keywords.source + ')' + /[a-z_]\\w*\\b/.source + '(?=[ \\t]*(?:' + (\n        // variable\n        '(?!' + keywords.source + ')' + /[a-z_]/.source + '|' +\n        // number\n        /\\d|-\\.?\\d/.source + '|' +\n        // other expressions or literals\n        /[({'\"$@#?]/.source) + '))', 'im'),\n        lookbehind: true,\n        greedy: true,\n        alias: 'function'\n      },\n      'function-definition': {\n        pattern: /(\\b(?:fn|function)\\s+)\\w+\\b/i,\n        lookbehind: true,\n        alias: 'function'\n      },\n      argument: {\n        pattern: /\\b[a-z_]\\w*(?=:)/i,\n        alias: 'attr-name'\n      },\n      keyword: keywords,\n      boolean: /\\b(?:false|true)\\b/,\n      time: {\n        pattern: /(^|[^\\w.])(?:(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eEdD][+-]\\d+|[LP])?[msft])+|\\d+:\\d+(?:\\.\\d*)?)(?![\\w.:])/,\n        lookbehind: true,\n        alias: 'number'\n      },\n      number: [{\n        pattern: /(^|[^\\w.])(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eEdD][+-]\\d+|[LP])?|0x[a-fA-F0-9]+)(?![\\w.:])/,\n        lookbehind: true\n      }, /\\b(?:e|pi)\\b/],\n      constant: /\\b(?:dontcollect|ok|silentValue|undefined|unsupplied)\\b/,\n      color: {\n        pattern: /\\b(?:black|blue|brown|gray|green|orange|red|white|yellow)\\b/i,\n        alias: 'constant'\n      },\n      operator: /[-+*/<>=!]=?|[&^?]|#(?!\\()/,\n      punctuation: /[()\\[\\]{}.:,;]|#(?=\\()|\\\\$/m\n    };\n  })(Prism);\n}", "map": {"version": 3, "names": ["module", "exports", "maxscript", "displayName", "aliases", "Prism", "keywords", "languages", "comment", "pattern", "greedy", "string", "lookbehind", "path", "alias", "RegExp", "source", "argument", "keyword", "boolean", "time", "number", "constant", "color", "operator", "punctuation"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/maxscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = maxscript\nmaxscript.displayName = 'maxscript'\nmaxscript.aliases = []\nfunction maxscript(Prism) {\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:about|and|animate|as|at|attributes|by|case|catch|collect|continue|coordsys|do|else|exit|fn|for|from|function|global|if|in|local|macroscript|mapped|max|not|of|off|on|or|parameters|persistent|plugin|rcmenu|return|rollout|set|struct|then|throw|to|tool|try|undo|utility|when|where|while|with)\\b/i\n    Prism.languages.maxscript = {\n      comment: {\n        pattern: /\\/\\*[\\s\\S]*?(?:\\*\\/|$)|--.*/,\n        greedy: true\n      },\n      string: {\n        pattern: /(^|[^\"\\\\@])(?:\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"|@\"[^\"]*\")/,\n        lookbehind: true,\n        greedy: true\n      },\n      path: {\n        pattern: /\\$(?:[\\w/\\\\.*?]|'[^']*')*/,\n        greedy: true,\n        alias: 'string'\n      },\n      'function-call': {\n        pattern: RegExp(\n          '((?:' + // start of line\n            (/^/.source +\n              '|' + // operators and other language constructs\n              /[;=<>+\\-*/^({\\[]/.source +\n              '|' + // keywords as part of statements\n              /\\b(?:and|by|case|catch|collect|do|else|if|in|not|or|return|then|to|try|where|while|with)\\b/\n                .source) +\n            ')[ \\t]*)' +\n            '(?!' +\n            keywords.source +\n            ')' +\n            /[a-z_]\\w*\\b/.source +\n            '(?=[ \\t]*(?:' + // variable\n            ('(?!' +\n              keywords.source +\n              ')' +\n              /[a-z_]/.source +\n              '|' + // number\n              /\\d|-\\.?\\d/.source +\n              '|' + // other expressions or literals\n              /[({'\"$@#?]/.source) +\n            '))',\n          'im'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'function'\n      },\n      'function-definition': {\n        pattern: /(\\b(?:fn|function)\\s+)\\w+\\b/i,\n        lookbehind: true,\n        alias: 'function'\n      },\n      argument: {\n        pattern: /\\b[a-z_]\\w*(?=:)/i,\n        alias: 'attr-name'\n      },\n      keyword: keywords,\n      boolean: /\\b(?:false|true)\\b/,\n      time: {\n        pattern:\n          /(^|[^\\w.])(?:(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eEdD][+-]\\d+|[LP])?[msft])+|\\d+:\\d+(?:\\.\\d*)?)(?![\\w.:])/,\n        lookbehind: true,\n        alias: 'number'\n      },\n      number: [\n        {\n          pattern:\n            /(^|[^\\w.])(?:(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eEdD][+-]\\d+|[LP])?|0x[a-fA-F0-9]+)(?![\\w.:])/,\n          lookbehind: true\n        },\n        /\\b(?:e|pi)\\b/\n      ],\n      constant: /\\b(?:dontcollect|ok|silentValue|undefined|unsupplied)\\b/,\n      color: {\n        pattern: /\\b(?:black|blue|brown|gray|green|orange|red|white|yellow)\\b/i,\n        alias: 'constant'\n      },\n      operator: /[-+*/<>=!]=?|[&^?]|#(?!\\()/,\n      punctuation: /[()\\[\\]{}.:,;]|#(?=\\()|\\\\$/m\n    }\n  })(Prism)\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,SAAS;AAC1BA,SAAS,CAACC,WAAW,GAAG,WAAW;AACnCD,SAAS,CAACE,OAAO,GAAG,EAAE;AACtB,SAASF,SAASA,CAACG,KAAK,EAAE;EACxB;EAAC,CAAC,UAAUA,KAAK,EAAE;IACjB,IAAIC,QAAQ,GACV,0SAA0S;IAC5SD,KAAK,CAACE,SAAS,CAACL,SAAS,GAAG;MAC1BM,OAAO,EAAE;QACPC,OAAO,EAAE,6BAA6B;QACtCC,MAAM,EAAE;MACV,CAAC;MACDC,MAAM,EAAE;QACNF,OAAO,EAAE,gDAAgD;QACzDG,UAAU,EAAE,IAAI;QAChBF,MAAM,EAAE;MACV,CAAC;MACDG,IAAI,EAAE;QACJJ,OAAO,EAAE,2BAA2B;QACpCC,MAAM,EAAE,IAAI;QACZI,KAAK,EAAE;MACT,CAAC;MACD,eAAe,EAAE;QACfL,OAAO,EAAEM,MAAM,CACb,MAAM;QAAG;QACN,GAAG,CAACC,MAAM,GACT,GAAG;QAAG;QACN,kBAAkB,CAACA,MAAM,GACzB,GAAG;QAAG;QACN,4FAA4F,CACzFA,MAAM,CAAC,GACZ,UAAU,GACV,KAAK,GACLV,QAAQ,CAACU,MAAM,GACf,GAAG,GACH,aAAa,CAACA,MAAM,GACpB,cAAc;QAAG;QAChB,KAAK,GACJV,QAAQ,CAACU,MAAM,GACf,GAAG,GACH,QAAQ,CAACA,MAAM,GACf,GAAG;QAAG;QACN,WAAW,CAACA,MAAM,GAClB,GAAG;QAAG;QACN,YAAY,CAACA,MAAM,CAAC,GACtB,IAAI,EACN,IACF,CAAC;QACDJ,UAAU,EAAE,IAAI;QAChBF,MAAM,EAAE,IAAI;QACZI,KAAK,EAAE;MACT,CAAC;MACD,qBAAqB,EAAE;QACrBL,OAAO,EAAE,8BAA8B;QACvCG,UAAU,EAAE,IAAI;QAChBE,KAAK,EAAE;MACT,CAAC;MACDG,QAAQ,EAAE;QACRR,OAAO,EAAE,mBAAmB;QAC5BK,KAAK,EAAE;MACT,CAAC;MACDI,OAAO,EAAEZ,QAAQ;MACjBa,OAAO,EAAE,oBAAoB;MAC7BC,IAAI,EAAE;QACJX,OAAO,EACL,qGAAqG;QACvGG,UAAU,EAAE,IAAI;QAChBE,KAAK,EAAE;MACT,CAAC;MACDO,MAAM,EAAE,CACN;QACEZ,OAAO,EACL,uFAAuF;QACzFG,UAAU,EAAE;MACd,CAAC,EACD,cAAc,CACf;MACDU,QAAQ,EAAE,yDAAyD;MACnEC,KAAK,EAAE;QACLd,OAAO,EAAE,8DAA8D;QACvEK,KAAK,EAAE;MACT,CAAC;MACDU,QAAQ,EAAE,4BAA4B;MACtCC,WAAW,EAAE;IACf,CAAC;EACH,CAAC,EAAEpB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}