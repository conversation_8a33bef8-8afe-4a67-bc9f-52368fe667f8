# 智能数据分析系统项目清理备份脚本
# 在执行清理前创建完整项目备份

$BackupDate = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupDir = "project_backup_$BackupDate"
$ProjectRoot = Get-Location

Write-Host "开始创建项目备份..." -ForegroundColor Green
Write-Host "备份目录: $BackupDir" -ForegroundColor Yellow

# 创建备份目录
New-Item -ItemType Directory -Path $BackupDir -Force

# 备份重要文件和目录
Write-Host "备份主应用程序目录..." -ForegroundColor Cyan
Copy-Item -Path "chatdb" -Destination "$BackupDir\chatdb" -Recurse -Force

Write-Host "备份数据库文件..." -ForegroundColor Cyan
Copy-Item -Path "*.db" -Destination $BackupDir -Force

Write-Host "备份重要脚本和文档..." -ForegroundColor Cyan
$ImportantFiles = @(
    "financial_data_column_guide.md",
    "suggested_queries.sql",
    "financial_data_metadata_queries.sql"
)

foreach ($file in $ImportantFiles) {
    if (Test-Path $file) {
        Copy-Item -Path $file -Destination $BackupDir -Force
    }
}

# 创建备份清单
$BackupManifest = @"
项目备份清单
备份时间: $(Get-Date)
备份目录: $BackupDir

备份内容:
- chatdb/ (完整应用程序)
- *.db (所有数据库文件)
- 重要文档和脚本

备份大小: $((Get-ChildItem $BackupDir -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB) MB
"@

$BackupManifest | Out-File -FilePath "$BackupDir\backup_manifest.txt" -Encoding UTF8

Write-Host "备份完成！" -ForegroundColor Green
Write-Host "备份位置: $((Get-Item $BackupDir).FullName)" -ForegroundColor Yellow
Write-Host "请验证备份完整性后再执行清理操作。" -ForegroundColor Red
