{"ast": null, "code": "import { warning } from \"rc-util/es/warning\";\nimport { useMemo } from 'react';\nexport default function useRange(range) {\n  return useMemo(function () {\n    if (range === true || !range) {\n      return [!!range, false, false, 0];\n    }\n    var editable = range.editable,\n      draggableTrack = range.draggableTrack,\n      minCount = range.minCount,\n      maxCount = range.maxCount;\n    if (process.env.NODE_ENV !== 'production') {\n      warning(!editable || !draggableTrack, '`editable` can not work with `draggableTrack`.');\n    }\n    return [true, editable, !editable && draggableTrack, minCount || 0, maxCount];\n  }, [range]);\n}", "map": {"version": 3, "names": ["warning", "useMemo", "useRange", "range", "editable", "draggableTrack", "minCount", "maxCount", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/rc-slider/es/hooks/useRange.js"], "sourcesContent": ["import { warning } from \"rc-util/es/warning\";\nimport { useMemo } from 'react';\nexport default function useRange(range) {\n  return useMemo(function () {\n    if (range === true || !range) {\n      return [!!range, false, false, 0];\n    }\n    var editable = range.editable,\n      draggableTrack = range.draggableTrack,\n      minCount = range.minCount,\n      maxCount = range.maxCount;\n    if (process.env.NODE_ENV !== 'production') {\n      warning(!editable || !draggableTrack, '`editable` can not work with `draggableTrack`.');\n    }\n    return [true, editable, !editable && draggableTrack, minCount || 0, maxCount];\n  }, [range]);\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,OAAO,QAAQ,OAAO;AAC/B,eAAe,SAASC,QAAQA,CAACC,KAAK,EAAE;EACtC,OAAOF,OAAO,CAAC,YAAY;IACzB,IAAIE,KAAK,KAAK,IAAI,IAAI,CAACA,KAAK,EAAE;MAC5B,OAAO,CAAC,CAAC,CAACA,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IACnC;IACA,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC3BC,cAAc,GAAGF,KAAK,CAACE,cAAc;MACrCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;MACzBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IAC3B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCV,OAAO,CAAC,CAACI,QAAQ,IAAI,CAACC,cAAc,EAAE,gDAAgD,CAAC;IACzF;IACA,OAAO,CAAC,IAAI,EAAED,QAAQ,EAAE,CAACA,QAAQ,IAAIC,cAAc,EAAEC,QAAQ,IAAI,CAAC,EAAEC,QAAQ,CAAC;EAC/E,CAAC,EAAE,CAACJ,KAAK,CAAC,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}