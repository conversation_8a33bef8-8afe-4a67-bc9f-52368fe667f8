{"ast": null, "code": "\"use client\";\n\nimport Panel from './Panel';\nimport SplitterComp from './Splitter';\nconst Splitter = SplitterComp;\nSplitter.Panel = Panel;\nexport default Splitter;", "map": {"version": 3, "names": ["Panel", "SplitterComp", "Splitter"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/splitter/index.js"], "sourcesContent": ["\"use client\";\n\nimport Panel from './Panel';\nimport SplitterComp from './Splitter';\nconst Splitter = SplitterComp;\nSplitter.Panel = Panel;\nexport default Splitter;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,YAAY,MAAM,YAAY;AACrC,MAAMC,QAAQ,GAAGD,YAAY;AAC7BC,QAAQ,CAACF,KAAK,GAAGA,KAAK;AACtB,eAAeE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}