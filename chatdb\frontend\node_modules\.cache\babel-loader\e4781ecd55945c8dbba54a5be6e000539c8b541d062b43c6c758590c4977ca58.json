{"ast": null, "code": "/*\nLanguage: crmsh\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: http://crmsh.github.io\nDescription: Syntax Highlighting for the crmsh DSL\nCategory: config\n*/\n\n/** @type LanguageFn */\nfunction crmsh(hljs) {\n  const RESOURCES = 'primitive rsc_template';\n  const COMMANDS = 'group clone ms master location colocation order fencing_topology ' + 'rsc_ticket acl_target acl_group user role ' + 'tag xml';\n  const PROPERTY_SETS = 'property rsc_defaults op_defaults';\n  const KEYWORDS = 'params meta operations op rule attributes utilization';\n  const OPERATORS = 'read write deny defined not_defined in_range date spec in ' + 'ref reference attribute type xpath version and or lt gt tag ' + 'lte gte eq ne \\\\';\n  const TYPES = 'number string';\n  const LITERALS = 'Master Started Slave Stopped start promote demote stop monitor true false';\n  return {\n    name: 'crmsh',\n    aliases: ['crm', 'pcmk'],\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS + ' ' + OPERATORS + ' ' + TYPES,\n      literal: LITERALS\n    },\n    contains: [hljs.HASH_COMMENT_MODE, {\n      beginKeywords: 'node',\n      starts: {\n        end: '\\\\s*([\\\\w_-]+:)?',\n        starts: {\n          className: 'title',\n          end: '\\\\s*[\\\\$\\\\w_][\\\\w_-]*'\n        }\n      }\n    }, {\n      beginKeywords: RESOURCES,\n      starts: {\n        className: 'title',\n        end: '\\\\s*[\\\\$\\\\w_][\\\\w_-]*',\n        starts: {\n          end: '\\\\s*@?[\\\\w_][\\\\w_\\\\.:-]*'\n        }\n      }\n    }, {\n      begin: '\\\\b(' + COMMANDS.split(' ').join('|') + ')\\\\s+',\n      keywords: COMMANDS,\n      starts: {\n        className: 'title',\n        end: '[\\\\$\\\\w_][\\\\w_-]*'\n      }\n    }, {\n      beginKeywords: PROPERTY_SETS,\n      starts: {\n        className: 'title',\n        end: '\\\\s*([\\\\w_-]+:)?'\n      }\n    }, hljs.QUOTE_STRING_MODE, {\n      className: 'meta',\n      begin: '(ocf|systemd|service|lsb):[\\\\w_:-]+',\n      relevance: 0\n    }, {\n      className: 'number',\n      begin: '\\\\b\\\\d+(\\\\.\\\\d+)?(ms|s|h|m)?',\n      relevance: 0\n    }, {\n      className: 'literal',\n      begin: '[-]?(infinity|inf)',\n      relevance: 0\n    }, {\n      className: 'attr',\n      begin: /([A-Za-z$_#][\\w_-]+)=/,\n      relevance: 0\n    }, {\n      className: 'tag',\n      begin: '</?',\n      end: '/?>',\n      relevance: 0\n    }]\n  };\n}\nmodule.exports = crmsh;", "map": {"version": 3, "names": ["crmsh", "hljs", "RESOURCES", "COMMANDS", "PROPERTY_SETS", "KEYWORDS", "OPERATORS", "TYPES", "LITERALS", "name", "aliases", "case_insensitive", "keywords", "keyword", "literal", "contains", "HASH_COMMENT_MODE", "beginKeywords", "starts", "end", "className", "begin", "split", "join", "QUOTE_STRING_MODE", "relevance", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/crmsh.js"], "sourcesContent": ["/*\nLanguage: crmsh\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: http://crmsh.github.io\nDescription: Syntax Highlighting for the crmsh DSL\nCategory: config\n*/\n\n/** @type LanguageFn */\nfunction crmsh(hljs) {\n  const RESOURCES = 'primitive rsc_template';\n  const COMMANDS = 'group clone ms master location colocation order fencing_topology ' +\n      'rsc_ticket acl_target acl_group user role ' +\n      'tag xml';\n  const PROPERTY_SETS = 'property rsc_defaults op_defaults';\n  const KEYWORDS = 'params meta operations op rule attributes utilization';\n  const OPERATORS = 'read write deny defined not_defined in_range date spec in ' +\n      'ref reference attribute type xpath version and or lt gt tag ' +\n      'lte gte eq ne \\\\';\n  const TYPES = 'number string';\n  const LITERALS = 'Master Started Slave Stopped start promote demote stop monitor true false';\n\n  return {\n    name: 'crmsh',\n    aliases: [\n      'crm',\n      'pcmk'\n    ],\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS + ' ' + OPERATORS + ' ' + TYPES,\n      literal: LITERALS\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      {\n        beginKeywords: 'node',\n        starts: {\n          end: '\\\\s*([\\\\w_-]+:)?',\n          starts: {\n            className: 'title',\n            end: '\\\\s*[\\\\$\\\\w_][\\\\w_-]*'\n          }\n        }\n      },\n      {\n        beginKeywords: RESOURCES,\n        starts: {\n          className: 'title',\n          end: '\\\\s*[\\\\$\\\\w_][\\\\w_-]*',\n          starts: {\n            end: '\\\\s*@?[\\\\w_][\\\\w_\\\\.:-]*'\n          }\n        }\n      },\n      {\n        begin: '\\\\b(' + COMMANDS.split(' ').join('|') + ')\\\\s+',\n        keywords: COMMANDS,\n        starts: {\n          className: 'title',\n          end: '[\\\\$\\\\w_][\\\\w_-]*'\n        }\n      },\n      {\n        beginKeywords: PROPERTY_SETS,\n        starts: {\n          className: 'title',\n          end: '\\\\s*([\\\\w_-]+:)?'\n        }\n      },\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'meta',\n        begin: '(ocf|systemd|service|lsb):[\\\\w_:-]+',\n        relevance: 0\n      },\n      {\n        className: 'number',\n        begin: '\\\\b\\\\d+(\\\\.\\\\d+)?(ms|s|h|m)?',\n        relevance: 0\n      },\n      {\n        className: 'literal',\n        begin: '[-]?(infinity|inf)',\n        relevance: 0\n      },\n      {\n        className: 'attr',\n        begin: /([A-Za-z$_#][\\w_-]+)=/,\n        relevance: 0\n      },\n      {\n        className: 'tag',\n        begin: '</?',\n        end: '/?>',\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = crmsh;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,MAAMC,SAAS,GAAG,wBAAwB;EAC1C,MAAMC,QAAQ,GAAG,mEAAmE,GAChF,4CAA4C,GAC5C,SAAS;EACb,MAAMC,aAAa,GAAG,mCAAmC;EACzD,MAAMC,QAAQ,GAAG,uDAAuD;EACxE,MAAMC,SAAS,GAAG,4DAA4D,GAC1E,8DAA8D,GAC9D,kBAAkB;EACtB,MAAMC,KAAK,GAAG,eAAe;EAC7B,MAAMC,QAAQ,GAAG,2EAA2E;EAE5F,OAAO;IACLC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,CACP,KAAK,EACL,MAAM,CACP;IACDC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,OAAO,EAAER,QAAQ,GAAG,GAAG,GAAGC,SAAS,GAAG,GAAG,GAAGC,KAAK;MACjDO,OAAO,EAAEN;IACX,CAAC;IACDO,QAAQ,EAAE,CACRd,IAAI,CAACe,iBAAiB,EACtB;MACEC,aAAa,EAAE,MAAM;MACrBC,MAAM,EAAE;QACNC,GAAG,EAAE,kBAAkB;QACvBD,MAAM,EAAE;UACNE,SAAS,EAAE,OAAO;UAClBD,GAAG,EAAE;QACP;MACF;IACF,CAAC,EACD;MACEF,aAAa,EAAEf,SAAS;MACxBgB,MAAM,EAAE;QACNE,SAAS,EAAE,OAAO;QAClBD,GAAG,EAAE,uBAAuB;QAC5BD,MAAM,EAAE;UACNC,GAAG,EAAE;QACP;MACF;IACF,CAAC,EACD;MACEE,KAAK,EAAE,MAAM,GAAGlB,QAAQ,CAACmB,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO;MACvDX,QAAQ,EAAET,QAAQ;MAClBe,MAAM,EAAE;QACNE,SAAS,EAAE,OAAO;QAClBD,GAAG,EAAE;MACP;IACF,CAAC,EACD;MACEF,aAAa,EAAEb,aAAa;MAC5Bc,MAAM,EAAE;QACNE,SAAS,EAAE,OAAO;QAClBD,GAAG,EAAE;MACP;IACF,CAAC,EACDlB,IAAI,CAACuB,iBAAiB,EACtB;MACEJ,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,qCAAqC;MAC5CI,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,8BAA8B;MACrCI,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE,oBAAoB;MAC3BI,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,uBAAuB;MAC9BI,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE,KAAK;MACZF,GAAG,EAAE,KAAK;MACVM,SAAS,EAAE;IACb,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAG3B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}