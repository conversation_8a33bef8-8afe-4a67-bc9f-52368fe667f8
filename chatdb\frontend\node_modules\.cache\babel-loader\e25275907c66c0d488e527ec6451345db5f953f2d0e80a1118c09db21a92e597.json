{"ast": null, "code": "import * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport { useLocale } from '../../locale';\nimport { generateColor } from '../util';\n/**\n * Combine the `color` and `mode` to make sure sync of state.\n */\nexport default function useModeColor(defaultValue, value, mode) {\n  const [locale] = useLocale('ColorPicker');\n  // ======================== Base ========================\n  // Color\n  const [mergedColor, setMergedColor] = useMergedState(defaultValue, {\n    value\n  });\n  // Mode\n  const [modeState, setModeState] = React.useState('single');\n  const [modeOptionList, modeSet] = React.useMemo(() => {\n    const list = (Array.isArray(mode) ? mode : [mode]).filter(m => m);\n    if (!list.length) {\n      list.push('single');\n    }\n    const modes = new Set(list);\n    const optionList = [];\n    const pushOption = (modeType, localeTxt) => {\n      if (modes.has(modeType)) {\n        optionList.push({\n          label: localeTxt,\n          value: modeType\n        });\n      }\n    };\n    pushOption('single', locale.singleColor);\n    pushOption('gradient', locale.gradientColor);\n    return [optionList, modes];\n  }, [mode]);\n  // ======================== Post ========================\n  // We need align `mode` with `color` state\n  // >>>>> Color\n  const [cacheColor, setCacheColor] = React.useState(null);\n  const setColor = useEvent(nextColor => {\n    setCacheColor(nextColor);\n    setMergedColor(nextColor);\n  });\n  const postColor = React.useMemo(() => {\n    const colorObj = generateColor(mergedColor || '');\n    // Use `cacheColor` in case the color is `cleared`\n    return colorObj.equals(cacheColor) ? cacheColor : colorObj;\n  }, [mergedColor, cacheColor]);\n  // >>>>> Mode\n  const postMode = React.useMemo(() => {\n    var _a;\n    if (modeSet.has(modeState)) {\n      return modeState;\n    }\n    return (_a = modeOptionList[0]) === null || _a === void 0 ? void 0 : _a.value;\n  }, [modeSet, modeState, modeOptionList]);\n  // ======================= Effect =======================\n  // Dynamic update mode when color change\n  React.useEffect(() => {\n    setModeState(postColor.isGradient() ? 'gradient' : 'single');\n  }, [postColor]);\n  // ======================= Return =======================\n  return [postColor, setColor, postMode, setModeState, modeOptionList];\n}", "map": {"version": 3, "names": ["React", "useEvent", "useMergedState", "useLocale", "generateColor", "useModeColor", "defaultValue", "value", "mode", "locale", "mergedColor", "setMergedColor", "modeState", "setModeState", "useState", "modeOptionList", "modeSet", "useMemo", "list", "Array", "isArray", "filter", "m", "length", "push", "modes", "Set", "optionList", "pushOption", "modeType", "localeTxt", "has", "label", "singleColor", "gradientColor", "cacheColor", "setCacheColor", "setColor", "nextColor", "postColor", "colorObj", "equals", "postMode", "_a", "useEffect", "isGradient"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/color-picker/hooks/useModeColor.js"], "sourcesContent": ["import * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport { useLocale } from '../../locale';\nimport { generateColor } from '../util';\n/**\n * Combine the `color` and `mode` to make sure sync of state.\n */\nexport default function useModeColor(defaultValue, value, mode) {\n  const [locale] = useLocale('ColorPicker');\n  // ======================== Base ========================\n  // Color\n  const [mergedColor, setMergedColor] = useMergedState(defaultValue, {\n    value\n  });\n  // Mode\n  const [modeState, setModeState] = React.useState('single');\n  const [modeOptionList, modeSet] = React.useMemo(() => {\n    const list = (Array.isArray(mode) ? mode : [mode]).filter(m => m);\n    if (!list.length) {\n      list.push('single');\n    }\n    const modes = new Set(list);\n    const optionList = [];\n    const pushOption = (modeType, localeTxt) => {\n      if (modes.has(modeType)) {\n        optionList.push({\n          label: localeTxt,\n          value: modeType\n        });\n      }\n    };\n    pushOption('single', locale.singleColor);\n    pushOption('gradient', locale.gradientColor);\n    return [optionList, modes];\n  }, [mode]);\n  // ======================== Post ========================\n  // We need align `mode` with `color` state\n  // >>>>> Color\n  const [cacheColor, setCacheColor] = React.useState(null);\n  const setColor = useEvent(nextColor => {\n    setCacheColor(nextColor);\n    setMergedColor(nextColor);\n  });\n  const postColor = React.useMemo(() => {\n    const colorObj = generateColor(mergedColor || '');\n    // Use `cacheColor` in case the color is `cleared`\n    return colorObj.equals(cacheColor) ? cacheColor : colorObj;\n  }, [mergedColor, cacheColor]);\n  // >>>>> Mode\n  const postMode = React.useMemo(() => {\n    var _a;\n    if (modeSet.has(modeState)) {\n      return modeState;\n    }\n    return (_a = modeOptionList[0]) === null || _a === void 0 ? void 0 : _a.value;\n  }, [modeSet, modeState, modeOptionList]);\n  // ======================= Effect =======================\n  // Dynamic update mode when color change\n  React.useEffect(() => {\n    setModeState(postColor.isGradient() ? 'gradient' : 'single');\n  }, [postColor]);\n  // ======================= Return =======================\n  return [postColor, setColor, postMode, setModeState, modeOptionList];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,aAAa,QAAQ,SAAS;AACvC;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC9D,MAAM,CAACC,MAAM,CAAC,GAAGN,SAAS,CAAC,aAAa,CAAC;EACzC;EACA;EACA,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGT,cAAc,CAACI,YAAY,EAAE;IACjEC;EACF,CAAC,CAAC;EACF;EACA,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAGb,KAAK,CAACc,QAAQ,CAAC,QAAQ,CAAC;EAC1D,MAAM,CAACC,cAAc,EAAEC,OAAO,CAAC,GAAGhB,KAAK,CAACiB,OAAO,CAAC,MAAM;IACpD,MAAMC,IAAI,GAAG,CAACC,KAAK,CAACC,OAAO,CAACZ,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC,EAAEa,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC;IACjE,IAAI,CAACJ,IAAI,CAACK,MAAM,EAAE;MAChBL,IAAI,CAACM,IAAI,CAAC,QAAQ,CAAC;IACrB;IACA,MAAMC,KAAK,GAAG,IAAIC,GAAG,CAACR,IAAI,CAAC;IAC3B,MAAMS,UAAU,GAAG,EAAE;IACrB,MAAMC,UAAU,GAAGA,CAACC,QAAQ,EAAEC,SAAS,KAAK;MAC1C,IAAIL,KAAK,CAACM,GAAG,CAACF,QAAQ,CAAC,EAAE;QACvBF,UAAU,CAACH,IAAI,CAAC;UACdQ,KAAK,EAAEF,SAAS;UAChBvB,KAAK,EAAEsB;QACT,CAAC,CAAC;MACJ;IACF,CAAC;IACDD,UAAU,CAAC,QAAQ,EAAEnB,MAAM,CAACwB,WAAW,CAAC;IACxCL,UAAU,CAAC,UAAU,EAAEnB,MAAM,CAACyB,aAAa,CAAC;IAC5C,OAAO,CAACP,UAAU,EAAEF,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACjB,IAAI,CAAC,CAAC;EACV;EACA;EACA;EACA,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAGpC,KAAK,CAACc,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMuB,QAAQ,GAAGpC,QAAQ,CAACqC,SAAS,IAAI;IACrCF,aAAa,CAACE,SAAS,CAAC;IACxB3B,cAAc,CAAC2B,SAAS,CAAC;EAC3B,CAAC,CAAC;EACF,MAAMC,SAAS,GAAGvC,KAAK,CAACiB,OAAO,CAAC,MAAM;IACpC,MAAMuB,QAAQ,GAAGpC,aAAa,CAACM,WAAW,IAAI,EAAE,CAAC;IACjD;IACA,OAAO8B,QAAQ,CAACC,MAAM,CAACN,UAAU,CAAC,GAAGA,UAAU,GAAGK,QAAQ;EAC5D,CAAC,EAAE,CAAC9B,WAAW,EAAEyB,UAAU,CAAC,CAAC;EAC7B;EACA,MAAMO,QAAQ,GAAG1C,KAAK,CAACiB,OAAO,CAAC,MAAM;IACnC,IAAI0B,EAAE;IACN,IAAI3B,OAAO,CAACe,GAAG,CAACnB,SAAS,CAAC,EAAE;MAC1B,OAAOA,SAAS;IAClB;IACA,OAAO,CAAC+B,EAAE,GAAG5B,cAAc,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI4B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpC,KAAK;EAC/E,CAAC,EAAE,CAACS,OAAO,EAAEJ,SAAS,EAAEG,cAAc,CAAC,CAAC;EACxC;EACA;EACAf,KAAK,CAAC4C,SAAS,CAAC,MAAM;IACpB/B,YAAY,CAAC0B,SAAS,CAACM,UAAU,CAAC,CAAC,GAAG,UAAU,GAAG,QAAQ,CAAC;EAC9D,CAAC,EAAE,CAACN,SAAS,CAAC,CAAC;EACf;EACA,OAAO,CAACA,SAAS,EAAEF,QAAQ,EAAEK,QAAQ,EAAE7B,YAAY,EAAEE,cAAc,CAAC;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}