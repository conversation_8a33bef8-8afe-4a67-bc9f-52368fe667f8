{"ast": null, "code": "import { useMemo } from 'react';\nexport default function useShowSizeChanger(showSizeChanger) {\n  return useMemo(() => {\n    if (typeof showSizeChanger === 'boolean') {\n      return [showSizeChanger, {}];\n    }\n    if (showSizeChanger && typeof showSizeChanger === 'object') {\n      return [true, showSizeChanger];\n    }\n    return [undefined, undefined];\n  }, [showSizeChanger]);\n}", "map": {"version": 3, "names": ["useMemo", "useShowSizeChanger", "showSizeChanger", "undefined"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/pagination/useShowSizeChanger.js"], "sourcesContent": ["import { useMemo } from 'react';\nexport default function useShowSizeChanger(showSizeChanger) {\n  return useMemo(() => {\n    if (typeof showSizeChanger === 'boolean') {\n      return [showSizeChanger, {}];\n    }\n    if (showSizeChanger && typeof showSizeChanger === 'object') {\n      return [true, showSizeChanger];\n    }\n    return [undefined, undefined];\n  }, [showSizeChanger]);\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,eAAe,SAASC,kBAAkBA,CAACC,eAAe,EAAE;EAC1D,OAAOF,OAAO,CAAC,MAAM;IACnB,IAAI,OAAOE,eAAe,KAAK,SAAS,EAAE;MACxC,OAAO,CAACA,eAAe,EAAE,CAAC,CAAC,CAAC;IAC9B;IACA,IAAIA,eAAe,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;MAC1D,OAAO,CAAC,IAAI,EAAEA,eAAe,CAAC;IAChC;IACA,OAAO,CAACC,SAAS,EAAEA,SAAS,CAAC;EAC/B,CAAC,EAAE,CAACD,eAAe,CAAC,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}