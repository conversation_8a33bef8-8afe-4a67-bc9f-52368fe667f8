{"ast": null, "code": "// Style as status component\nimport { prepareComponentToken, prepareToken } from '.';\nimport { genPresetColor, genSubStyleComponent } from '../../theme/internal';\n// ============================== Preset ==============================\nconst genPresetStyle = token => genPresetColor(token, (colorKey, _ref) => {\n  let {\n    textColor,\n    lightBorderColor,\n    lightColor,\n    darkColor\n  } = _ref;\n  return {\n    [`${token.componentCls}${token.componentCls}-${colorKey}`]: {\n      color: textColor,\n      background: lightColor,\n      borderColor: lightBorderColor,\n      // Inverse color\n      '&-inverse': {\n        color: token.colorTextLightSolid,\n        background: darkColor,\n        borderColor: darkColor\n      },\n      [`&${token.componentCls}-borderless`]: {\n        borderColor: 'transparent'\n      }\n    }\n  };\n});\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'preset'], token => {\n  const tagToken = prepareToken(token);\n  return genPresetStyle(tagToken);\n}, prepareComponentToken);", "map": {"version": 3, "names": ["prepareComponentToken", "prepareToken", "genPresetColor", "genSubStyleComponent", "genPresetStyle", "token", "colorKey", "_ref", "textColor", "lightBorderColor", "lightColor", "darkColor", "componentCls", "color", "background", "borderColor", "colorTextLightSolid", "tagToken"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/tag/style/presetCmp.js"], "sourcesContent": ["// Style as status component\nimport { prepareComponentToken, prepareToken } from '.';\nimport { genPresetColor, genSubStyleComponent } from '../../theme/internal';\n// ============================== Preset ==============================\nconst genPresetStyle = token => genPresetColor(token, (colorKey, _ref) => {\n  let {\n    textColor,\n    lightBorderColor,\n    lightColor,\n    darkColor\n  } = _ref;\n  return {\n    [`${token.componentCls}${token.componentCls}-${colorKey}`]: {\n      color: textColor,\n      background: lightColor,\n      borderColor: lightBorderColor,\n      // Inverse color\n      '&-inverse': {\n        color: token.colorTextLightSolid,\n        background: darkColor,\n        borderColor: darkColor\n      },\n      [`&${token.componentCls}-borderless`]: {\n        borderColor: 'transparent'\n      }\n    }\n  };\n});\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'preset'], token => {\n  const tagToken = prepareToken(token);\n  return genPresetStyle(tagToken);\n}, prepareComponentToken);"], "mappings": "AAAA;AACA,SAASA,qBAAqB,EAAEC,YAAY,QAAQ,GAAG;AACvD,SAASC,cAAc,EAAEC,oBAAoB,QAAQ,sBAAsB;AAC3E;AACA,MAAMC,cAAc,GAAGC,KAAK,IAAIH,cAAc,CAACG,KAAK,EAAE,CAACC,QAAQ,EAAEC,IAAI,KAAK;EACxE,IAAI;IACFC,SAAS;IACTC,gBAAgB;IAChBC,UAAU;IACVC;EACF,CAAC,GAAGJ,IAAI;EACR,OAAO;IACL,CAAC,GAAGF,KAAK,CAACO,YAAY,GAAGP,KAAK,CAACO,YAAY,IAAIN,QAAQ,EAAE,GAAG;MAC1DO,KAAK,EAAEL,SAAS;MAChBM,UAAU,EAAEJ,UAAU;MACtBK,WAAW,EAAEN,gBAAgB;MAC7B;MACA,WAAW,EAAE;QACXI,KAAK,EAAER,KAAK,CAACW,mBAAmB;QAChCF,UAAU,EAAEH,SAAS;QACrBI,WAAW,EAAEJ;MACf,CAAC;MACD,CAAC,IAAIN,KAAK,CAACO,YAAY,aAAa,GAAG;QACrCG,WAAW,EAAE;MACf;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF;AACA,eAAeZ,oBAAoB,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAEE,KAAK,IAAI;EAC9D,MAAMY,QAAQ,GAAGhB,YAAY,CAACI,KAAK,CAAC;EACpC,OAAOD,cAAc,CAACa,QAAQ,CAAC;AACjC,CAAC,EAAEjB,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}