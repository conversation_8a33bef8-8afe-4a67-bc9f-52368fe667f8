{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nexport default function Popup(props) {\n  var children = props.children,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    innerStyle = props.overlayInnerStyle,\n    bodyClassName = props.bodyClassName,\n    className = props.className,\n    style = props.style;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    style: style\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-inner\"), bodyClassName),\n    id: id,\n    role: \"tooltip\",\n    style: innerStyle\n  }, typeof children === 'function' ? children() : children));\n}", "map": {"version": 3, "names": ["classNames", "React", "Popup", "props", "children", "prefixCls", "id", "innerStyle", "overlayInnerStyle", "bodyClassName", "className", "style", "createElement", "concat", "role"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/rc-tooltip/es/Popup.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nexport default function Popup(props) {\n  var children = props.children,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    innerStyle = props.overlayInnerStyle,\n    bodyClassName = props.bodyClassName,\n    className = props.className,\n    style = props.style;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    style: style\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-inner\"), bodyClassName),\n    id: id,\n    role: \"tooltip\",\n    style: innerStyle\n  }, typeof children === 'function' ? children() : children));\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,KAAKA,CAACC,KAAK,EAAE;EACnC,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAC3BC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,EAAE,GAAGH,KAAK,CAACG,EAAE;IACbC,UAAU,GAAGJ,KAAK,CAACK,iBAAiB;IACpCC,aAAa,GAAGN,KAAK,CAACM,aAAa;IACnCC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,KAAK,GAAGR,KAAK,CAACQ,KAAK;EACrB,OAAO,aAAaV,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IAC7CF,SAAS,EAAEV,UAAU,CAAC,EAAE,CAACa,MAAM,CAACR,SAAS,EAAE,UAAU,CAAC,EAAEK,SAAS,CAAC;IAClEC,KAAK,EAAEA;EACT,CAAC,EAAE,aAAaV,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IACzCF,SAAS,EAAEV,UAAU,CAAC,EAAE,CAACa,MAAM,CAACR,SAAS,EAAE,QAAQ,CAAC,EAAEI,aAAa,CAAC;IACpEH,EAAE,EAAEA,EAAE;IACNQ,IAAI,EAAE,SAAS;IACfH,KAAK,EAAEJ;EACT,CAAC,EAAE,OAAOH,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}