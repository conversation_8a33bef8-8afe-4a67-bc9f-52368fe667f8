{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CrownFilledSvg from \"@ant-design/icons-svg/es/asn/CrownFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CrownFilled = function CrownFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CrownFilledSvg\n  }));\n};\n\n/**![crown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5OS42IDI3Ni41TDcwNSAzOTYuNCA1MTguNCAxNDcuNWE4LjA2IDguMDYgMCAwMC0xMi45IDBMMzE5IDM5Ni40IDEyNC4zIDI3Ni41Yy01LjctMy41LTEzLjEgMS4yLTEyLjIgNy45TDE4OC41IDg2NWMxLjEgNy45IDcuOSAxNCAxNiAxNGg2MTUuMWM4IDAgMTQuOS02IDE1LjktMTRsNzYuNC01ODAuNmMuOC02LjctNi41LTExLjQtMTIuMy03Ljl6TTUxMiA3MzQuMmMtNjIuMSAwLTExMi42LTUwLjUtMTEyLjYtMTEyLjZTNDQ5LjkgNTA5IDUxMiA1MDlzMTEyLjYgNTAuNSAxMTIuNiAxMTIuNlM1NzQuMSA3MzQuMiA1MTIgNzM0LjJ6bTAtMTYwLjljLTI2LjYgMC00OC4yIDIxLjYtNDguMiA0OC4zIDAgMjYuNiAyMS42IDQ4LjMgNDguMiA0OC4zczQ4LjItMjEuNiA0OC4yLTQ4LjNjMC0yNi42LTIxLjYtNDguMy00OC4yLTQ4LjN6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CrownFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CrownFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "CrownFilledSvg", "AntdIcon", "CrownFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/CrownFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CrownFilledSvg from \"@ant-design/icons-svg/es/asn/CrownFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CrownFilled = function CrownFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CrownFilledSvg\n  }));\n};\n\n/**![crown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5OS42IDI3Ni41TDcwNSAzOTYuNCA1MTguNCAxNDcuNWE4LjA2IDguMDYgMCAwMC0xMi45IDBMMzE5IDM5Ni40IDEyNC4zIDI3Ni41Yy01LjctMy41LTEzLjEgMS4yLTEyLjIgNy45TDE4OC41IDg2NWMxLjEgNy45IDcuOSAxNCAxNiAxNGg2MTUuMWM4IDAgMTQuOS02IDE1LjktMTRsNzYuNC01ODAuNmMuOC02LjctNi41LTExLjQtMTIuMy03Ljl6TTUxMiA3MzQuMmMtNjIuMSAwLTExMi42LTUwLjUtMTEyLjYtMTEyLjZTNDQ5LjkgNTA5IDUxMiA1MDlzMTEyLjYgNTAuNSAxMTIuNiAxMTIuNlM1NzQuMSA3MzQuMiA1MTIgNzM0LjJ6bTAtMTYwLjljLTI2LjYgMC00OC4yIDIxLjYtNDguMiA0OC4zIDAgMjYuNiAyMS42IDQ4LjMgNDguMiA0OC4zczQ4LjItMjEuNiA0OC4yLTQ4LjNjMC0yNi42LTIxLjYtNDguMy00OC4yLTQ4LjN6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CrownFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CrownFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}