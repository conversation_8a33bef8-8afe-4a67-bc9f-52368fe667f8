{"ast": null, "code": "import { createTheme, getComputedToken } from '@ant-design/cssinjs';\nimport defaultTheme from './themes/default/theme';\nimport seedToken from './themes/seed';\nimport formatToken from './util/alias';\nconst getDesignToken = config => {\n  const theme = (config === null || config === void 0 ? void 0 : config.algorithm) ? createTheme(config.algorithm) : defaultTheme;\n  const mergedToken = Object.assign(Object.assign({}, seedToken), config === null || config === void 0 ? void 0 : config.token);\n  return getComputedToken(mergedToken, {\n    override: config === null || config === void 0 ? void 0 : config.token\n  }, theme, formatToken);\n};\nexport default getDesignToken;", "map": {"version": 3, "names": ["createTheme", "getComputedToken", "defaultTheme", "seedToken", "formatToken", "getDesignToken", "config", "theme", "algorithm", "mergedToken", "Object", "assign", "token", "override"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/theme/getDesignToken.js"], "sourcesContent": ["import { createTheme, getComputedToken } from '@ant-design/cssinjs';\nimport defaultTheme from './themes/default/theme';\nimport seedToken from './themes/seed';\nimport formatToken from './util/alias';\nconst getDesignToken = config => {\n  const theme = (config === null || config === void 0 ? void 0 : config.algorithm) ? createTheme(config.algorithm) : defaultTheme;\n  const mergedToken = Object.assign(Object.assign({}, seedToken), config === null || config === void 0 ? void 0 : config.token);\n  return getComputedToken(mergedToken, {\n    override: config === null || config === void 0 ? void 0 : config.token\n  }, theme, formatToken);\n};\nexport default getDesignToken;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAQ,qBAAqB;AACnE,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,SAAS,MAAM,eAAe;AACrC,OAAOC,WAAW,MAAM,cAAc;AACtC,MAAMC,cAAc,GAAGC,MAAM,IAAI;EAC/B,MAAMC,KAAK,GAAG,CAACD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,SAAS,IAAIR,WAAW,CAACM,MAAM,CAACE,SAAS,CAAC,GAAGN,YAAY;EAC/H,MAAMO,WAAW,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,SAAS,CAAC,EAAEG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACM,KAAK,CAAC;EAC7H,OAAOX,gBAAgB,CAACQ,WAAW,EAAE;IACnCI,QAAQ,EAAEP,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACM;EACnE,CAAC,EAAEL,KAAK,EAAEH,WAAW,CAAC;AACxB,CAAC;AACD,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}