# 智能数据分析系统确认清理脚本
# 清理需要用户确认的文件

param(
    [switch]$DryRun = $false,
    [switch]$Interactive = $true
)

$CleanupLog = "cleanup_confirm_log_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$TotalSaved = 0

function Write-CleanupLog {
    param($Message, $Color = "White")
    $LogEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss'): $Message"
    Write-Host $LogEntry -ForegroundColor $Color
    $LogEntry | Out-File -FilePath $CleanupLog -Append -Encoding UTF8
}

function Confirm-Deletion {
    param($FilePath, $Reason, $Category)
    
    if (-not (Test-Path $FilePath)) {
        return $false
    }
    
    $FileSize = if (Test-Path $FilePath -PathType Container) {
        (Get-ChildItem $FilePath -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
    } else {
        (Get-Item $FilePath).Length
    }
    
    Write-Host "`n文件/目录: $FilePath" -ForegroundColor Cyan
    Write-Host "大小: $([math]::Round($FileSize/1KB, 2)) KB" -ForegroundColor Yellow
    Write-Host "类别: $Category" -ForegroundColor Magenta
    Write-Host "原因: $Reason" -ForegroundColor White
    
    if ($Interactive) {
        $response = Read-Host "是否删除? (y/n/s=跳过所有同类)"
        return $response.ToLower()
    } else {
        return "y"
    }
}

function Remove-ConfirmedFile {
    param($FilePath, $Reason)
    
    if (Test-Path $FilePath) {
        $FileSize = if (Test-Path $FilePath -PathType Container) {
            (Get-ChildItem $FilePath -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
        } else {
            (Get-Item $FilePath).Length
        }
        
        if ($DryRun) {
            Write-CleanupLog "DRY RUN: 将删除 $FilePath ($([math]::Round($FileSize/1KB, 2)) KB) - $Reason" "Yellow"
        } else {
            try {
                if (Test-Path $FilePath -PathType Container) {
                    Remove-Item $FilePath -Recurse -Force
                } else {
                    Remove-Item $FilePath -Force
                }
                Write-CleanupLog "已删除: $FilePath ($([math]::Round($FileSize/1KB, 2)) KB) - $Reason" "Green"
                $script:TotalSaved += $FileSize
            } catch {
                Write-CleanupLog "删除失败: $FilePath - $($_.Exception.Message)" "Red"
            }
        }
    }
}

Write-CleanupLog "开始确认清理过程..." "Cyan"

# 1. 测试和验证脚本
Write-Host "`n=== 测试和验证脚本 ===" -ForegroundColor Green

$TestScripts = @(
    @{Path="analyze_sql_issue.py"; Reason="SQL问题分析脚本，调试完成后可删除"},
    @{Path="check_db_schema.py"; Reason="数据库模式检查脚本，一次性使用"},
    @{Path="check_resource_db_status.py"; Reason="资源数据库状态检查脚本"},
    @{Path="cleanup_duplicate_data.py"; Reason="重复数据清理脚本，任务完成后可删除"},
    @{Path="database_duplication_analysis.py"; Reason="数据库重复分析脚本，分析完成后可删除"},
    @{Path="test_enhanced_metadata.py"; Reason="元数据增强测试脚本"},
    @{Path="test_metadata_integration.py"; Reason="元数据集成测试脚本"},
    @{Path="test_metadata_loading.py"; Reason="元数据加载测试脚本"},
    @{Path="test_metadata_queries.py"; Reason="元数据查询测试脚本"},
    @{Path="verify_complete_setup.py"; Reason="完整设置验证脚本"},
    @{Path="verify_database_status.py"; Reason="数据库状态验证脚本"},
    @{Path="verify_debit_credit_amounts.py"; Reason="借贷金额验证脚本"},
    @{Path="verify_rename_success.py"; Reason="重命名成功验证脚本"}
)

$skipTestScripts = $false
foreach ($script in $TestScripts) {
    if ($skipTestScripts) { break }
    
    $response = Confirm-Deletion $script.Path $script.Reason "测试/验证脚本"
    
    switch ($response) {
        "y" { Remove-ConfirmedFile $script.Path $script.Reason }
        "s" { $skipTestScripts = $true; Write-Host "跳过所有测试脚本" -ForegroundColor Yellow }
        "n" { Write-Host "保留文件" -ForegroundColor Gray }
    }
}

# 2. 数据库操作脚本
Write-Host "`n=== 数据库操作脚本 ===" -ForegroundColor Green

$DbScripts = @(
    @{Path="execute_management_expense_query.py"; Reason="管理费用查询执行脚本，一次性使用"},
    @{Path="migrate_metadata_only.py"; Reason="元数据迁移脚本，迁移完成后可删除"},
    @{Path="modify_balance_field_type.py"; Reason="余额字段类型修改脚本，修改完成后可删除"},
    @{Path="rename_financial_columns.py"; Reason="财务列重命名脚本，重命名完成后可删除"},
    @{Path="restore_original_database.py"; Reason="数据库恢复脚本，保留作为工具"}
)

$skipDbScripts = $false
foreach ($script in $DbScripts) {
    if ($skipDbScripts) { break }
    
    $response = Confirm-Deletion $script.Path $script.Reason "数据库操作脚本"
    
    switch ($response) {
        "y" { Remove-ConfirmedFile $script.Path $script.Reason }
        "s" { $skipDbScripts = $true; Write-Host "跳过所有数据库脚本" -ForegroundColor Yellow }
        "n" { Write-Host "保留文件" -ForegroundColor Gray }
    }
}

# 3. 分析报告文档
Write-Host "`n=== 分析报告文档 ===" -ForegroundColor Green

$ReportDocs = @(
    @{Path="CLEANUP_SUCCESS_REPORT.md"; Reason="清理成功报告，过程性文档"},
    @{Path="CRITICAL_DATABASE_ANALYSIS.md"; Reason="关键数据库分析报告，过程性文档"},
    @{Path="DATABASE_ARCHITECTURE_ANALYSIS.md"; Reason="数据库架构分析报告，过程性文档"},
    @{Path="DATABASE_DUPLICATION_ANALYSIS_REPORT.md"; Reason="数据库重复分析报告，过程性文档"},
    @{Path="DEPLOYMENT_CHECKLIST.md"; Reason="部署检查清单，可能需要保留"},
    @{Path="FINAL_INTEGRATION_ANALYSIS_REPORT.md"; Reason="最终集成分析报告，过程性文档"},
    @{Path="FINAL_TECHNICAL_RECOMMENDATIONS.md"; Reason="最终技术建议，重要文档"},
    @{Path="FINANCIAL_DATA_DESCRIPTION_REPORT.md"; Reason="财务数据描述报告，过程性文档"},
    @{Path="INTEGRATION_COMPLETION_REPORT.md"; Reason="集成完成报告，过程性文档"},
    @{Path="LLM_METADATA_INTEGRATION_ANALYSIS.md"; Reason="LLM元数据集成分析，过程性文档"},
    @{Path="METADATA_ONLY_MIGRATION_PLAN.md"; Reason="元数据迁移计划，过程性文档"},
    @{Path="METADATA_SYSTEM_DESIGN_PHILOSOPHY.md"; Reason="元数据系统设计理念，重要文档"},
    @{Path="METADATA_SYSTEM_SUMMARY.md"; Reason="元数据系统总结，重要文档"},
    @{Path="MIGRATION_SUCCESS_REPORT.md"; Reason="迁移成功报告，过程性文档"},
    @{Path="TEXT2SQL_DETAILED_ANALYSIS.md"; Reason="Text2SQL详细分析，重要文档"},
    @{Path="TEXT2SQL_INTEGRATION_CHECKLIST.md"; Reason="Text2SQL集成检查清单，重要文档"}
)

$skipReportDocs = $false
foreach ($doc in $ReportDocs) {
    if ($skipReportDocs) { break }
    
    $response = Confirm-Deletion $doc.Path $doc.Reason "分析报告文档"
    
    switch ($response) {
        "y" { Remove-ConfirmedFile $doc.Path $doc.Reason }
        "s" { $skipReportDocs = $true; Write-Host "跳过所有报告文档" -ForegroundColor Yellow }
        "n" { Write-Host "保留文件" -ForegroundColor Gray }
    }
}

# 4. 其他文件
Write-Host "`n=== 其他文件 ===" -ForegroundColor Green

$OtherFiles = @(
    @{Path="input.py"; Reason="输入处理脚本，用途不明"},
    @{Path="ai_metadata_usage_examples.py"; Reason="AI元数据使用示例，可能有参考价值"},
    @{Path="metadata_integration_implementation.py"; Reason="元数据集成实现，实现完成后可删除"},
    @{Path="view_financial_data_descriptions.py"; Reason="查看财务数据描述脚本，工具脚本"}
)

$skipOtherFiles = $false
foreach ($file in $OtherFiles) {
    if ($skipOtherFiles) { break }
    
    $response = Confirm-Deletion $file.Path $file.Reason "其他文件"
    
    switch ($response) {
        "y" { Remove-ConfirmedFile $file.Path $file.Reason }
        "s" { $skipOtherFiles = $true; Write-Host "跳过所有其他文件" -ForegroundColor Yellow }
        "n" { Write-Host "保留文件" -ForegroundColor Gray }
    }
}

# 总结
Write-CleanupLog "`n确认清理完成！" "Green"
Write-CleanupLog "总共节省空间: $([math]::Round($TotalSaved/1KB, 2)) KB" "Green"

if ($DryRun) {
    Write-CleanupLog "这是预览模式，没有实际删除文件。" "Yellow"
} else {
    Write-CleanupLog "确认清理已完成。日志文件: $CleanupLog" "Green"
}
