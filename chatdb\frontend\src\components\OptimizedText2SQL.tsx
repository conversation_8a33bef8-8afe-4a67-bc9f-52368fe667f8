/**
 * 优化版本的Text2SQL组件
 * 集成性能监控、错误处理、缓存和用户体验优化
 */
import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Table, 
  Alert, 
  Spin, 
  Progress, 
  Tooltip, 
  Space,
  Divider,
  Tag,
  Collapse,
  Statistic,
  Row,
  Col
} from 'antd';
import { 
  PlayCircleOutlined, 
  ClearOutlined, 
  HistoryOutlined,
  ThunderboltOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';

import { apiClient } from '../services/api-client';
import { useErrorHandler } from '../services/error-handler';
import { usePerformanceMonitor } from '../services/performance-monitor';
import { useAppState } from '../services/state-manager';

const { TextArea } = Input;
const { Panel } = Collapse;

interface QueryResult {
  sql: string;
  results: any[];
  error?: string;
  context?: {
    performance_analysis?: {
      complexity_score: number;
      performance_issues: string[];
      optimization_suggestions: string[];
      estimated_cost: string;
    };
  };
}

interface QueryStats {
  executionTime: number;
  resultCount: number;
  cacheHit: boolean;
  complexityScore: number;
}

const OptimizedText2SQL: React.FC = () => {
  // 状态管理
  const { state, setQueryLoading, setQueryResults, setQueryError, addQueryToHistory } = useAppState();
  const { handleError, retry } = useErrorHandler();
  const { recordApiCall, recordComponentMount } = usePerformanceMonitor();

  // 本地状态
  const [query, setQuery] = useState('');
  const [result, setResult] = useState<QueryResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [queryStats, setQueryStats] = useState<QueryStats | null>(null);
  const [queryHistory, setQueryHistory] = useState<string[]>([]);

  // 引用
  const abortControllerRef = useRef<AbortController | null>(null);
  const startTimeRef = useRef<number>(0);

  // 性能监控
  useEffect(() => {
    const startTime = performance.now();
    return () => {
      const mountTime = performance.now() - startTime;
      recordComponentMount('OptimizedText2SQL', mountTime);
    };
  }, [recordComponentMount]);

  // 表格列配置（使用useMemo优化）
  const tableColumns = useMemo(() => {
    if (!result?.results || result.results.length === 0) return [];
    
    const firstRow = result.results[0];
    return Object.keys(firstRow).map(key => ({
      title: key,
      dataIndex: key,
      key: key,
      ellipsis: true,
      sorter: (a: any, b: any) => {
        const aVal = a[key];
        const bVal = b[key];
        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return aVal - bVal;
        }
        return String(aVal).localeCompare(String(bVal));
      },
    }));
  }, [result?.results]);

  // 执行查询
  const executeQuery = useCallback(async () => {
    if (!query.trim()) {
      handleError(new Error('请输入查询内容'));
      return;
    }

    if (!state.connections.selected) {
      handleError(new Error('请先选择数据库连接'));
      return;
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    startTimeRef.current = performance.now();

    setLoading(true);
    setProgress(0);
    setResult(null);
    setQueryStats(null);
    setQueryLoading(true);

    try {
      // 使用流式API获取实时进度
      await apiClient.stream('/api/optimized-text2sql/query-stream', {
        onMessage: (data) => {
          switch (data.type) {
            case 'start':
              setProgress(10);
              break;
            case 'progress':
              setProgress(data.progress || 0);
              break;
            case 'sql_generated':
              setProgress(60);
              setResult(prev => ({ ...prev, sql: data.sql } as QueryResult));
              break;
            case 'results':
              setProgress(100);
              const executionTime = performance.now() - startTimeRef.current;
              
              const queryResult: QueryResult = {
                sql: result?.sql || '',
                results: data.data || [],
                context: data.context
              };
              
              setResult(queryResult);
              setQueryResults(data.data || [], null);
              
              // 记录性能统计
              const stats: QueryStats = {
                executionTime,
                resultCount: data.data?.length || 0,
                cacheHit: data.cacheHit || false,
                complexityScore: data.context?.performance_analysis?.complexity_score || 0
              };
              setQueryStats(stats);
              
              // 记录API调用性能
              recordApiCall(executionTime, '/api/optimized-text2sql/query-stream');
              
              // 添加到查询历史
              addQueryToHistory(query);
              setQueryHistory(prev => [query, ...prev.filter(q => q !== query)].slice(0, 10));
              
              break;
            case 'error':
              throw new Error(data.message);
            case 'complete':
              setProgress(100);
              break;
          }
        },
        onError: (error) => {
          const executionTime = performance.now() - startTimeRef.current;
          recordApiCall(executionTime, '/api/optimized-text2sql/query-stream');
          handleError(error, { query: query.slice(0, 100) });
          setQueryError(error.message);
        },
        onComplete: () => {
          setLoading(false);
          setQueryLoading(false);
        },
        signal: abortControllerRef.current.signal
      });

    } catch (error) {
      const executionTime = performance.now() - startTimeRef.current;
      recordApiCall(executionTime, '/api/optimized-text2sql/query-stream');
      
      setLoading(false);
      setQueryLoading(false);
      setProgress(0);
      
      handleError(error as Error, { query: query.slice(0, 100) });
      setQueryError((error as Error).message);
    }
  }, [query, state.connections.selected, handleError, recordApiCall, addQueryToHistory, setQueryLoading, setQueryResults, setQueryError]);

  // 重试查询
  const retryQuery = useCallback(async () => {
    await retry(executeQuery, { action: 'retry_query' });
  }, [retry, executeQuery]);

  // 清空结果
  const clearResults = useCallback(() => {
    setResult(null);
    setQueryStats(null);
    setProgress(0);
    setQueryError(null);
  }, [setQueryError]);

  // 取消查询
  const cancelQuery = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setLoading(false);
      setQueryLoading(false);
      setProgress(0);
    }
  }, [setQueryLoading]);

  // 性能分析组件
  const PerformanceAnalysis = useMemo(() => {
    if (!result?.context?.performance_analysis) return null;

    const analysis = result.context.performance_analysis;
    const getCostColor = (cost: string) => {
      switch (cost) {
        case 'low': return 'green';
        case 'medium': return 'orange';
        case 'high': return 'red';
        default: return 'default';
      }
    };

    return (
      <Card size="small" title="性能分析" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic 
              title="复杂度分数" 
              value={analysis.complexity_score} 
              prefix={<ThunderboltOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic 
              title="预估成本" 
              value={analysis.estimated_cost.toUpperCase()} 
              valueStyle={{ color: getCostColor(analysis.estimated_cost) }}
            />
          </Col>
          {queryStats && (
            <>
              <Col span={6}>
                <Statistic 
                  title="执行时间" 
                  value={queryStats.executionTime} 
                  suffix="ms"
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="结果数量" 
                  value={queryStats.resultCount} 
                  prefix={<DatabaseOutlined />}
                />
              </Col>
            </>
          )}
        </Row>

        {analysis.performance_issues.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <h4>性能问题:</h4>
            {analysis.performance_issues.map((issue, index) => (
              <Tag key={index} color="warning" style={{ marginBottom: 4 }}>
                <ExclamationCircleOutlined /> {issue}
              </Tag>
            ))}
          </div>
        )}

        {analysis.optimization_suggestions.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <h4>优化建议:</h4>
            {analysis.optimization_suggestions.map((suggestion, index) => (
              <Tag key={index} color="success" style={{ marginBottom: 4 }}>
                <CheckCircleOutlined /> {suggestion}
              </Tag>
            ))}
          </div>
        )}
      </Card>
    );
  }, [result?.context?.performance_analysis, queryStats]);

  return (
    <div style={{ padding: 24 }}>
      <Card title="智能SQL查询 (优化版)" extra={
        <Space>
          {queryStats?.cacheHit && (
            <Tooltip title="此查询结果来自缓存">
              <Tag color="blue">缓存命中</Tag>
            </Tooltip>
          )}
          <Button 
            icon={<HistoryOutlined />} 
            onClick={() => console.log('查询历史:', queryHistory)}
          >
            历史
          </Button>
        </Space>
      }>
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* 查询输入区域 */}
          <div>
            <TextArea
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="请输入您的查询需求，例如：查询最近一个月的销售数据"
              rows={4}
              disabled={loading}
            />
            
            {loading && (
              <div style={{ marginTop: 8 }}>
                <Progress percent={progress} size="small" />
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={executeQuery}
              loading={loading}
              disabled={!query.trim() || !state.connections.selected}
            >
              {loading ? '执行中...' : '执行查询'}
            </Button>
            
            {loading && (
              <Button onClick={cancelQuery}>
                取消
              </Button>
            )}
            
            <Button
              icon={<ClearOutlined />}
              onClick={clearResults}
              disabled={loading}
            >
              清空结果
            </Button>
            
            {result?.error && (
              <Button
                type="dashed"
                onClick={retryQuery}
                disabled={loading}
              >
                重试
              </Button>
            )}
          </Space>

          {/* 错误显示 */}
          {result?.error && (
            <Alert
              message="查询执行失败"
              description={result.error}
              type="error"
              showIcon
              closable
            />
          )}

          {/* 结果显示 */}
          {result && !result.error && (
            <Collapse defaultActiveKey={['results']}>
              {/* SQL语句 */}
              {result.sql && (
                <Panel header="生成的SQL语句" key="sql">
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: 12, 
                    borderRadius: 4,
                    overflow: 'auto'
                  }}>
                    {result.sql}
                  </pre>
                </Panel>
              )}

              {/* 查询结果 */}
              <Panel header={`查询结果 (${result.results?.length || 0} 条记录)`} key="results">
                {result.results && result.results.length > 0 ? (
                  <Table
                    columns={tableColumns}
                    dataSource={result.results.map((row, index) => ({ ...row, key: index }))}
                    scroll={{ x: true, y: 400 }}
                    pagination={{
                      pageSize: 50,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条记录`
                    }}
                    size="small"
                  />
                ) : (
                  <div style={{ textAlign: 'center', padding: 20 }}>
                    <DatabaseOutlined style={{ fontSize: 48, color: '#ccc' }} />
                    <p>暂无数据</p>
                  </div>
                )}
              </Panel>
            </Collapse>
          )}

          {/* 性能分析 */}
          {PerformanceAnalysis}
        </Space>
      </Card>
    </div>
  );
};

export default React.memo(OptimizedText2SQL);
