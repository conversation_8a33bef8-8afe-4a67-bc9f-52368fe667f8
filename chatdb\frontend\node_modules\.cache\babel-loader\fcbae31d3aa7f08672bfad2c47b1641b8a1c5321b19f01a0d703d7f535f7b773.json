{"ast": null, "code": "import { caseSensitiveTransform } from './case-sensitive-transform.js';\n\n/**\n * @param {Record<string, string>} attributes\n * @param {string} property\n * @returns {string}\n */\nexport function caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase());\n}", "map": {"version": 3, "names": ["caseSensitiveTransform", "caseInsensitiveTransform", "attributes", "property", "toLowerCase"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/react-markdown/node_modules/property-information/lib/util/case-insensitive-transform.js"], "sourcesContent": ["import {caseSensitiveTransform} from './case-sensitive-transform.js'\n\n/**\n * @param {Record<string, string>} attributes\n * @param {string} property\n * @returns {string}\n */\nexport function caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n"], "mappings": "AAAA,SAAQA,sBAAsB,QAAO,+BAA+B;;AAEpE;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EAC7D,OAAOH,sBAAsB,CAACE,UAAU,EAAEC,QAAQ,CAACC,WAAW,CAAC,CAAC,CAAC;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}