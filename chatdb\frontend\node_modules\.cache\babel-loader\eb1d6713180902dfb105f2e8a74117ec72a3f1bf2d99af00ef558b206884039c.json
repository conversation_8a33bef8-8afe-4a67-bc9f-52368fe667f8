{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileExclamationTwoToneSvg from \"@ant-design/icons-svg/es/asn/FileExclamationTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileExclamationTwoTone = function FileExclamationTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileExclamationTwoToneSvg\n  }));\n};\n\n/**![file-exclamation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUzNCAzNTJWMTM2SDIzMnY3NTJoNTYwVjM5NEg1NzZhNDIgNDIgMCAwMS00Mi00MnptLTU0IDk2YzAtNC40IDMuNi04IDgtOGg0OGM0LjQgMCA4IDMuNiA4IDh2MTg0YzAgNC40LTMuNiA4LTggOGgtNDhjLTQuNCAwLTgtMy42LTgtOFY0NDh6bTMyIDMzNmMtMjIuMSAwLTQwLTE3LjktNDAtNDBzMTcuOS00MCA0MC00MCA0MCAxNy45IDQwIDQwLTE3LjkgNDAtNDAgNDB6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04NTQuNiAyODguNkw2MzkuNCA3My40Yy02LTYtMTQuMS05LjQtMjIuNi05LjRIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY4MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjMxMS4zYzAtOC41LTMuNC0xNi43LTkuNC0yMi43ek02MDIgMTM3LjhMNzkwLjIgMzI2SDYwMlYxMzcuOHpNNzkyIDg4OEgyMzJWMTM2aDMwMnYyMTZhNDIgNDIgMCAwMDQyIDQyaDIxNnY0OTR6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik00ODggNjQwaDQ4YzQuNCAwIDgtMy42IDgtOFY0NDhjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4djE4NGMwIDQuNCAzLjYgOCA4IDh6bS0xNiAxMDRhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAweiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileExclamationTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileExclamationTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FileExclamationTwoToneSvg", "AntdIcon", "FileExclamationTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/FileExclamationTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileExclamationTwoToneSvg from \"@ant-design/icons-svg/es/asn/FileExclamationTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileExclamationTwoTone = function FileExclamationTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileExclamationTwoToneSvg\n  }));\n};\n\n/**![file-exclamation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUzNCAzNTJWMTM2SDIzMnY3NTJoNTYwVjM5NEg1NzZhNDIgNDIgMCAwMS00Mi00MnptLTU0IDk2YzAtNC40IDMuNi04IDgtOGg0OGM0LjQgMCA4IDMuNiA4IDh2MTg0YzAgNC40LTMuNiA4LTggOGgtNDhjLTQuNCAwLTgtMy42LTgtOFY0NDh6bTMyIDMzNmMtMjIuMSAwLTQwLTE3LjktNDAtNDBzMTcuOS00MCA0MC00MCA0MCAxNy45IDQwIDQwLTE3LjkgNDAtNDAgNDB6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04NTQuNiAyODguNkw2MzkuNCA3My40Yy02LTYtMTQuMS05LjQtMjIuNi05LjRIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY4MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjMxMS4zYzAtOC41LTMuNC0xNi43LTkuNC0yMi43ek02MDIgMTM3LjhMNzkwLjIgMzI2SDYwMlYxMzcuOHpNNzkyIDg4OEgyMzJWMTM2aDMwMnYyMTZhNDIgNDIgMCAwMDQyIDQyaDIxNnY0OTR6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik00ODggNjQwaDQ4YzQuNCAwIDgtMy42IDgtOFY0NDhjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4djE4NGMwIDQuNCAzLjYgOCA4IDh6bS0xNiAxMDRhNDAgNDAgMCAxMDgwIDAgNDAgNDAgMCAxMC04MCAweiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileExclamationTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileExclamationTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,sBAAsB,CAAC;AACnE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,wBAAwB;AAChD;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}