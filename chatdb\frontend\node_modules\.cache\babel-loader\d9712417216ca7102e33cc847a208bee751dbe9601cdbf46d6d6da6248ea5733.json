{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PullRequestOutlinedSvg from \"@ant-design/icons-svg/es/asn/PullRequestOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PullRequestOutlined = function PullRequestOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PullRequestOutlinedSvg\n  }));\n};\n\n/**![pull-request](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc4OCA3MDUuOVYxOTJjMC04LjgtNy4yLTE2LTE2LTE2SDYwMnYtNjguOGMwLTYtNy05LjQtMTEuNy01LjdMNDYyLjcgMjAyLjNhNy4xNCA3LjE0IDAgMDAwIDExLjNsMTI3LjUgMTAwLjhjNC43IDMuNyAxMS43LjQgMTEuNy01LjdWMjQwaDExNHY0NjUuOWMtNDQuMiAxNS03NiA1Ni45LTc2IDEwNi4xIDAgNjEuOCA1MC4yIDExMiAxMTIgMTEyczExMi01MC4yIDExMi0xMTJjLjEtNDkuMi0zMS43LTkxLTc1LjktMTA2LjF6TTc1MiA4NjBhNDguMDEgNDguMDEgMCAwMTAtOTYgNDguMDEgNDguMDEgMCAwMTAgOTZ6TTM4NCAyMTJjMC02MS44LTUwLjItMTEyLTExMi0xMTJzLTExMiA1MC4yLTExMiAxMTJjMCA0OS4yIDMxLjggOTEgNzYgMTA2LjFWNzA2Yy00NC4yIDE1LTc2IDU2LjktNzYgMTA2LjEgMCA2MS44IDUwLjIgMTEyIDExMiAxMTJzMTEyLTUwLjIgMTEyLTExMmMwLTQ5LjItMzEuOC05MS03Ni0xMDYuMVYzMTguMWM0NC4yLTE1LjEgNzYtNTYuOSA3Ni0xMDYuMXptLTE2MCAwYTQ4LjAxIDQ4LjAxIDAgMDE5NiAwIDQ4LjAxIDQ4LjAxIDAgMDEtOTYgMHptOTYgNjAwYTQ4LjAxIDQ4LjAxIDAgMDEtOTYgMCA0OC4wMSA0OC4wMSAwIDAxOTYgMHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PullRequestOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PullRequestOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "PullRequestOutlinedSvg", "AntdIcon", "PullRequestOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/PullRequestOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PullRequestOutlinedSvg from \"@ant-design/icons-svg/es/asn/PullRequestOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PullRequestOutlined = function PullRequestOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PullRequestOutlinedSvg\n  }));\n};\n\n/**![pull-request](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc4OCA3MDUuOVYxOTJjMC04LjgtNy4yLTE2LTE2LTE2SDYwMnYtNjguOGMwLTYtNy05LjQtMTEuNy01LjdMNDYyLjcgMjAyLjNhNy4xNCA3LjE0IDAgMDAwIDExLjNsMTI3LjUgMTAwLjhjNC43IDMuNyAxMS43LjQgMTEuNy01LjdWMjQwaDExNHY0NjUuOWMtNDQuMiAxNS03NiA1Ni45LTc2IDEwNi4xIDAgNjEuOCA1MC4yIDExMiAxMTIgMTEyczExMi01MC4yIDExMi0xMTJjLjEtNDkuMi0zMS43LTkxLTc1LjktMTA2LjF6TTc1MiA4NjBhNDguMDEgNDguMDEgMCAwMTAtOTYgNDguMDEgNDguMDEgMCAwMTAgOTZ6TTM4NCAyMTJjMC02MS44LTUwLjItMTEyLTExMi0xMTJzLTExMiA1MC4yLTExMiAxMTJjMCA0OS4yIDMxLjggOTEgNzYgMTA2LjFWNzA2Yy00NC4yIDE1LTc2IDU2LjktNzYgMTA2LjEgMCA2MS44IDUwLjIgMTEyIDExMiAxMTJzMTEyLTUwLjIgMTEyLTExMmMwLTQ5LjItMzEuOC05MS03Ni0xMDYuMVYzMTguMWM0NC4yLTE1LjEgNzYtNTYuOSA3Ni0xMDYuMXptLTE2MCAwYTQ4LjAxIDQ4LjAxIDAgMDE5NiAwIDQ4LjAxIDQ4LjAxIDAgMDEtOTYgMHptOTYgNjAwYTQ4LjAxIDQ4LjAxIDAgMDEtOTYgMCA0OC4wMSA0OC4wMSAwIDAxOTYgMHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PullRequestOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PullRequestOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,mBAAmB,CAAC;AAChE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,qBAAqB;AAC7C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}