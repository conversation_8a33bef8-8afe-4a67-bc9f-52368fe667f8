{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport ConfigProvider, { ConfigContext } from '../config-provider';\nexport function withPureRenderTheme(Component) {\n  return props => (/*#__PURE__*/React.createElement(ConfigProvider, {\n    theme: {\n      token: {\n        motion: false,\n        zIndexPopupBase: 0\n      }\n    }\n  }, /*#__PURE__*/React.createElement(Component, Object.assign({}, props))));\n}\n/* istanbul ignore next */\nconst genPurePanel = (Component, alignPropName, postProps, defaultPrefixCls, getDropdownCls) => {\n  const PurePanel = props => {\n    const {\n      prefixCls: customizePrefixCls,\n      style\n    } = props;\n    const holderRef = React.useRef(null);\n    const [popupHeight, setPopupHeight] = React.useState(0);\n    const [popupWidth, setPopupWidth] = React.useState(0);\n    const [open, setOpen] = useMergedState(false, {\n      value: props.open\n    });\n    const {\n      getPrefixCls\n    } = React.useContext(ConfigContext);\n    const prefixCls = getPrefixCls(defaultPrefixCls || 'select', customizePrefixCls);\n    React.useEffect(() => {\n      // We do not care about ssr\n      setOpen(true);\n      if (typeof ResizeObserver !== 'undefined') {\n        const resizeObserver = new ResizeObserver(entries => {\n          const element = entries[0].target;\n          setPopupHeight(element.offsetHeight + 8);\n          setPopupWidth(element.offsetWidth);\n        });\n        const interval = setInterval(() => {\n          var _a;\n          const dropdownCls = getDropdownCls ? `.${getDropdownCls(prefixCls)}` : `.${prefixCls}-dropdown`;\n          const popup = (_a = holderRef.current) === null || _a === void 0 ? void 0 : _a.querySelector(dropdownCls);\n          if (popup) {\n            clearInterval(interval);\n            resizeObserver.observe(popup);\n          }\n        }, 10);\n        return () => {\n          clearInterval(interval);\n          resizeObserver.disconnect();\n        };\n      }\n    }, []);\n    let mergedProps = Object.assign(Object.assign({}, props), {\n      style: Object.assign(Object.assign({}, style), {\n        margin: 0\n      }),\n      open,\n      visible: open,\n      getPopupContainer: () => holderRef.current\n    });\n    if (postProps) {\n      mergedProps = postProps(mergedProps);\n    }\n    if (alignPropName) {\n      Object.assign(mergedProps, {\n        [alignPropName]: {\n          overflow: {\n            adjustX: false,\n            adjustY: false\n          }\n        }\n      });\n    }\n    const mergedStyle = {\n      paddingBottom: popupHeight,\n      position: 'relative',\n      minWidth: popupWidth\n    };\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: holderRef,\n      style: mergedStyle\n    }, /*#__PURE__*/React.createElement(Component, Object.assign({}, mergedProps)));\n  };\n  return withPureRenderTheme(PurePanel);\n};\nexport default genPurePanel;", "map": {"version": 3, "names": ["React", "useMergedState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ConfigContext", "withPureRenderTheme", "Component", "props", "createElement", "theme", "token", "motion", "zIndexPopupBase", "Object", "assign", "genPurePanel", "alignPropName", "postProps", "defaultPrefixCls", "getDropdownCls", "PurePanel", "prefixCls", "customizePrefixCls", "style", "holder<PERSON><PERSON>", "useRef", "popupHeight", "setPopupHeight", "useState", "popup<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "open", "<PERSON><PERSON><PERSON>", "value", "getPrefixCls", "useContext", "useEffect", "ResizeObserver", "resizeObserver", "entries", "element", "target", "offsetHeight", "offsetWidth", "interval", "setInterval", "_a", "dropdownCls", "popup", "current", "querySelector", "clearInterval", "observe", "disconnect", "mergedProps", "margin", "visible", "getPopupContainer", "overflow", "adjustX", "adjustY", "mergedStyle", "paddingBottom", "position", "min<PERSON><PERSON><PERSON>", "ref"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/_util/PurePanel.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport ConfigProvider, { ConfigContext } from '../config-provider';\nexport function withPureRenderTheme(Component) {\n  return props => (/*#__PURE__*/React.createElement(ConfigProvider, {\n    theme: {\n      token: {\n        motion: false,\n        zIndexPopupBase: 0\n      }\n    }\n  }, /*#__PURE__*/React.createElement(Component, Object.assign({}, props))));\n}\n/* istanbul ignore next */\nconst genPurePanel = (Component, alignPropName, postProps, defaultPrefixCls, getDropdownCls) => {\n  const PurePanel = props => {\n    const {\n      prefixCls: customizePrefixCls,\n      style\n    } = props;\n    const holderRef = React.useRef(null);\n    const [popupHeight, setPopupHeight] = React.useState(0);\n    const [popupWidth, setPopupWidth] = React.useState(0);\n    const [open, setOpen] = useMergedState(false, {\n      value: props.open\n    });\n    const {\n      getPrefixCls\n    } = React.useContext(ConfigContext);\n    const prefixCls = getPrefixCls(defaultPrefixCls || 'select', customizePrefixCls);\n    React.useEffect(() => {\n      // We do not care about ssr\n      setOpen(true);\n      if (typeof ResizeObserver !== 'undefined') {\n        const resizeObserver = new ResizeObserver(entries => {\n          const element = entries[0].target;\n          setPopupHeight(element.offsetHeight + 8);\n          setPopupWidth(element.offsetWidth);\n        });\n        const interval = setInterval(() => {\n          var _a;\n          const dropdownCls = getDropdownCls ? `.${getDropdownCls(prefixCls)}` : `.${prefixCls}-dropdown`;\n          const popup = (_a = holderRef.current) === null || _a === void 0 ? void 0 : _a.querySelector(dropdownCls);\n          if (popup) {\n            clearInterval(interval);\n            resizeObserver.observe(popup);\n          }\n        }, 10);\n        return () => {\n          clearInterval(interval);\n          resizeObserver.disconnect();\n        };\n      }\n    }, []);\n    let mergedProps = Object.assign(Object.assign({}, props), {\n      style: Object.assign(Object.assign({}, style), {\n        margin: 0\n      }),\n      open,\n      visible: open,\n      getPopupContainer: () => holderRef.current\n    });\n    if (postProps) {\n      mergedProps = postProps(mergedProps);\n    }\n    if (alignPropName) {\n      Object.assign(mergedProps, {\n        [alignPropName]: {\n          overflow: {\n            adjustX: false,\n            adjustY: false\n          }\n        }\n      });\n    }\n    const mergedStyle = {\n      paddingBottom: popupHeight,\n      position: 'relative',\n      minWidth: popupWidth\n    };\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: holderRef,\n      style: mergedStyle\n    }, /*#__PURE__*/React.createElement(Component, Object.assign({}, mergedProps)));\n  };\n  return withPureRenderTheme(PurePanel);\n};\nexport default genPurePanel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,cAAc,IAAIC,aAAa,QAAQ,oBAAoB;AAClE,OAAO,SAASC,mBAAmBA,CAACC,SAAS,EAAE;EAC7C,OAAOC,KAAK,KAAK,aAAaN,KAAK,CAACO,aAAa,CAACL,cAAc,EAAE;IAChEM,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,MAAM,EAAE,KAAK;QACbC,eAAe,EAAE;MACnB;IACF;EACF,CAAC,EAAE,aAAaX,KAAK,CAACO,aAAa,CAACF,SAAS,EAAEO,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5E;AACA;AACA,MAAMQ,YAAY,GAAGA,CAACT,SAAS,EAAEU,aAAa,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,cAAc,KAAK;EAC9F,MAAMC,SAAS,GAAGb,KAAK,IAAI;IACzB,MAAM;MACJc,SAAS,EAAEC,kBAAkB;MAC7BC;IACF,CAAC,GAAGhB,KAAK;IACT,MAAMiB,SAAS,GAAGvB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;IACpC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,KAAK,CAAC2B,QAAQ,CAAC,CAAC,CAAC;IACvD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7B,KAAK,CAAC2B,QAAQ,CAAC,CAAC,CAAC;IACrD,MAAM,CAACG,IAAI,EAAEC,OAAO,CAAC,GAAG9B,cAAc,CAAC,KAAK,EAAE;MAC5C+B,KAAK,EAAE1B,KAAK,CAACwB;IACf,CAAC,CAAC;IACF,MAAM;MACJG;IACF,CAAC,GAAGjC,KAAK,CAACkC,UAAU,CAAC/B,aAAa,CAAC;IACnC,MAAMiB,SAAS,GAAGa,YAAY,CAAChB,gBAAgB,IAAI,QAAQ,EAAEI,kBAAkB,CAAC;IAChFrB,KAAK,CAACmC,SAAS,CAAC,MAAM;MACpB;MACAJ,OAAO,CAAC,IAAI,CAAC;MACb,IAAI,OAAOK,cAAc,KAAK,WAAW,EAAE;QACzC,MAAMC,cAAc,GAAG,IAAID,cAAc,CAACE,OAAO,IAAI;UACnD,MAAMC,OAAO,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACE,MAAM;UACjCd,cAAc,CAACa,OAAO,CAACE,YAAY,GAAG,CAAC,CAAC;UACxCZ,aAAa,CAACU,OAAO,CAACG,WAAW,CAAC;QACpC,CAAC,CAAC;QACF,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;UACjC,IAAIC,EAAE;UACN,MAAMC,WAAW,GAAG5B,cAAc,GAAG,IAAIA,cAAc,CAACE,SAAS,CAAC,EAAE,GAAG,IAAIA,SAAS,WAAW;UAC/F,MAAM2B,KAAK,GAAG,CAACF,EAAE,GAAGtB,SAAS,CAACyB,OAAO,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACI,aAAa,CAACH,WAAW,CAAC;UACzG,IAAIC,KAAK,EAAE;YACTG,aAAa,CAACP,QAAQ,CAAC;YACvBN,cAAc,CAACc,OAAO,CAACJ,KAAK,CAAC;UAC/B;QACF,CAAC,EAAE,EAAE,CAAC;QACN,OAAO,MAAM;UACXG,aAAa,CAACP,QAAQ,CAAC;UACvBN,cAAc,CAACe,UAAU,CAAC,CAAC;QAC7B,CAAC;MACH;IACF,CAAC,EAAE,EAAE,CAAC;IACN,IAAIC,WAAW,GAAGzC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,KAAK,CAAC,EAAE;MACxDgB,KAAK,EAAEV,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAES,KAAK,CAAC,EAAE;QAC7CgC,MAAM,EAAE;MACV,CAAC,CAAC;MACFxB,IAAI;MACJyB,OAAO,EAAEzB,IAAI;MACb0B,iBAAiB,EAAEA,CAAA,KAAMjC,SAAS,CAACyB;IACrC,CAAC,CAAC;IACF,IAAIhC,SAAS,EAAE;MACbqC,WAAW,GAAGrC,SAAS,CAACqC,WAAW,CAAC;IACtC;IACA,IAAItC,aAAa,EAAE;MACjBH,MAAM,CAACC,MAAM,CAACwC,WAAW,EAAE;QACzB,CAACtC,aAAa,GAAG;UACf0C,QAAQ,EAAE;YACRC,OAAO,EAAE,KAAK;YACdC,OAAO,EAAE;UACX;QACF;MACF,CAAC,CAAC;IACJ;IACA,MAAMC,WAAW,GAAG;MAClBC,aAAa,EAAEpC,WAAW;MAC1BqC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAEnC;IACZ,CAAC;IACD,OAAO,aAAa5B,KAAK,CAACO,aAAa,CAAC,KAAK,EAAE;MAC7CyD,GAAG,EAAEzC,SAAS;MACdD,KAAK,EAAEsC;IACT,CAAC,EAAE,aAAa5D,KAAK,CAACO,aAAa,CAACF,SAAS,EAAEO,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEwC,WAAW,CAAC,CAAC,CAAC;EACjF,CAAC;EACD,OAAOjD,mBAAmB,CAACe,SAAS,CAAC;AACvC,CAAC;AACD,eAAeL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}