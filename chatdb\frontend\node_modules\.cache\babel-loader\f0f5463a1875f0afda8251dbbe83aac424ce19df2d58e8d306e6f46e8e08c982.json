{"ast": null, "code": "export class Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   */\n  constructor(property, attribute) {\n    /** @type {string} */\n    this.property = property;\n    /** @type {string} */\n    this.attribute = attribute;\n  }\n}\n\n/** @type {string|null} */\nInfo.prototype.space = null;\nInfo.prototype.boolean = false;\nInfo.prototype.booleanish = false;\nInfo.prototype.overloadedBoolean = false;\nInfo.prototype.number = false;\nInfo.prototype.commaSeparated = false;\nInfo.prototype.spaceSeparated = false;\nInfo.prototype.commaOrSpaceSeparated = false;\nInfo.prototype.mustUseProperty = false;\nInfo.prototype.defined = false;", "map": {"version": 3, "names": ["Info", "constructor", "property", "attribute", "prototype", "space", "boolean", "booleanish", "overloadedBoolean", "number", "commaSeparated", "spaceSeparated", "commaOrSpaceSeparated", "mustUseProperty", "defined"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/react-markdown/node_modules/property-information/lib/util/info.js"], "sourcesContent": ["export class Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   */\n  constructor(property, attribute) {\n    /** @type {string} */\n    this.property = property\n    /** @type {string} */\n    this.attribute = attribute\n  }\n}\n\n/** @type {string|null} */\nInfo.prototype.space = null\nInfo.prototype.boolean = false\nInfo.prototype.booleanish = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.number = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.spaceSeparated = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.defined = false\n"], "mappings": "AAAA,OAAO,MAAMA,IAAI,CAAC;EAChB;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACC,QAAQ,EAAEC,SAAS,EAAE;IAC/B;IACA,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC5B;AACF;;AAEA;AACAH,IAAI,CAACI,SAAS,CAACC,KAAK,GAAG,IAAI;AAC3BL,IAAI,CAACI,SAAS,CAACE,OAAO,GAAG,KAAK;AAC9BN,IAAI,CAACI,SAAS,CAACG,UAAU,GAAG,KAAK;AACjCP,IAAI,CAACI,SAAS,CAACI,iBAAiB,GAAG,KAAK;AACxCR,IAAI,CAACI,SAAS,CAACK,MAAM,GAAG,KAAK;AAC7BT,IAAI,CAACI,SAAS,CAACM,cAAc,GAAG,KAAK;AACrCV,IAAI,CAACI,SAAS,CAACO,cAAc,GAAG,KAAK;AACrCX,IAAI,CAACI,SAAS,CAACQ,qBAAqB,GAAG,KAAK;AAC5CZ,IAAI,CAACI,SAAS,CAACS,eAAe,GAAG,KAAK;AACtCb,IAAI,CAACI,SAAS,CAACU,OAAO,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}