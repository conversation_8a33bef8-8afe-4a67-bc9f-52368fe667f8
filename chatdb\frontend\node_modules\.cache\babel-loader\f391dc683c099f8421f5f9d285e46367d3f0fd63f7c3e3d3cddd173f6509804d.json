{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport DescriptionsContext from './DescriptionsContext';\nfunction notEmpty(val) {\n  return val !== undefined && val !== null;\n}\nconst Cell = props => {\n  const {\n    itemPrefixCls,\n    component,\n    span,\n    className,\n    style,\n    labelStyle,\n    contentStyle,\n    bordered,\n    label,\n    content,\n    colon,\n    type,\n    styles\n  } = props;\n  const Component = component;\n  const descContext = React.useContext(DescriptionsContext);\n  const {\n    classNames: descriptionsClassNames\n  } = descContext;\n  if (bordered) {\n    return /*#__PURE__*/React.createElement(Component, {\n      className: classNames({\n        [`${itemPrefixCls}-item-label`]: type === 'label',\n        [`${itemPrefixCls}-item-content`]: type === 'content',\n        [`${descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.label}`]: type === 'label',\n        [`${descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.content}`]: type === 'content'\n      }, className),\n      style: style,\n      colSpan: span\n    }, notEmpty(label) && /*#__PURE__*/React.createElement(\"span\", {\n      style: Object.assign(Object.assign({}, labelStyle), styles === null || styles === void 0 ? void 0 : styles.label)\n    }, label), notEmpty(content) && /*#__PURE__*/React.createElement(\"span\", {\n      style: Object.assign(Object.assign({}, labelStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n    }, content));\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: classNames(`${itemPrefixCls}-item`, className),\n    style: style,\n    colSpan: span\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${itemPrefixCls}-item-container`\n  }, (label || label === 0) && (/*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(`${itemPrefixCls}-item-label`, descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.label, {\n      [`${itemPrefixCls}-item-no-colon`]: !colon\n    }),\n    style: Object.assign(Object.assign({}, labelStyle), styles === null || styles === void 0 ? void 0 : styles.label)\n  }, label)), (content || content === 0) && (/*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(`${itemPrefixCls}-item-content`, descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.content),\n    style: Object.assign(Object.assign({}, contentStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n  }, content))));\n};\nexport default Cell;", "map": {"version": 3, "names": ["React", "classNames", "DescriptionsContext", "notEmpty", "val", "undefined", "Cell", "props", "itemPrefixCls", "component", "span", "className", "style", "labelStyle", "contentStyle", "bordered", "label", "content", "colon", "type", "styles", "Component", "descContext", "useContext", "descriptionsClassNames", "createElement", "colSpan", "Object", "assign"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/descriptions/Cell.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport DescriptionsContext from './DescriptionsContext';\nfunction notEmpty(val) {\n  return val !== undefined && val !== null;\n}\nconst Cell = props => {\n  const {\n    itemPrefixCls,\n    component,\n    span,\n    className,\n    style,\n    labelStyle,\n    contentStyle,\n    bordered,\n    label,\n    content,\n    colon,\n    type,\n    styles\n  } = props;\n  const Component = component;\n  const descContext = React.useContext(DescriptionsContext);\n  const {\n    classNames: descriptionsClassNames\n  } = descContext;\n  if (bordered) {\n    return /*#__PURE__*/React.createElement(Component, {\n      className: classNames({\n        [`${itemPrefixCls}-item-label`]: type === 'label',\n        [`${itemPrefixCls}-item-content`]: type === 'content',\n        [`${descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.label}`]: type === 'label',\n        [`${descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.content}`]: type === 'content'\n      }, className),\n      style: style,\n      colSpan: span\n    }, notEmpty(label) && /*#__PURE__*/React.createElement(\"span\", {\n      style: Object.assign(Object.assign({}, labelStyle), styles === null || styles === void 0 ? void 0 : styles.label)\n    }, label), notEmpty(content) && /*#__PURE__*/React.createElement(\"span\", {\n      style: Object.assign(Object.assign({}, labelStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n    }, content));\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: classNames(`${itemPrefixCls}-item`, className),\n    style: style,\n    colSpan: span\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${itemPrefixCls}-item-container`\n  }, (label || label === 0) && (/*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(`${itemPrefixCls}-item-label`, descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.label, {\n      [`${itemPrefixCls}-item-no-colon`]: !colon\n    }),\n    style: Object.assign(Object.assign({}, labelStyle), styles === null || styles === void 0 ? void 0 : styles.label)\n  }, label)), (content || content === 0) && (/*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(`${itemPrefixCls}-item-content`, descriptionsClassNames === null || descriptionsClassNames === void 0 ? void 0 : descriptionsClassNames.content),\n    style: Object.assign(Object.assign({}, contentStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n  }, content))));\n};\nexport default Cell;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,OAAOA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI;AAC1C;AACA,MAAME,IAAI,GAAGC,KAAK,IAAI;EACpB,MAAM;IACJC,aAAa;IACbC,SAAS;IACTC,IAAI;IACJC,SAAS;IACTC,KAAK;IACLC,UAAU;IACVC,YAAY;IACZC,QAAQ;IACRC,KAAK;IACLC,OAAO;IACPC,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,GAAGb,KAAK;EACT,MAAMc,SAAS,GAAGZ,SAAS;EAC3B,MAAMa,WAAW,GAAGtB,KAAK,CAACuB,UAAU,CAACrB,mBAAmB,CAAC;EACzD,MAAM;IACJD,UAAU,EAAEuB;EACd,CAAC,GAAGF,WAAW;EACf,IAAIP,QAAQ,EAAE;IACZ,OAAO,aAAaf,KAAK,CAACyB,aAAa,CAACJ,SAAS,EAAE;MACjDV,SAAS,EAAEV,UAAU,CAAC;QACpB,CAAC,GAAGO,aAAa,aAAa,GAAGW,IAAI,KAAK,OAAO;QACjD,CAAC,GAAGX,aAAa,eAAe,GAAGW,IAAI,KAAK,SAAS;QACrD,CAAC,GAAGK,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACR,KAAK,EAAE,GAAGG,IAAI,KAAK,OAAO;QACrI,CAAC,GAAGK,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACP,OAAO,EAAE,GAAGE,IAAI,KAAK;MAClI,CAAC,EAAER,SAAS,CAAC;MACbC,KAAK,EAAEA,KAAK;MACZc,OAAO,EAAEhB;IACX,CAAC,EAAEP,QAAQ,CAACa,KAAK,CAAC,IAAI,aAAahB,KAAK,CAACyB,aAAa,CAAC,MAAM,EAAE;MAC7Db,KAAK,EAAEe,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,UAAU,CAAC,EAAEO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACJ,KAAK;IAClH,CAAC,EAAEA,KAAK,CAAC,EAAEb,QAAQ,CAACc,OAAO,CAAC,IAAI,aAAajB,KAAK,CAACyB,aAAa,CAAC,MAAM,EAAE;MACvEb,KAAK,EAAEe,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,UAAU,CAAC,EAAEO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACH,OAAO;IACpH,CAAC,EAAEA,OAAO,CAAC,CAAC;EACd;EACA,OAAO,aAAajB,KAAK,CAACyB,aAAa,CAACJ,SAAS,EAAE;IACjDV,SAAS,EAAEV,UAAU,CAAC,GAAGO,aAAa,OAAO,EAAEG,SAAS,CAAC;IACzDC,KAAK,EAAEA,KAAK;IACZc,OAAO,EAAEhB;EACX,CAAC,EAAE,aAAaV,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAE;IACzCd,SAAS,EAAE,GAAGH,aAAa;EAC7B,CAAC,EAAE,CAACQ,KAAK,IAAIA,KAAK,KAAK,CAAC,MAAM,aAAahB,KAAK,CAACyB,aAAa,CAAC,MAAM,EAAE;IACrEd,SAAS,EAAEV,UAAU,CAAC,GAAGO,aAAa,aAAa,EAAEgB,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACR,KAAK,EAAE;MACjK,CAAC,GAAGR,aAAa,gBAAgB,GAAG,CAACU;IACvC,CAAC,CAAC;IACFN,KAAK,EAAEe,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,UAAU,CAAC,EAAEO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACJ,KAAK;EAClH,CAAC,EAAEA,KAAK,CAAC,CAAC,EAAE,CAACC,OAAO,IAAIA,OAAO,KAAK,CAAC,MAAM,aAAajB,KAAK,CAACyB,aAAa,CAAC,MAAM,EAAE;IAClFd,SAAS,EAAEV,UAAU,CAAC,GAAGO,aAAa,eAAe,EAAEgB,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACP,OAAO,CAAC;IACtKL,KAAK,EAAEe,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEd,YAAY,CAAC,EAAEM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACH,OAAO;EACtH,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;AAChB,CAAC;AACD,eAAeX,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}