{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport useState from \"rc-util/es/hooks/useState\";\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/buttonHelpers';\nfunction isThenable(thing) {\n  return !!(thing === null || thing === void 0 ? void 0 : thing.then);\n}\nconst ActionButton = props => {\n  const {\n    type,\n    children,\n    prefixCls,\n    buttonProps,\n    close,\n    autoFocus,\n    emitEvent,\n    isSilent,\n    quitOnNullishReturnValue,\n    actionFn\n  } = props;\n  const clickedRef = React.useRef(false);\n  const buttonRef = React.useRef(null);\n  const [loading, setLoading] = useState(false);\n  const onInternalClose = function () {\n    close === null || close === void 0 ? void 0 : close.apply(void 0, arguments);\n  };\n  React.useEffect(() => {\n    let timeoutId = null;\n    if (autoFocus) {\n      timeoutId = setTimeout(() => {\n        var _a;\n        (_a = buttonRef.current) === null || _a === void 0 ? void 0 : _a.focus({\n          preventScroll: true\n        });\n      });\n    }\n    return () => {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n  }, []);\n  const handlePromiseOnOk = returnValueOfOnOk => {\n    if (!isThenable(returnValueOfOnOk)) {\n      return;\n    }\n    setLoading(true);\n    returnValueOfOnOk.then(function () {\n      setLoading(false, true);\n      onInternalClose.apply(void 0, arguments);\n      clickedRef.current = false;\n    }, e => {\n      // See: https://github.com/ant-design/ant-design/issues/6183\n      setLoading(false, true);\n      clickedRef.current = false;\n      // Do not throw if is `await` mode\n      if (isSilent === null || isSilent === void 0 ? void 0 : isSilent()) {\n        return;\n      }\n      return Promise.reject(e);\n    });\n  };\n  const onClick = e => {\n    if (clickedRef.current) {\n      return;\n    }\n    clickedRef.current = true;\n    if (!actionFn) {\n      onInternalClose();\n      return;\n    }\n    let returnValueOfOnOk;\n    if (emitEvent) {\n      returnValueOfOnOk = actionFn(e);\n      if (quitOnNullishReturnValue && !isThenable(returnValueOfOnOk)) {\n        clickedRef.current = false;\n        onInternalClose(e);\n        return;\n      }\n    } else if (actionFn.length) {\n      returnValueOfOnOk = actionFn(close);\n      // https://github.com/ant-design/ant-design/issues/23358\n      clickedRef.current = false;\n    } else {\n      returnValueOfOnOk = actionFn();\n      if (!isThenable(returnValueOfOnOk)) {\n        onInternalClose();\n        return;\n      }\n    }\n    handlePromiseOnOk(returnValueOfOnOk);\n  };\n  return /*#__PURE__*/React.createElement(Button, Object.assign({}, convertLegacyProps(type), {\n    onClick: onClick,\n    loading: loading,\n    prefixCls: prefixCls\n  }, buttonProps, {\n    ref: buttonRef\n  }), children);\n};\nexport default ActionButton;", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "convertLegacyProps", "isThenable", "thing", "then", "ActionButton", "props", "type", "children", "prefixCls", "buttonProps", "close", "autoFocus", "emitEvent", "isSilent", "quitOnNullishReturnValue", "actionFn", "clickedRef", "useRef", "buttonRef", "loading", "setLoading", "onInternalClose", "apply", "arguments", "useEffect", "timeoutId", "setTimeout", "_a", "current", "focus", "preventScroll", "clearTimeout", "handlePromiseOnOk", "returnValueOfOnOk", "e", "Promise", "reject", "onClick", "length", "createElement", "Object", "assign", "ref"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/_util/ActionButton.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport useState from \"rc-util/es/hooks/useState\";\nimport Button from '../button';\nimport { convertLegacyProps } from '../button/buttonHelpers';\nfunction isThenable(thing) {\n  return !!(thing === null || thing === void 0 ? void 0 : thing.then);\n}\nconst ActionButton = props => {\n  const {\n    type,\n    children,\n    prefixCls,\n    buttonProps,\n    close,\n    autoFocus,\n    emitEvent,\n    isSilent,\n    quitOnNullishReturnValue,\n    actionFn\n  } = props;\n  const clickedRef = React.useRef(false);\n  const buttonRef = React.useRef(null);\n  const [loading, setLoading] = useState(false);\n  const onInternalClose = function () {\n    close === null || close === void 0 ? void 0 : close.apply(void 0, arguments);\n  };\n  React.useEffect(() => {\n    let timeoutId = null;\n    if (autoFocus) {\n      timeoutId = setTimeout(() => {\n        var _a;\n        (_a = buttonRef.current) === null || _a === void 0 ? void 0 : _a.focus({\n          preventScroll: true\n        });\n      });\n    }\n    return () => {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n  }, []);\n  const handlePromiseOnOk = returnValueOfOnOk => {\n    if (!isThenable(returnValueOfOnOk)) {\n      return;\n    }\n    setLoading(true);\n    returnValueOfOnOk.then(function () {\n      setLoading(false, true);\n      onInternalClose.apply(void 0, arguments);\n      clickedRef.current = false;\n    }, e => {\n      // See: https://github.com/ant-design/ant-design/issues/6183\n      setLoading(false, true);\n      clickedRef.current = false;\n      // Do not throw if is `await` mode\n      if (isSilent === null || isSilent === void 0 ? void 0 : isSilent()) {\n        return;\n      }\n      return Promise.reject(e);\n    });\n  };\n  const onClick = e => {\n    if (clickedRef.current) {\n      return;\n    }\n    clickedRef.current = true;\n    if (!actionFn) {\n      onInternalClose();\n      return;\n    }\n    let returnValueOfOnOk;\n    if (emitEvent) {\n      returnValueOfOnOk = actionFn(e);\n      if (quitOnNullishReturnValue && !isThenable(returnValueOfOnOk)) {\n        clickedRef.current = false;\n        onInternalClose(e);\n        return;\n      }\n    } else if (actionFn.length) {\n      returnValueOfOnOk = actionFn(close);\n      // https://github.com/ant-design/ant-design/issues/23358\n      clickedRef.current = false;\n    } else {\n      returnValueOfOnOk = actionFn();\n      if (!isThenable(returnValueOfOnOk)) {\n        onInternalClose();\n        return;\n      }\n    }\n    handlePromiseOnOk(returnValueOfOnOk);\n  };\n  return /*#__PURE__*/React.createElement(Button, Object.assign({}, convertLegacyProps(type), {\n    onClick: onClick,\n    loading: loading,\n    prefixCls: prefixCls\n  }, buttonProps, {\n    ref: buttonRef\n  }), children);\n};\nexport default ActionButton;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,CAAC,EAAEA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,CAAC;AACrE;AACA,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,IAAI;IACJC,QAAQ;IACRC,SAAS;IACTC,WAAW;IACXC,KAAK;IACLC,SAAS;IACTC,SAAS;IACTC,QAAQ;IACRC,wBAAwB;IACxBC;EACF,CAAC,GAAGV,KAAK;EACT,MAAMW,UAAU,GAAGnB,KAAK,CAACoB,MAAM,CAAC,KAAK,CAAC;EACtC,MAAMC,SAAS,GAAGrB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM,CAACE,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMuB,eAAe,GAAG,SAAAA,CAAA,EAAY;IAClCX,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACY,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;EAC9E,CAAC;EACD1B,KAAK,CAAC2B,SAAS,CAAC,MAAM;IACpB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAId,SAAS,EAAE;MACbc,SAAS,GAAGC,UAAU,CAAC,MAAM;QAC3B,IAAIC,EAAE;QACN,CAACA,EAAE,GAAGT,SAAS,CAACU,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,KAAK,CAAC;UACrEC,aAAa,EAAE;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,OAAO,MAAM;MACX,IAAIL,SAAS,EAAE;QACbM,YAAY,CAACN,SAAS,CAAC;MACzB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAMO,iBAAiB,GAAGC,iBAAiB,IAAI;IAC7C,IAAI,CAAChC,UAAU,CAACgC,iBAAiB,CAAC,EAAE;MAClC;IACF;IACAb,UAAU,CAAC,IAAI,CAAC;IAChBa,iBAAiB,CAAC9B,IAAI,CAAC,YAAY;MACjCiB,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;MACvBC,eAAe,CAACC,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;MACxCP,UAAU,CAACY,OAAO,GAAG,KAAK;IAC5B,CAAC,EAAEM,CAAC,IAAI;MACN;MACAd,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;MACvBJ,UAAU,CAACY,OAAO,GAAG,KAAK;MAC1B;MACA,IAAIf,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC,EAAE;QAClE;MACF;MACA,OAAOsB,OAAO,CAACC,MAAM,CAACF,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;EACD,MAAMG,OAAO,GAAGH,CAAC,IAAI;IACnB,IAAIlB,UAAU,CAACY,OAAO,EAAE;MACtB;IACF;IACAZ,UAAU,CAACY,OAAO,GAAG,IAAI;IACzB,IAAI,CAACb,QAAQ,EAAE;MACbM,eAAe,CAAC,CAAC;MACjB;IACF;IACA,IAAIY,iBAAiB;IACrB,IAAIrB,SAAS,EAAE;MACbqB,iBAAiB,GAAGlB,QAAQ,CAACmB,CAAC,CAAC;MAC/B,IAAIpB,wBAAwB,IAAI,CAACb,UAAU,CAACgC,iBAAiB,CAAC,EAAE;QAC9DjB,UAAU,CAACY,OAAO,GAAG,KAAK;QAC1BP,eAAe,CAACa,CAAC,CAAC;QAClB;MACF;IACF,CAAC,MAAM,IAAInB,QAAQ,CAACuB,MAAM,EAAE;MAC1BL,iBAAiB,GAAGlB,QAAQ,CAACL,KAAK,CAAC;MACnC;MACAM,UAAU,CAACY,OAAO,GAAG,KAAK;IAC5B,CAAC,MAAM;MACLK,iBAAiB,GAAGlB,QAAQ,CAAC,CAAC;MAC9B,IAAI,CAACd,UAAU,CAACgC,iBAAiB,CAAC,EAAE;QAClCZ,eAAe,CAAC,CAAC;QACjB;MACF;IACF;IACAW,iBAAiB,CAACC,iBAAiB,CAAC;EACtC,CAAC;EACD,OAAO,aAAapC,KAAK,CAAC0C,aAAa,CAACxC,MAAM,EAAEyC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzC,kBAAkB,CAACM,IAAI,CAAC,EAAE;IAC1F+B,OAAO,EAAEA,OAAO;IAChBlB,OAAO,EAAEA,OAAO;IAChBX,SAAS,EAAEA;EACb,CAAC,EAAEC,WAAW,EAAE;IACdiC,GAAG,EAAExB;EACP,CAAC,CAAC,EAAEX,QAAQ,CAAC;AACf,CAAC;AACD,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}