{"ast": null, "code": "import { Info } from './info.js';\nimport * as types from './types.js';\n\n/** @type {Array<keyof types>} */\n// @ts-expect-error: hush.\nconst checks = Object.keys(types);\nexport class DefinedInfo extends Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   * @param {number|null} [mask]\n   * @param {string} [space]\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1;\n    super(property, attribute);\n    mark(this, 'space', space);\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index];\n        mark(this, checks[index], (mask & types[check]) === types[check]);\n      }\n    }\n  }\n}\nDefinedInfo.prototype.defined = true;\n\n/**\n * @param {DefinedInfo} values\n * @param {string} key\n * @param {unknown} value\n */\nfunction mark(values, key, value) {\n  if (value) {\n    // @ts-expect-error: assume `value` matches the expected value of `key`.\n    values[key] = value;\n  }\n}", "map": {"version": 3, "names": ["Info", "types", "checks", "Object", "keys", "DefinedInfo", "constructor", "property", "attribute", "mask", "space", "index", "mark", "length", "check", "prototype", "defined", "values", "key", "value"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/react-markdown/node_modules/property-information/lib/util/defined-info.js"], "sourcesContent": ["import {Info} from './info.js'\nimport * as types from './types.js'\n\n/** @type {Array<keyof types>} */\n// @ts-expect-error: hush.\nconst checks = Object.keys(types)\n\nexport class DefinedInfo extends Info {\n  /**\n   * @constructor\n   * @param {string} property\n   * @param {string} attribute\n   * @param {number|null} [mask]\n   * @param {string} [space]\n   */\n  constructor(property, attribute, mask, space) {\n    let index = -1\n\n    super(property, attribute)\n\n    mark(this, 'space', space)\n\n    if (typeof mask === 'number') {\n      while (++index < checks.length) {\n        const check = checks[index]\n        mark(this, checks[index], (mask & types[check]) === types[check])\n      }\n    }\n  }\n}\n\nDefinedInfo.prototype.defined = true\n\n/**\n * @param {DefinedInfo} values\n * @param {string} key\n * @param {unknown} value\n */\nfunction mark(values, key, value) {\n  if (value) {\n    // @ts-expect-error: assume `value` matches the expected value of `key`.\n    values[key] = value\n  }\n}\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,WAAW;AAC9B,OAAO,KAAKC,KAAK,MAAM,YAAY;;AAEnC;AACA;AACA,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC;AAEjC,OAAO,MAAMI,WAAW,SAASL,IAAI,CAAC;EACpC;AACF;AACA;AACA;AACA;AACA;AACA;EACEM,WAAWA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAE;IAC5C,IAAIC,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,CAACJ,QAAQ,EAAEC,SAAS,CAAC;IAE1BI,IAAI,CAAC,IAAI,EAAE,OAAO,EAAEF,KAAK,CAAC;IAE1B,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAO,EAAEE,KAAK,GAAGT,MAAM,CAACW,MAAM,EAAE;QAC9B,MAAMC,KAAK,GAAGZ,MAAM,CAACS,KAAK,CAAC;QAC3BC,IAAI,CAAC,IAAI,EAAEV,MAAM,CAACS,KAAK,CAAC,EAAE,CAACF,IAAI,GAAGR,KAAK,CAACa,KAAK,CAAC,MAAMb,KAAK,CAACa,KAAK,CAAC,CAAC;MACnE;IACF;EACF;AACF;AAEAT,WAAW,CAACU,SAAS,CAACC,OAAO,GAAG,IAAI;;AAEpC;AACA;AACA;AACA;AACA;AACA,SAASJ,IAAIA,CAACK,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAChC,IAAIA,KAAK,EAAE;IACT;IACAF,MAAM,CAACC,GAAG,CAAC,GAAGC,KAAK;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}