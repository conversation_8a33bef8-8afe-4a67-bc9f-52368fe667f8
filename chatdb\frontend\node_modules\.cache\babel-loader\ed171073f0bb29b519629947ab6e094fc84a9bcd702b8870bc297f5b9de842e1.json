{"ast": null, "code": "import { resetComponent } from '../../style';\nimport { initZoomMotion } from '../../style/motion';\nimport getArrowStyle, { getArrowOffsetToken } from '../../style/placementArrow';\nimport { getArrowToken } from '../../style/roundedArrow';\nimport { genStyleHooks, mergeToken, PresetColors } from '../../theme/internal';\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    popoverColor,\n    titleMinWidth,\n    fontWeightStrong,\n    innerPadding,\n    boxShadowSecondary,\n    colorTextHeading,\n    borderRadiusLG,\n    zIndexPopup,\n    titleMarginBottom,\n    colorBgElevated,\n    popoverBg,\n    titleBorderBottom,\n    innerContentPadding,\n    titlePadding\n  } = token;\n  return [{\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: 0,\n      // use `left` to fix https://github.com/ant-design/ant-design/issues/39195\n      left: {\n        _skip_check_: true,\n        value: 0\n      },\n      zIndex: zIndexPopup,\n      fontWeight: 'normal',\n      whiteSpace: 'normal',\n      textAlign: 'start',\n      cursor: 'auto',\n      userSelect: 'text',\n      // When use `autoArrow`, origin will follow the arrow position\n      '--valid-offset-x': 'var(--arrow-offset-horizontal, var(--arrow-x))',\n      transformOrigin: [`var(--valid-offset-x, 50%)`, `var(--arrow-y, 50%)`].join(' '),\n      '--antd-arrow-background-color': colorBgElevated,\n      width: 'max-content',\n      maxWidth: '100vw',\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&-hidden': {\n        display: 'none'\n      },\n      [`${componentCls}-content`]: {\n        position: 'relative'\n      },\n      [`${componentCls}-inner`]: {\n        backgroundColor: popoverBg,\n        backgroundClip: 'padding-box',\n        borderRadius: borderRadiusLG,\n        boxShadow: boxShadowSecondary,\n        padding: innerPadding\n      },\n      [`${componentCls}-title`]: {\n        minWidth: titleMinWidth,\n        marginBottom: titleMarginBottom,\n        color: colorTextHeading,\n        fontWeight: fontWeightStrong,\n        borderBottom: titleBorderBottom,\n        padding: titlePadding\n      },\n      [`${componentCls}-inner-content`]: {\n        color: popoverColor,\n        padding: innerContentPadding\n      }\n    })\n  },\n  // Arrow Style\n  getArrowStyle(token, 'var(--antd-arrow-background-color)'),\n  // Pure Render\n  {\n    [`${componentCls}-pure`]: {\n      position: 'relative',\n      maxWidth: 'none',\n      margin: token.sizePopupArrow,\n      display: 'inline-block',\n      [`${componentCls}-content`]: {\n        display: 'inline-block'\n      }\n    }\n  }];\n};\nconst genColorStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: PresetColors.map(colorKey => {\n      const lightColor = token[`${colorKey}6`];\n      return {\n        [`&${componentCls}-${colorKey}`]: {\n          '--antd-arrow-background-color': lightColor,\n          [`${componentCls}-inner`]: {\n            backgroundColor: lightColor\n          },\n          [`${componentCls}-arrow`]: {\n            background: 'transparent'\n          }\n        }\n      };\n    })\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    lineWidth,\n    controlHeight,\n    fontHeight,\n    padding,\n    wireframe,\n    zIndexPopupBase,\n    borderRadiusLG,\n    marginXS,\n    lineType,\n    colorSplit,\n    paddingSM\n  } = token;\n  const titlePaddingBlockDist = controlHeight - fontHeight;\n  const popoverTitlePaddingBlockTop = titlePaddingBlockDist / 2;\n  const popoverTitlePaddingBlockBottom = titlePaddingBlockDist / 2 - lineWidth;\n  const popoverPaddingHorizontal = padding;\n  return Object.assign(Object.assign(Object.assign({\n    titleMinWidth: 177,\n    zIndexPopup: zIndexPopupBase + 30\n  }, getArrowToken(token)), getArrowOffsetToken({\n    contentRadius: borderRadiusLG,\n    limitVerticalRadius: true\n  })), {\n    // internal\n    innerPadding: wireframe ? 0 : 12,\n    titleMarginBottom: wireframe ? 0 : marginXS,\n    titlePadding: wireframe ? `${popoverTitlePaddingBlockTop}px ${popoverPaddingHorizontal}px ${popoverTitlePaddingBlockBottom}px` : 0,\n    titleBorderBottom: wireframe ? `${lineWidth}px ${lineType} ${colorSplit}` : 'none',\n    innerContentPadding: wireframe ? `${paddingSM}px ${popoverPaddingHorizontal}px` : 0\n  });\n};\nexport default genStyleHooks('Popover', token => {\n  const {\n    colorBgElevated,\n    colorText\n  } = token;\n  const popoverToken = mergeToken(token, {\n    popoverBg: colorBgElevated,\n    popoverColor: colorText\n  });\n  return [genBaseStyle(popoverToken), genColorStyle(popoverToken), initZoomMotion(popoverToken, 'zoom-big')];\n}, prepareComponentToken, {\n  resetStyle: false,\n  deprecatedTokens: [['width', 'titleMinWidth'], ['minWidth', 'titleMinWidth']]\n});", "map": {"version": 3, "names": ["resetComponent", "initZoomMotion", "getArrowStyle", "getArrowOffsetToken", "getArrowToken", "genStyleHooks", "mergeToken", "PresetColors", "genBaseStyle", "token", "componentCls", "popoverColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fontWeightStrong", "innerPadding", "boxShadowSecondary", "colorTextHeading", "borderRadiusLG", "zIndexPopup", "titleMarginBottom", "colorBgElevated", "popoverBg", "titleBorderBottom", "innerContentPadding", "titlePadding", "Object", "assign", "position", "top", "left", "_skip_check_", "value", "zIndex", "fontWeight", "whiteSpace", "textAlign", "cursor", "userSelect", "transform<PERSON><PERSON>in", "join", "width", "max<PERSON><PERSON><PERSON>", "direction", "display", "backgroundColor", "backgroundClip", "borderRadius", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "marginBottom", "color", "borderBottom", "margin", "sizePopupArrow", "genColorStyle", "map", "colorKey", "lightColor", "background", "prepareComponentToken", "lineWidth", "controlHeight", "fontHeight", "wireframe", "zIndexPopupBase", "marginXS", "lineType", "colorSplit", "paddingSM", "titlePaddingBlockDist", "popoverTitlePaddingBlockTop", "popoverTitlePaddingBlockBottom", "popoverPaddingHorizontal", "contentRadius", "limitVerticalRadius", "colorText", "popoverToken", "resetStyle", "deprecatedTokens"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/popover/style/index.js"], "sourcesContent": ["import { resetComponent } from '../../style';\nimport { initZoomMotion } from '../../style/motion';\nimport getArrowStyle, { getArrowOffsetToken } from '../../style/placementArrow';\nimport { getArrowToken } from '../../style/roundedArrow';\nimport { genStyleHooks, mergeToken, PresetColors } from '../../theme/internal';\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    popoverColor,\n    titleMinWidth,\n    fontWeightStrong,\n    innerPadding,\n    boxShadowSecondary,\n    colorTextHeading,\n    borderRadiusLG,\n    zIndexPopup,\n    titleMarginBottom,\n    colorBgElevated,\n    popoverBg,\n    titleBorderBottom,\n    innerContentPadding,\n    titlePadding\n  } = token;\n  return [{\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: 0,\n      // use `left` to fix https://github.com/ant-design/ant-design/issues/39195\n      left: {\n        _skip_check_: true,\n        value: 0\n      },\n      zIndex: zIndexPopup,\n      fontWeight: 'normal',\n      whiteSpace: 'normal',\n      textAlign: 'start',\n      cursor: 'auto',\n      userSelect: 'text',\n      // When use `autoArrow`, origin will follow the arrow position\n      '--valid-offset-x': 'var(--arrow-offset-horizontal, var(--arrow-x))',\n      transformOrigin: [`var(--valid-offset-x, 50%)`, `var(--arrow-y, 50%)`].join(' '),\n      '--antd-arrow-background-color': colorBgElevated,\n      width: 'max-content',\n      maxWidth: '100vw',\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&-hidden': {\n        display: 'none'\n      },\n      [`${componentCls}-content`]: {\n        position: 'relative'\n      },\n      [`${componentCls}-inner`]: {\n        backgroundColor: popoverBg,\n        backgroundClip: 'padding-box',\n        borderRadius: borderRadiusLG,\n        boxShadow: boxShadowSecondary,\n        padding: innerPadding\n      },\n      [`${componentCls}-title`]: {\n        minWidth: titleMinWidth,\n        marginBottom: titleMarginBottom,\n        color: colorTextHeading,\n        fontWeight: fontWeightStrong,\n        borderBottom: titleBorderBottom,\n        padding: titlePadding\n      },\n      [`${componentCls}-inner-content`]: {\n        color: popoverColor,\n        padding: innerContentPadding\n      }\n    })\n  },\n  // Arrow Style\n  getArrowStyle(token, 'var(--antd-arrow-background-color)'),\n  // Pure Render\n  {\n    [`${componentCls}-pure`]: {\n      position: 'relative',\n      maxWidth: 'none',\n      margin: token.sizePopupArrow,\n      display: 'inline-block',\n      [`${componentCls}-content`]: {\n        display: 'inline-block'\n      }\n    }\n  }];\n};\nconst genColorStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: PresetColors.map(colorKey => {\n      const lightColor = token[`${colorKey}6`];\n      return {\n        [`&${componentCls}-${colorKey}`]: {\n          '--antd-arrow-background-color': lightColor,\n          [`${componentCls}-inner`]: {\n            backgroundColor: lightColor\n          },\n          [`${componentCls}-arrow`]: {\n            background: 'transparent'\n          }\n        }\n      };\n    })\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    lineWidth,\n    controlHeight,\n    fontHeight,\n    padding,\n    wireframe,\n    zIndexPopupBase,\n    borderRadiusLG,\n    marginXS,\n    lineType,\n    colorSplit,\n    paddingSM\n  } = token;\n  const titlePaddingBlockDist = controlHeight - fontHeight;\n  const popoverTitlePaddingBlockTop = titlePaddingBlockDist / 2;\n  const popoverTitlePaddingBlockBottom = titlePaddingBlockDist / 2 - lineWidth;\n  const popoverPaddingHorizontal = padding;\n  return Object.assign(Object.assign(Object.assign({\n    titleMinWidth: 177,\n    zIndexPopup: zIndexPopupBase + 30\n  }, getArrowToken(token)), getArrowOffsetToken({\n    contentRadius: borderRadiusLG,\n    limitVerticalRadius: true\n  })), {\n    // internal\n    innerPadding: wireframe ? 0 : 12,\n    titleMarginBottom: wireframe ? 0 : marginXS,\n    titlePadding: wireframe ? `${popoverTitlePaddingBlockTop}px ${popoverPaddingHorizontal}px ${popoverTitlePaddingBlockBottom}px` : 0,\n    titleBorderBottom: wireframe ? `${lineWidth}px ${lineType} ${colorSplit}` : 'none',\n    innerContentPadding: wireframe ? `${paddingSM}px ${popoverPaddingHorizontal}px` : 0\n  });\n};\nexport default genStyleHooks('Popover', token => {\n  const {\n    colorBgElevated,\n    colorText\n  } = token;\n  const popoverToken = mergeToken(token, {\n    popoverBg: colorBgElevated,\n    popoverColor: colorText\n  });\n  return [genBaseStyle(popoverToken), genColorStyle(popoverToken), initZoomMotion(popoverToken, 'zoom-big')];\n}, prepareComponentToken, {\n  resetStyle: false,\n  deprecatedTokens: [['width', 'titleMinWidth'], ['minWidth', 'titleMinWidth']]\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,aAAa;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,OAAOC,aAAa,IAAIC,mBAAmB,QAAQ,4BAA4B;AAC/E,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,aAAa,EAAEC,UAAU,EAAEC,YAAY,QAAQ,sBAAsB;AAC9E,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,YAAY;IACZC,aAAa;IACbC,gBAAgB;IAChBC,YAAY;IACZC,kBAAkB;IAClBC,gBAAgB;IAChBC,cAAc;IACdC,WAAW;IACXC,iBAAiB;IACjBC,eAAe;IACfC,SAAS;IACTC,iBAAiB;IACjBC,mBAAmB;IACnBC;EACF,CAAC,GAAGf,KAAK;EACT,OAAO,CAAC;IACN,CAACC,YAAY,GAAGe,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1B,cAAc,CAACS,KAAK,CAAC,CAAC,EAAE;MACtEkB,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,CAAC;MACN;MACAC,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE;MACT,CAAC;MACDC,MAAM,EAAEd,WAAW;MACnBe,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,QAAQ;MACpBC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,MAAM;MAClB;MACA,kBAAkB,EAAE,gDAAgD;MACpEC,eAAe,EAAE,CAAC,4BAA4B,EAAE,qBAAqB,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MAChF,+BAA+B,EAAEnB,eAAe;MAChDoB,KAAK,EAAE,aAAa;MACpBC,QAAQ,EAAE,OAAO;MACjB,OAAO,EAAE;QACPC,SAAS,EAAE;MACb,CAAC;MACD,UAAU,EAAE;QACVC,OAAO,EAAE;MACX,CAAC;MACD,CAAC,GAAGjC,YAAY,UAAU,GAAG;QAC3BiB,QAAQ,EAAE;MACZ,CAAC;MACD,CAAC,GAAGjB,YAAY,QAAQ,GAAG;QACzBkC,eAAe,EAAEvB,SAAS;QAC1BwB,cAAc,EAAE,aAAa;QAC7BC,YAAY,EAAE7B,cAAc;QAC5B8B,SAAS,EAAEhC,kBAAkB;QAC7BiC,OAAO,EAAElC;MACX,CAAC;MACD,CAAC,GAAGJ,YAAY,QAAQ,GAAG;QACzBuC,QAAQ,EAAErC,aAAa;QACvBsC,YAAY,EAAE/B,iBAAiB;QAC/BgC,KAAK,EAAEnC,gBAAgB;QACvBiB,UAAU,EAAEpB,gBAAgB;QAC5BuC,YAAY,EAAE9B,iBAAiB;QAC/B0B,OAAO,EAAExB;MACX,CAAC;MACD,CAAC,GAAGd,YAAY,gBAAgB,GAAG;QACjCyC,KAAK,EAAExC,YAAY;QACnBqC,OAAO,EAAEzB;MACX;IACF,CAAC;EACH,CAAC;EACD;EACArB,aAAa,CAACO,KAAK,EAAE,oCAAoC,CAAC;EAC1D;EACA;IACE,CAAC,GAAGC,YAAY,OAAO,GAAG;MACxBiB,QAAQ,EAAE,UAAU;MACpBc,QAAQ,EAAE,MAAM;MAChBY,MAAM,EAAE5C,KAAK,CAAC6C,cAAc;MAC5BX,OAAO,EAAE,cAAc;MACvB,CAAC,GAAGjC,YAAY,UAAU,GAAG;QAC3BiC,OAAO,EAAE;MACX;IACF;EACF,CAAC,CAAC;AACJ,CAAC;AACD,MAAMY,aAAa,GAAG9C,KAAK,IAAI;EAC7B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAGH,YAAY,CAACiD,GAAG,CAACC,QAAQ,IAAI;MAC3C,MAAMC,UAAU,GAAGjD,KAAK,CAAC,GAAGgD,QAAQ,GAAG,CAAC;MACxC,OAAO;QACL,CAAC,IAAI/C,YAAY,IAAI+C,QAAQ,EAAE,GAAG;UAChC,+BAA+B,EAAEC,UAAU;UAC3C,CAAC,GAAGhD,YAAY,QAAQ,GAAG;YACzBkC,eAAe,EAAEc;UACnB,CAAC;UACD,CAAC,GAAGhD,YAAY,QAAQ,GAAG;YACzBiD,UAAU,EAAE;UACd;QACF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGnD,KAAK,IAAI;EAC5C,MAAM;IACJoD,SAAS;IACTC,aAAa;IACbC,UAAU;IACVf,OAAO;IACPgB,SAAS;IACTC,eAAe;IACfhD,cAAc;IACdiD,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC;EACF,CAAC,GAAG5D,KAAK;EACT,MAAM6D,qBAAqB,GAAGR,aAAa,GAAGC,UAAU;EACxD,MAAMQ,2BAA2B,GAAGD,qBAAqB,GAAG,CAAC;EAC7D,MAAME,8BAA8B,GAAGF,qBAAqB,GAAG,CAAC,GAAGT,SAAS;EAC5E,MAAMY,wBAAwB,GAAGzB,OAAO;EACxC,OAAOvB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;IAC/Cd,aAAa,EAAE,GAAG;IAClBM,WAAW,EAAE+C,eAAe,GAAG;EACjC,CAAC,EAAE7D,aAAa,CAACK,KAAK,CAAC,CAAC,EAAEN,mBAAmB,CAAC;IAC5CuE,aAAa,EAAEzD,cAAc;IAC7B0D,mBAAmB,EAAE;EACvB,CAAC,CAAC,CAAC,EAAE;IACH;IACA7D,YAAY,EAAEkD,SAAS,GAAG,CAAC,GAAG,EAAE;IAChC7C,iBAAiB,EAAE6C,SAAS,GAAG,CAAC,GAAGE,QAAQ;IAC3C1C,YAAY,EAAEwC,SAAS,GAAG,GAAGO,2BAA2B,MAAME,wBAAwB,MAAMD,8BAA8B,IAAI,GAAG,CAAC;IAClIlD,iBAAiB,EAAE0C,SAAS,GAAG,GAAGH,SAAS,MAAMM,QAAQ,IAAIC,UAAU,EAAE,GAAG,MAAM;IAClF7C,mBAAmB,EAAEyC,SAAS,GAAG,GAAGK,SAAS,MAAMI,wBAAwB,IAAI,GAAG;EACpF,CAAC,CAAC;AACJ,CAAC;AACD,eAAepE,aAAa,CAAC,SAAS,EAAEI,KAAK,IAAI;EAC/C,MAAM;IACJW,eAAe;IACfwD;EACF,CAAC,GAAGnE,KAAK;EACT,MAAMoE,YAAY,GAAGvE,UAAU,CAACG,KAAK,EAAE;IACrCY,SAAS,EAAED,eAAe;IAC1BT,YAAY,EAAEiE;EAChB,CAAC,CAAC;EACF,OAAO,CAACpE,YAAY,CAACqE,YAAY,CAAC,EAAEtB,aAAa,CAACsB,YAAY,CAAC,EAAE5E,cAAc,CAAC4E,YAAY,EAAE,UAAU,CAAC,CAAC;AAC5G,CAAC,EAAEjB,qBAAqB,EAAE;EACxBkB,UAAU,EAAE,KAAK;EACjBC,gBAAgB,EAAE,CAAC,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC;AAC9E,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}