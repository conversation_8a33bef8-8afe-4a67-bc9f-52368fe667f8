/**
 * 前端性能监控和优化服务
 * 提供性能指标收集、内存管理、渲染优化等功能
 */

// 性能指标接口
interface PerformanceMetrics {
  // 页面性能
  pageLoad: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  
  // 网络性能
  apiResponseTimes: number[];
  networkLatency: number;
  
  // 渲染性能
  renderTimes: number[];
  componentMountTimes: Map<string, number>;
  
  // 内存使用
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  
  // 用户交互
  interactionTimes: number[];
  scrollPerformance: number[];
}

// 性能阈值配置
interface PerformanceThresholds {
  slowApiCall: number;
  slowRender: number;
  highMemoryUsage: number;
  slowInteraction: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private thresholds: PerformanceThresholds;
  private observers: Map<string, PerformanceObserver> = new Map();
  private isMonitoring: boolean = false;

  constructor() {
    this.metrics = {
      pageLoad: 0,
      domContentLoaded: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      apiResponseTimes: [],
      networkLatency: 0,
      renderTimes: [],
      componentMountTimes: new Map(),
      memoryUsage: { used: 0, total: 0, percentage: 0 },
      interactionTimes: [],
      scrollPerformance: [],
    };

    this.thresholds = {
      slowApiCall: 3000, // 3秒
      slowRender: 100,   // 100ms
      highMemoryUsage: 80, // 80%
      slowInteraction: 100, // 100ms
    };

    this.initializeMonitoring();
  }

  private initializeMonitoring(): void {
    if (typeof window === 'undefined') return;

    // 监控页面加载性能
    this.monitorPagePerformance();
    
    // 监控渲染性能
    this.monitorRenderPerformance();
    
    // 监控内存使用
    this.monitorMemoryUsage();
    
    // 监控用户交互
    this.monitorUserInteractions();

    this.isMonitoring = true;
    console.log('性能监控已启动');
  }

  private monitorPagePerformance(): void {
    // 使用Performance API监控页面性能
    if ('performance' in window) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          
          this.metrics.pageLoad = navigation.loadEventEnd - navigation.loadEventStart;
          this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
          
          // 获取Paint Timing
          const paintEntries = performance.getEntriesByType('paint');
          paintEntries.forEach(entry => {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.firstContentfulPaint = entry.startTime;
            }
          });

          // 监控LCP
          if ('PerformanceObserver' in window) {
            const lcpObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              const lastEntry = entries[entries.length - 1];
              this.metrics.largestContentfulPaint = lastEntry.startTime;
            });
            
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            this.observers.set('lcp', lcpObserver);
          }
        }, 0);
      });
    }
  }

  private monitorRenderPerformance(): void {
    if ('PerformanceObserver' in window) {
      // 监控长任务
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          if (entry.duration > this.thresholds.slowRender) {
            console.warn(`检测到长任务: ${entry.duration}ms`);
            this.metrics.renderTimes.push(entry.duration);
          }
        });
      });

      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.set('longtask', longTaskObserver);
    }
  }

  private monitorMemoryUsage(): void {
    // 定期检查内存使用情况
    setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.metrics.memoryUsage = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
        };

        // 内存使用过高时发出警告
        if (this.metrics.memoryUsage.percentage > this.thresholds.highMemoryUsage) {
          console.warn(`内存使用率过高: ${this.metrics.memoryUsage.percentage.toFixed(1)}%`);
          this.optimizeMemory();
        }
      }
    }, 10000); // 每10秒检查一次
  }

  private monitorUserInteractions(): void {
    // 监控点击响应时间
    document.addEventListener('click', (event) => {
      const startTime = performance.now();
      
      // 使用requestAnimationFrame来测量到下一帧的时间
      requestAnimationFrame(() => {
        const endTime = performance.now();
        const interactionTime = endTime - startTime;
        
        this.metrics.interactionTimes.push(interactionTime);
        
        if (interactionTime > this.thresholds.slowInteraction) {
          console.warn(`慢交互检测: ${interactionTime.toFixed(2)}ms`);
        }
      });
    });

    // 监控滚动性能
    let scrollStartTime = 0;
    document.addEventListener('scroll', () => {
      if (scrollStartTime === 0) {
        scrollStartTime = performance.now();
      }
    });

    document.addEventListener('scrollend', () => {
      if (scrollStartTime > 0) {
        const scrollTime = performance.now() - scrollStartTime;
        this.metrics.scrollPerformance.push(scrollTime);
        scrollStartTime = 0;
      }
    });
  }

  // 记录API调用性能
  recordApiCall(duration: number, url: string): void {
    this.metrics.apiResponseTimes.push(duration);
    
    if (duration > this.thresholds.slowApiCall) {
      console.warn(`慢API调用: ${url} - ${duration}ms`);
    }

    // 只保留最近100次调用
    if (this.metrics.apiResponseTimes.length > 100) {
      this.metrics.apiResponseTimes = this.metrics.apiResponseTimes.slice(-100);
    }
  }

  // 记录组件挂载时间
  recordComponentMount(componentName: string, duration: number): void {
    this.metrics.componentMountTimes.set(componentName, duration);
    
    if (duration > this.thresholds.slowRender) {
      console.warn(`慢组件挂载: ${componentName} - ${duration}ms`);
    }
  }

  // 内存优化
  private optimizeMemory(): void {
    // 清理不必要的数据
    if (this.metrics.apiResponseTimes.length > 50) {
      this.metrics.apiResponseTimes = this.metrics.apiResponseTimes.slice(-50);
    }
    
    if (this.metrics.renderTimes.length > 50) {
      this.metrics.renderTimes = this.metrics.renderTimes.slice(-50);
    }
    
    if (this.metrics.interactionTimes.length > 50) {
      this.metrics.interactionTimes = this.metrics.interactionTimes.slice(-50);
    }

    // 强制垃圾回收（如果支持）
    if ('gc' in window) {
      (window as any).gc();
    }

    console.log('执行内存优化');
  }

  // 获取性能报告
  getPerformanceReport(): {
    metrics: PerformanceMetrics;
    analysis: {
      pageLoadGrade: string;
      apiPerformanceGrade: string;
      renderPerformanceGrade: string;
      memoryUsageGrade: string;
      recommendations: string[];
    };
  } {
    const recommendations: string[] = [];
    
    // 分析页面加载性能
    let pageLoadGrade = 'A';
    if (this.metrics.pageLoad > 3000) {
      pageLoadGrade = 'C';
      recommendations.push('优化页面加载时间，考虑代码分割和懒加载');
    } else if (this.metrics.pageLoad > 1500) {
      pageLoadGrade = 'B';
    }

    // 分析API性能
    const avgApiTime = this.metrics.apiResponseTimes.length > 0 
      ? this.metrics.apiResponseTimes.reduce((a, b) => a + b, 0) / this.metrics.apiResponseTimes.length 
      : 0;
    
    let apiPerformanceGrade = 'A';
    if (avgApiTime > 2000) {
      apiPerformanceGrade = 'C';
      recommendations.push('优化API响应时间，考虑添加缓存或优化后端查询');
    } else if (avgApiTime > 1000) {
      apiPerformanceGrade = 'B';
    }

    // 分析渲染性能
    const avgRenderTime = this.metrics.renderTimes.length > 0
      ? this.metrics.renderTimes.reduce((a, b) => a + b, 0) / this.metrics.renderTimes.length
      : 0;
    
    let renderPerformanceGrade = 'A';
    if (avgRenderTime > 100) {
      renderPerformanceGrade = 'C';
      recommendations.push('优化组件渲染性能，考虑使用React.memo和useMemo');
    } else if (avgRenderTime > 50) {
      renderPerformanceGrade = 'B';
    }

    // 分析内存使用
    let memoryUsageGrade = 'A';
    if (this.metrics.memoryUsage.percentage > 80) {
      memoryUsageGrade = 'C';
      recommendations.push('内存使用率过高，检查是否存在内存泄漏');
    } else if (this.metrics.memoryUsage.percentage > 60) {
      memoryUsageGrade = 'B';
    }

    return {
      metrics: this.metrics,
      analysis: {
        pageLoadGrade,
        apiPerformanceGrade,
        renderPerformanceGrade,
        memoryUsageGrade,
        recommendations
      }
    };
  }

  // 停止监控
  stopMonitoring(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.isMonitoring = false;
    console.log('性能监控已停止');
  }

  // 导出性能数据
  exportMetrics(): string {
    return JSON.stringify(this.getPerformanceReport(), null, 2);
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// React Hook for performance monitoring
export const usePerformanceMonitor = () => {
  const recordApiCall = (duration: number, url: string) => {
    performanceMonitor.recordApiCall(duration, url);
  };

  const recordComponentMount = (componentName: string, duration: number) => {
    performanceMonitor.recordComponentMount(componentName, duration);
  };

  const getReport = () => {
    return performanceMonitor.getPerformanceReport();
  };

  return {
    recordApiCall,
    recordComponentMount,
    getReport,
    exportMetrics: () => performanceMonitor.exportMetrics()
  };
};

// 性能监控装饰器
export const withPerformanceMonitoring = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) => {
  return React.memo((props: P) => {
    const mountStartTime = React.useRef(performance.now());

    React.useEffect(() => {
      const mountTime = performance.now() - mountStartTime.current;
      performanceMonitor.recordComponentMount(componentName, mountTime);
    }, []);

    return <WrappedComponent {...props} />;
  });
};
