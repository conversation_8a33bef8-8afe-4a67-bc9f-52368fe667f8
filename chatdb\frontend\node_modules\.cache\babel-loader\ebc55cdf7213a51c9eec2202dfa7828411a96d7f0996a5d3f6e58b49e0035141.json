{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { prepareComponentToken } from '.';\nimport { genComponentStyleHook } from '../../theme/internal';\nimport getColumnsStyle from './columns';\n// ============================== Panel ===============================\nconst genPanelStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-panel`]: [getColumnsStyle(token), {\n      display: 'inline-flex',\n      border: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`,\n      borderRadius: token.borderRadiusLG,\n      overflowX: 'auto',\n      maxWidth: '100%',\n      [`${componentCls}-menus`]: {\n        alignItems: 'stretch'\n      },\n      [`${componentCls}-menu`]: {\n        height: 'auto'\n      },\n      '&-empty': {\n        padding: token.paddingXXS\n      }\n    }]\n  };\n};\n// ============================== Export ==============================\nexport default genComponentStyleHook(['Cascader', 'Panel'], token => genPanelStyle(token), prepareComponentToken);", "map": {"version": 3, "names": ["unit", "prepareComponentToken", "genComponentStyleHook", "getColumnsStyle", "genPanelStyle", "token", "componentCls", "display", "border", "lineWidth", "lineType", "colorSplit", "borderRadius", "borderRadiusLG", "overflowX", "max<PERSON><PERSON><PERSON>", "alignItems", "height", "padding", "paddingXXS"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/cascader/style/panel.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { prepareComponentToken } from '.';\nimport { genComponentStyleHook } from '../../theme/internal';\nimport getColumnsStyle from './columns';\n// ============================== Panel ===============================\nconst genPanelStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-panel`]: [getColumnsStyle(token), {\n      display: 'inline-flex',\n      border: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`,\n      borderRadius: token.borderRadiusLG,\n      overflowX: 'auto',\n      maxWidth: '100%',\n      [`${componentCls}-menus`]: {\n        alignItems: 'stretch'\n      },\n      [`${componentCls}-menu`]: {\n        height: 'auto'\n      },\n      '&-empty': {\n        padding: token.paddingXXS\n      }\n    }]\n  };\n};\n// ============================== Export ==============================\nexport default genComponentStyleHook(['Cascader', 'Panel'], token => genPanelStyle(token), prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,qBAAqB,QAAQ,GAAG;AACzC,SAASC,qBAAqB,QAAQ,sBAAsB;AAC5D,OAAOC,eAAe,MAAM,WAAW;AACvC;AACA,MAAMC,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,QAAQ,GAAG,CAACH,eAAe,CAACE,KAAK,CAAC,EAAE;MAClDE,OAAO,EAAE,aAAa;MACtBC,MAAM,EAAE,GAAGR,IAAI,CAACK,KAAK,CAACI,SAAS,CAAC,IAAIJ,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACM,UAAU,EAAE;MACxEC,YAAY,EAAEP,KAAK,CAACQ,cAAc;MAClCC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAE,MAAM;MAChB,CAAC,GAAGT,YAAY,QAAQ,GAAG;QACzBU,UAAU,EAAE;MACd,CAAC;MACD,CAAC,GAAGV,YAAY,OAAO,GAAG;QACxBW,MAAM,EAAE;MACV,CAAC;MACD,SAAS,EAAE;QACTC,OAAO,EAAEb,KAAK,CAACc;MACjB;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD;AACA,eAAejB,qBAAqB,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,EAAEG,KAAK,IAAID,aAAa,CAACC,KAAK,CAAC,EAAEJ,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}