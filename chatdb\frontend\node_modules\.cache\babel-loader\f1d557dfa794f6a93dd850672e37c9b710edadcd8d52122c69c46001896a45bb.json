{"ast": null, "code": "/*\nLanguage: Shell Session\nRequires: bash.js\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction shell(hljs) {\n  return {\n    name: 'Shell Session',\n    aliases: ['console'],\n    contains: [{\n      className: 'meta',\n      // We cannot add \\s (spaces) in the regular expression otherwise it will be too broad and produce unexpected result.\n      // For instance, in the following example, it would match \"echo /path/to/home >\" as a prompt:\n      // echo /path/to/home > t.exe\n      begin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#]/,\n      starts: {\n        end: /[^\\\\](?=\\s*$)/,\n        subLanguage: 'bash'\n      }\n    }]\n  };\n}\nmodule.exports = shell;", "map": {"version": 3, "names": ["shell", "hljs", "name", "aliases", "contains", "className", "begin", "starts", "end", "subLanguage", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/shell.js"], "sourcesContent": ["/*\nLanguage: Shell Session\nRequires: bash.js\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction shell(hljs) {\n  return {\n    name: 'Shell Session',\n    aliases: [ 'console' ],\n    contains: [\n      {\n        className: 'meta',\n        // We cannot add \\s (spaces) in the regular expression otherwise it will be too broad and produce unexpected result.\n        // For instance, in the following example, it would match \"echo /path/to/home >\" as a prompt:\n        // echo /path/to/home > t.exe\n        begin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#]/,\n        starts: {\n          end: /[^\\\\](?=\\s*$)/,\n          subLanguage: 'bash'\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = shell;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO;IACLC,IAAI,EAAE,eAAe;IACrBC,OAAO,EAAE,CAAE,SAAS,CAAE;IACtBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MACjB;MACA;MACA;MACAC,KAAK,EAAE,gCAAgC;MACvCC,MAAM,EAAE;QACNC,GAAG,EAAE,eAAe;QACpBC,WAAW,EAAE;MACf;IACF,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGX,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}