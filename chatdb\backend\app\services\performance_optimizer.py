"""
性能优化服务
提供查询优化、缓存策略、连接池管理和性能监控功能
"""
import time
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable
from functools import wraps
from collections import defaultdict, deque
from datetime import datetime, timedelta
import threading
import psutil
import gc

from app.services.cache_service import cache_service
from app.services.monitoring_service import monitoring_service

logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        # 性能指标
        self.metrics = {
            'query_times': deque(maxlen=1000),
            'llm_call_times': deque(maxlen=1000),
            'db_connection_times': deque(maxlen=1000),
            'memory_usage': deque(maxlen=100),
            'active_connections': 0,
            'slow_queries': deque(maxlen=100),
        }
        
        # 性能阈值配置
        self.thresholds = {
            'slow_query_time': 5.0,  # 5秒
            'memory_warning': 80.0,  # 80%
            'connection_warning': 50,  # 50个连接
            'cache_hit_rate_warning': 0.3,  # 30%以下
        }
        
        # 优化策略
        self.optimization_strategies = {
            'enable_query_cache': True,
            'enable_connection_pooling': True,
            'enable_result_pagination': True,
            'enable_lazy_loading': True,
            'enable_compression': True,
        }
        
        # 启动性能监控
        self._start_performance_monitoring()
    
    def optimize_query(self, sql: str, connection_info: Dict[str, Any]) -> str:
        """优化SQL查询"""
        optimized_sql = sql
        
        # 1. 添加LIMIT限制大结果集
        if not self._has_limit(sql) and self._is_select_query(sql):
            optimized_sql = self._add_smart_limit(optimized_sql)
        
        # 2. 优化JOIN查询
        if 'JOIN' in sql.upper():
            optimized_sql = self._optimize_joins(optimized_sql)
        
        # 3. 添加索引提示（如果支持）
        if connection_info.get('db_type') == 'mysql':
            optimized_sql = self._add_index_hints(optimized_sql)
        
        # 4. 优化WHERE子句
        optimized_sql = self._optimize_where_clause(optimized_sql)
        
        return optimized_sql
    
    def _has_limit(self, sql: str) -> bool:
        """检查SQL是否已有LIMIT子句"""
        return 'LIMIT' in sql.upper()
    
    def _is_select_query(self, sql: str) -> bool:
        """检查是否为SELECT查询"""
        return sql.strip().upper().startswith('SELECT')
    
    def _add_smart_limit(self, sql: str) -> str:
        """智能添加LIMIT"""
        # 根据查询复杂度决定LIMIT大小
        if 'GROUP BY' in sql.upper() or 'DISTINCT' in sql.upper():
            limit = 500  # 聚合查询限制更小
        else:
            limit = 1000  # 普通查询
        
        return f"{sql.rstrip(';')} LIMIT {limit}"
    
    def _optimize_joins(self, sql: str) -> str:
        """优化JOIN查询"""
        # 这里可以添加JOIN优化逻辑
        # 例如：重排JOIN顺序、添加索引提示等
        return sql
    
    def _add_index_hints(self, sql: str) -> str:
        """添加索引提示（MySQL）"""
        # 这里可以添加索引提示逻辑
        return sql
    
    def _optimize_where_clause(self, sql: str) -> str:
        """优化WHERE子句"""
        # 这里可以添加WHERE子句优化逻辑
        # 例如：条件重排、类型转换优化等
        return sql
    
    def monitor_query_performance(self, func: Callable) -> Callable:
        """查询性能监控装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 记录性能指标
                self.metrics['query_times'].append(execution_time)
                
                # 检查慢查询
                if execution_time > self.thresholds['slow_query_time']:
                    self._record_slow_query(args, kwargs, execution_time)
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"查询执行失败，耗时: {execution_time:.3f}秒，错误: {e}")
                raise
        return wrapper
    
    def monitor_llm_performance(self, func: Callable) -> Callable:
        """LLM调用性能监控装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                self.metrics['llm_call_times'].append(execution_time)
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"LLM调用失败，耗时: {execution_time:.3f}秒，错误: {e}")
                raise
        return wrapper
    
    def _record_slow_query(self, args: tuple, kwargs: dict, execution_time: float):
        """记录慢查询"""
        slow_query_info = {
            'timestamp': datetime.now(),
            'execution_time': execution_time,
            'args': str(args)[:200],
            'kwargs': str(kwargs)[:200],
        }
        self.metrics['slow_queries'].append(slow_query_info)
        logger.warning(f"慢查询检测: 耗时 {execution_time:.3f}秒")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        query_times = list(self.metrics['query_times'])
        llm_times = list(self.metrics['llm_call_times'])
        
        stats = {
            'query_performance': {
                'avg_time': sum(query_times) / len(query_times) if query_times else 0,
                'max_time': max(query_times) if query_times else 0,
                'min_time': min(query_times) if query_times else 0,
                'total_queries': len(query_times),
                'slow_queries': len(self.metrics['slow_queries']),
            },
            'llm_performance': {
                'avg_time': sum(llm_times) / len(llm_times) if llm_times else 0,
                'max_time': max(llm_times) if llm_times else 0,
                'total_calls': len(llm_times),
            },
            'system_performance': {
                'memory_usage': psutil.virtual_memory().percent,
                'cpu_usage': psutil.cpu_percent(),
                'active_connections': self.metrics['active_connections'],
            },
            'cache_performance': cache_service.get_stats(),
        }
        
        return stats
    
    def optimize_memory_usage(self):
        """内存使用优化"""
        # 1. 强制垃圾回收
        collected = gc.collect()
        logger.info(f"垃圾回收完成，回收对象数: {collected}")
        
        # 2. 清理过期缓存
        expired_count = cache_service.cleanup_expired()
        logger.info(f"清理过期缓存项: {expired_count}")
        
        # 3. 检查内存使用情况
        memory_percent = psutil.virtual_memory().percent
        if memory_percent > self.thresholds['memory_warning']:
            logger.warning(f"内存使用率过高: {memory_percent:.1f}%")
            self._emergency_memory_cleanup()
    
    def _emergency_memory_cleanup(self):
        """紧急内存清理"""
        # 清空部分缓存
        cache_service.clear()
        
        # 强制垃圾回收
        gc.collect()
        
        logger.warning("执行紧急内存清理")
    
    def check_performance_health(self) -> Dict[str, Any]:
        """性能健康检查"""
        stats = self.get_performance_stats()
        health_status = {
            'overall_healthy': True,
            'issues': [],
            'recommendations': [],
        }
        
        # 检查查询性能
        avg_query_time = stats['query_performance']['avg_time']
        if avg_query_time > self.thresholds['slow_query_time']:
            health_status['overall_healthy'] = False
            health_status['issues'].append(f"平均查询时间过长: {avg_query_time:.2f}秒")
            health_status['recommendations'].append("考虑添加数据库索引或优化查询")
        
        # 检查内存使用
        memory_usage = stats['system_performance']['memory_usage']
        if memory_usage > self.thresholds['memory_warning']:
            health_status['overall_healthy'] = False
            health_status['issues'].append(f"内存使用率过高: {memory_usage:.1f}%")
            health_status['recommendations'].append("执行内存清理或增加服务器内存")
        
        # 检查缓存命中率
        cache_stats = stats['cache_performance']
        hit_rate = cache_stats.get('hit_rate', 0) / 100
        if hit_rate < self.thresholds['cache_hit_rate_warning']:
            health_status['issues'].append(f"缓存命中率过低: {hit_rate:.1%}")
            health_status['recommendations'].append("调整缓存策略或增加缓存时间")
        
        return health_status
    
    def _start_performance_monitoring(self):
        """启动性能监控线程"""
        def monitor_loop():
            while True:
                try:
                    time.sleep(60)  # 每分钟检查一次
                    
                    # 记录系统指标
                    memory_usage = psutil.virtual_memory().percent
                    self.metrics['memory_usage'].append(memory_usage)
                    
                    # 执行性能优化
                    if memory_usage > self.thresholds['memory_warning']:
                        self.optimize_memory_usage()
                    
                    # 性能健康检查
                    health = self.check_performance_health()
                    if not health['overall_healthy']:
                        logger.warning(f"性能健康检查发现问题: {health['issues']}")
                        
                except Exception as e:
                    logger.error(f"性能监控线程错误: {e}")
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        logger.info("性能监控线程已启动")

# 全局性能优化器实例
performance_optimizer = PerformanceOptimizer()

# 装饰器导出
def monitor_query_performance(func):
    return performance_optimizer.monitor_query_performance(func)

def monitor_llm_performance(func):
    return performance_optimizer.monitor_llm_performance(func)
