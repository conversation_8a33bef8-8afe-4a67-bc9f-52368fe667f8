{"ast": null, "code": "/*\nLanguage: Python REPL\nRequires: python.js\nAuthor: <PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction pythonRepl(hljs) {\n  return {\n    aliases: ['pycon'],\n    contains: [{\n      className: 'meta',\n      starts: {\n        // a space separates the REPL prefix from the actual code\n        // this is purely for cleaner HTML output\n        end: / |$/,\n        starts: {\n          end: '$',\n          subLanguage: 'python'\n        }\n      },\n      variants: [{\n        begin: /^>>>(?=[ ]|$)/\n      }, {\n        begin: /^\\.\\.\\.(?=[ ]|$)/\n      }]\n    }]\n  };\n}\nmodule.exports = pythonRepl;", "map": {"version": 3, "names": ["pythonRepl", "hljs", "aliases", "contains", "className", "starts", "end", "subLanguage", "variants", "begin", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/python-repl.js"], "sourcesContent": ["/*\nLanguage: Python REPL\nRequires: python.js\nAuthor: <PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction pythonRepl(hljs) {\n  return {\n    aliases: [ 'pycon' ],\n    contains: [\n      {\n        className: 'meta',\n        starts: {\n          // a space separates the REPL prefix from the actual code\n          // this is purely for cleaner HTML output\n          end: / |$/,\n          starts: {\n            end: '$',\n            subLanguage: 'python'\n          }\n        },\n        variants: [\n          {\n            begin: /^>>>(?=[ ]|$)/\n          },\n          {\n            begin: /^\\.\\.\\.(?=[ ]|$)/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = pythonRepl;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO;IACLC,OAAO,EAAE,CAAE,OAAO,CAAE;IACpBC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAE;QACN;QACA;QACAC,GAAG,EAAE,KAAK;QACVD,MAAM,EAAE;UACNC,GAAG,EAAE,GAAG;UACRC,WAAW,EAAE;QACf;MACF,CAAC;MACDC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE;MACT,CAAC,EACD;QACEA,KAAK,EAAE;MACT,CAAC;IAEL,CAAC;EAEL,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGX,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}