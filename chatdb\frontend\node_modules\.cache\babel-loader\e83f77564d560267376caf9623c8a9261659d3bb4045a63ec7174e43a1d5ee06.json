{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst SkeletonNode = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    active,\n    children\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-element`, {\n    [`${prefixCls}-active`]: active\n  }, hashId, className, rootClassName, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-image`, className),\n    style: style\n  }, children)));\n};\nexport default SkeletonNode;", "map": {"version": 3, "names": ["React", "classNames", "ConfigContext", "useStyle", "SkeletonNode", "props", "prefixCls", "customizePrefixCls", "className", "rootClassName", "style", "active", "children", "getPrefixCls", "useContext", "wrapCSSVar", "hashId", "cssVarCls", "cls", "createElement"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/skeleton/Node.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst SkeletonNode = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    active,\n    children\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-element`, {\n    [`${prefixCls}-active`]: active\n  }, hashId, className, rootClassName, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-image`, className),\n    style: style\n  }, children)));\n};\nexport default SkeletonNode;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,aAAa;IACbC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAGP,KAAK;EACT,MAAM;IACJQ;EACF,CAAC,GAAGb,KAAK,CAACc,UAAU,CAACZ,aAAa,CAAC;EACnC,MAAMI,SAAS,GAAGO,YAAY,CAAC,UAAU,EAAEN,kBAAkB,CAAC;EAC9D,MAAM,CAACQ,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAACG,SAAS,CAAC;EAC3D,MAAMY,GAAG,GAAGjB,UAAU,CAACK,SAAS,EAAE,GAAGA,SAAS,UAAU,EAAE;IACxD,CAAC,GAAGA,SAAS,SAAS,GAAGK;EAC3B,CAAC,EAAEK,MAAM,EAAER,SAAS,EAAEC,aAAa,EAAEQ,SAAS,CAAC;EAC/C,OAAOF,UAAU,CAAC,aAAaf,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACxDX,SAAS,EAAEU;EACb,CAAC,EAAE,aAAalB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACzCX,SAAS,EAAEP,UAAU,CAAC,GAAGK,SAAS,QAAQ,EAAEE,SAAS,CAAC;IACtDE,KAAK,EAAEA;EACT,CAAC,EAAEE,QAAQ,CAAC,CAAC,CAAC;AAChB,CAAC;AACD,eAAeR,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}