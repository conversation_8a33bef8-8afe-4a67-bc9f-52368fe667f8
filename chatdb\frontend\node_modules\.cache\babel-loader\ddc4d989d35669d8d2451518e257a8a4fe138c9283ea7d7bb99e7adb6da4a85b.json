{"ast": null, "code": "// Part logic is from `qrcode.react`. (ISC License)\n// https://github.com/zpao/qrcode.react\n\n// ==========================================================\n\nimport { Ecc } from \"./libs/qrcodegen\";\n\n// =================== ERROR_LEVEL ==========================\nexport var ERROR_LEVEL_MAP = {\n  L: Ecc.LOW,\n  M: Ecc.MEDIUM,\n  Q: Ecc.QUARTILE,\n  H: Ecc.HIGH\n};\n\n// =================== DEFAULT_VALUE ==========================\nexport var DEFAULT_SIZE = 128;\nexport var DEFAULT_LEVEL = 'L';\nexport var DEFAULT_BACKGROUND_COLOR = '#FFFFFF';\nexport var DEFAULT_FRONT_COLOR = '#000000';\nexport var DEFAULT_NEED_MARGIN = false;\nexport var DEFAULT_MINVERSION = 1;\nexport var SPEC_MARGIN_SIZE = 4;\nexport var DEFAULT_MARGIN_SIZE = 0;\nexport var DEFAULT_IMG_SCALE = 0.1;\n\n// =================== UTILS ==========================\n/**\n * Generate a path string from modules\n * @param modules\n * @param margin \n * @returns \n */\nexport function generatePath(modules) {\n  var margin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var ops = [];\n  modules.forEach(function (row, y) {\n    var start = null;\n    row.forEach(function (cell, x) {\n      if (!cell && start !== null) {\n        ops.push(\"M\".concat(start + margin, \" \").concat(y + margin, \"h\").concat(x - start, \"v1H\").concat(start + margin, \"z\"));\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(\"M\".concat(x + margin, \",\").concat(y + margin, \" h1v1H\").concat(x + margin, \"z\"));\n        } else {\n          ops.push(\"M\".concat(start + margin, \",\").concat(y + margin, \" h\").concat(x + 1 - start, \"v1H\").concat(start + margin, \"z\"));\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join('');\n}\n/**\n * Excavate modules\n * @param modules \n * @param excavation \n * @returns \n */\nexport function excavateModules(modules, excavation) {\n  return modules.slice().map(function (row, y) {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map(function (cell, x) {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\n\n/**\n * Get image settings\n * @param cells The modules of the QR code\n * @param size The size of the QR code\n * @param margin \n * @param imageSettings \n * @returns \n */\nexport function getImageSettings(cells, size, margin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  var numCells = cells.length + margin * 2;\n  var defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  var scale = numCells / size;\n  var w = (imageSettings.width || defaultSize) * scale;\n  var h = (imageSettings.height || defaultSize) * scale;\n  var x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  var y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  var opacity = imageSettings.opacity == null ? 1 : imageSettings.opacity;\n  var excavation = null;\n  if (imageSettings.excavate) {\n    var floorX = Math.floor(x);\n    var floorY = Math.floor(y);\n    var ceilW = Math.ceil(w + x - floorX);\n    var ceilH = Math.ceil(h + y - floorY);\n    excavation = {\n      x: floorX,\n      y: floorY,\n      w: ceilW,\n      h: ceilH\n    };\n  }\n  var crossOrigin = imageSettings.crossOrigin;\n  return {\n    x: x,\n    y: y,\n    h: h,\n    w: w,\n    excavation: excavation,\n    opacity: opacity,\n    crossOrigin: crossOrigin\n  };\n}\n\n/**\n * Get margin size\n * @param needMargin Whether need margin\n * @param marginSize Custom margin size\n * @returns \n */\nexport function getMarginSize(needMargin, marginSize) {\n  if (marginSize != null) {\n    return Math.floor(marginSize);\n  }\n  return needMargin ? SPEC_MARGIN_SIZE : DEFAULT_MARGIN_SIZE;\n}\n/**\n * Check if Path2D is supported\n */\nexport var isSupportPath2d = function () {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();", "map": {"version": 3, "names": ["Ecc", "ERROR_LEVEL_MAP", "L", "LOW", "M", "MEDIUM", "Q", "QUARTILE", "H", "HIGH", "DEFAULT_SIZE", "DEFAULT_LEVEL", "DEFAULT_BACKGROUND_COLOR", "DEFAULT_FRONT_COLOR", "DEFAULT_NEED_MARGIN", "DEFAULT_MINVERSION", "SPEC_MARGIN_SIZE", "DEFAULT_MARGIN_SIZE", "DEFAULT_IMG_SCALE", "generatePath", "modules", "margin", "arguments", "length", "undefined", "ops", "for<PERSON>ach", "row", "y", "start", "cell", "x", "push", "concat", "join", "excavateModules", "excavation", "slice", "map", "h", "w", "getImageSettings", "cells", "size", "imageSettings", "num<PERSON>ells", "defaultSize", "Math", "floor", "scale", "width", "height", "opacity", "excavate", "floorX", "floorY", "ceilW", "ceil", "ceilH", "crossOrigin", "getMarginSize", "<PERSON><PERSON><PERSON><PERSON>", "marginSize", "isSupportPath2d", "Path2D", "addPath", "e"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@rc-component/qrcode/es/utils.js"], "sourcesContent": ["// Part logic is from `qrcode.react`. (ISC License)\n// https://github.com/zpao/qrcode.react\n\n// ==========================================================\n\nimport { Ecc } from \"./libs/qrcodegen\";\n\n// =================== ERROR_LEVEL ==========================\nexport var ERROR_LEVEL_MAP = {\n  L: Ecc.LOW,\n  M: Ecc.MEDIUM,\n  Q: Ecc.QUARTILE,\n  H: Ecc.HIGH\n};\n\n// =================== DEFAULT_VALUE ==========================\nexport var DEFAULT_SIZE = 128;\nexport var DEFAULT_LEVEL = 'L';\nexport var DEFAULT_BACKGROUND_COLOR = '#FFFFFF';\nexport var DEFAULT_FRONT_COLOR = '#000000';\nexport var DEFAULT_NEED_MARGIN = false;\nexport var DEFAULT_MINVERSION = 1;\nexport var SPEC_MARGIN_SIZE = 4;\nexport var DEFAULT_MARGIN_SIZE = 0;\nexport var DEFAULT_IMG_SCALE = 0.1;\n\n// =================== UTILS ==========================\n/**\n * Generate a path string from modules\n * @param modules\n * @param margin \n * @returns \n */\nexport function generatePath(modules) {\n  var margin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var ops = [];\n  modules.forEach(function (row, y) {\n    var start = null;\n    row.forEach(function (cell, x) {\n      if (!cell && start !== null) {\n        ops.push(\"M\".concat(start + margin, \" \").concat(y + margin, \"h\").concat(x - start, \"v1H\").concat(start + margin, \"z\"));\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(\"M\".concat(x + margin, \",\").concat(y + margin, \" h1v1H\").concat(x + margin, \"z\"));\n        } else {\n          ops.push(\"M\".concat(start + margin, \",\").concat(y + margin, \" h\").concat(x + 1 - start, \"v1H\").concat(start + margin, \"z\"));\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join('');\n}\n/**\n * Excavate modules\n * @param modules \n * @param excavation \n * @returns \n */\nexport function excavateModules(modules, excavation) {\n  return modules.slice().map(function (row, y) {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map(function (cell, x) {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\n\n/**\n * Get image settings\n * @param cells The modules of the QR code\n * @param size The size of the QR code\n * @param margin \n * @param imageSettings \n * @returns \n */\nexport function getImageSettings(cells, size, margin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  var numCells = cells.length + margin * 2;\n  var defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  var scale = numCells / size;\n  var w = (imageSettings.width || defaultSize) * scale;\n  var h = (imageSettings.height || defaultSize) * scale;\n  var x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  var y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  var opacity = imageSettings.opacity == null ? 1 : imageSettings.opacity;\n  var excavation = null;\n  if (imageSettings.excavate) {\n    var floorX = Math.floor(x);\n    var floorY = Math.floor(y);\n    var ceilW = Math.ceil(w + x - floorX);\n    var ceilH = Math.ceil(h + y - floorY);\n    excavation = {\n      x: floorX,\n      y: floorY,\n      w: ceilW,\n      h: ceilH\n    };\n  }\n  var crossOrigin = imageSettings.crossOrigin;\n  return {\n    x: x,\n    y: y,\n    h: h,\n    w: w,\n    excavation: excavation,\n    opacity: opacity,\n    crossOrigin: crossOrigin\n  };\n}\n\n/**\n * Get margin size\n * @param needMargin Whether need margin\n * @param marginSize Custom margin size\n * @returns \n */\nexport function getMarginSize(needMargin, marginSize) {\n  if (marginSize != null) {\n    return Math.floor(marginSize);\n  }\n  return needMargin ? SPEC_MARGIN_SIZE : DEFAULT_MARGIN_SIZE;\n}\n/**\n * Check if Path2D is supported\n */\nexport var isSupportPath2d = function () {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();"], "mappings": "AAAA;AACA;;AAEA;;AAEA,SAASA,GAAG,QAAQ,kBAAkB;;AAEtC;AACA,OAAO,IAAIC,eAAe,GAAG;EAC3BC,CAAC,EAAEF,GAAG,CAACG,GAAG;EACVC,CAAC,EAAEJ,GAAG,CAACK,MAAM;EACbC,CAAC,EAAEN,GAAG,CAACO,QAAQ;EACfC,CAAC,EAAER,GAAG,CAACS;AACT,CAAC;;AAED;AACA,OAAO,IAAIC,YAAY,GAAG,GAAG;AAC7B,OAAO,IAAIC,aAAa,GAAG,GAAG;AAC9B,OAAO,IAAIC,wBAAwB,GAAG,SAAS;AAC/C,OAAO,IAAIC,mBAAmB,GAAG,SAAS;AAC1C,OAAO,IAAIC,mBAAmB,GAAG,KAAK;AACtC,OAAO,IAAIC,kBAAkB,GAAG,CAAC;AACjC,OAAO,IAAIC,gBAAgB,GAAG,CAAC;AAC/B,OAAO,IAAIC,mBAAmB,GAAG,CAAC;AAClC,OAAO,IAAIC,iBAAiB,GAAG,GAAG;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EACpC,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EAClF,IAAIG,GAAG,GAAG,EAAE;EACZL,OAAO,CAACM,OAAO,CAAC,UAAUC,GAAG,EAAEC,CAAC,EAAE;IAChC,IAAIC,KAAK,GAAG,IAAI;IAChBF,GAAG,CAACD,OAAO,CAAC,UAAUI,IAAI,EAAEC,CAAC,EAAE;MAC7B,IAAI,CAACD,IAAI,IAAID,KAAK,KAAK,IAAI,EAAE;QAC3BJ,GAAG,CAACO,IAAI,CAAC,GAAG,CAACC,MAAM,CAACJ,KAAK,GAAGR,MAAM,EAAE,GAAG,CAAC,CAACY,MAAM,CAACL,CAAC,GAAGP,MAAM,EAAE,GAAG,CAAC,CAACY,MAAM,CAACF,CAAC,GAAGF,KAAK,EAAE,KAAK,CAAC,CAACI,MAAM,CAACJ,KAAK,GAAGR,MAAM,EAAE,GAAG,CAAC,CAAC;QACtHQ,KAAK,GAAG,IAAI;QACZ;MACF;MACA,IAAIE,CAAC,KAAKJ,GAAG,CAACJ,MAAM,GAAG,CAAC,EAAE;QACxB,IAAI,CAACO,IAAI,EAAE;UACT;QACF;QACA,IAAID,KAAK,KAAK,IAAI,EAAE;UAClBJ,GAAG,CAACO,IAAI,CAAC,GAAG,CAACC,MAAM,CAACF,CAAC,GAAGV,MAAM,EAAE,GAAG,CAAC,CAACY,MAAM,CAACL,CAAC,GAAGP,MAAM,EAAE,QAAQ,CAAC,CAACY,MAAM,CAACF,CAAC,GAAGV,MAAM,EAAE,GAAG,CAAC,CAAC;QAC5F,CAAC,MAAM;UACLI,GAAG,CAACO,IAAI,CAAC,GAAG,CAACC,MAAM,CAACJ,KAAK,GAAGR,MAAM,EAAE,GAAG,CAAC,CAACY,MAAM,CAACL,CAAC,GAAGP,MAAM,EAAE,IAAI,CAAC,CAACY,MAAM,CAACF,CAAC,GAAG,CAAC,GAAGF,KAAK,EAAE,KAAK,CAAC,CAACI,MAAM,CAACJ,KAAK,GAAGR,MAAM,EAAE,GAAG,CAAC,CAAC;QAC7H;QACA;MACF;MACA,IAAIS,IAAI,IAAID,KAAK,KAAK,IAAI,EAAE;QAC1BA,KAAK,GAAGE,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAON,GAAG,CAACS,IAAI,CAAC,EAAE,CAAC;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACf,OAAO,EAAEgB,UAAU,EAAE;EACnD,OAAOhB,OAAO,CAACiB,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,UAAUX,GAAG,EAAEC,CAAC,EAAE;IAC3C,IAAIA,CAAC,GAAGQ,UAAU,CAACR,CAAC,IAAIA,CAAC,IAAIQ,UAAU,CAACR,CAAC,GAAGQ,UAAU,CAACG,CAAC,EAAE;MACxD,OAAOZ,GAAG;IACZ;IACA,OAAOA,GAAG,CAACW,GAAG,CAAC,UAAUR,IAAI,EAAEC,CAAC,EAAE;MAChC,IAAIA,CAAC,GAAGK,UAAU,CAACL,CAAC,IAAIA,CAAC,IAAIK,UAAU,CAACL,CAAC,GAAGK,UAAU,CAACI,CAAC,EAAE;QACxD,OAAOV,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,gBAAgBA,CAACC,KAAK,EAAEC,IAAI,EAAEtB,MAAM,EAAEuB,aAAa,EAAE;EACnE,IAAIA,aAAa,IAAI,IAAI,EAAE;IACzB,OAAO,IAAI;EACb;EACA,IAAIC,QAAQ,GAAGH,KAAK,CAACnB,MAAM,GAAGF,MAAM,GAAG,CAAC;EACxC,IAAIyB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,GAAGzB,iBAAiB,CAAC;EACtD,IAAI+B,KAAK,GAAGJ,QAAQ,GAAGF,IAAI;EAC3B,IAAIH,CAAC,GAAG,CAACI,aAAa,CAACM,KAAK,IAAIJ,WAAW,IAAIG,KAAK;EACpD,IAAIV,CAAC,GAAG,CAACK,aAAa,CAACO,MAAM,IAAIL,WAAW,IAAIG,KAAK;EACrD,IAAIlB,CAAC,GAAGa,aAAa,CAACb,CAAC,IAAI,IAAI,GAAGW,KAAK,CAACnB,MAAM,GAAG,CAAC,GAAGiB,CAAC,GAAG,CAAC,GAAGI,aAAa,CAACb,CAAC,GAAGkB,KAAK;EACpF,IAAIrB,CAAC,GAAGgB,aAAa,CAAChB,CAAC,IAAI,IAAI,GAAGc,KAAK,CAACnB,MAAM,GAAG,CAAC,GAAGgB,CAAC,GAAG,CAAC,GAAGK,aAAa,CAAChB,CAAC,GAAGqB,KAAK;EACpF,IAAIG,OAAO,GAAGR,aAAa,CAACQ,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGR,aAAa,CAACQ,OAAO;EACvE,IAAIhB,UAAU,GAAG,IAAI;EACrB,IAAIQ,aAAa,CAACS,QAAQ,EAAE;IAC1B,IAAIC,MAAM,GAAGP,IAAI,CAACC,KAAK,CAACjB,CAAC,CAAC;IAC1B,IAAIwB,MAAM,GAAGR,IAAI,CAACC,KAAK,CAACpB,CAAC,CAAC;IAC1B,IAAI4B,KAAK,GAAGT,IAAI,CAACU,IAAI,CAACjB,CAAC,GAAGT,CAAC,GAAGuB,MAAM,CAAC;IACrC,IAAII,KAAK,GAAGX,IAAI,CAACU,IAAI,CAAClB,CAAC,GAAGX,CAAC,GAAG2B,MAAM,CAAC;IACrCnB,UAAU,GAAG;MACXL,CAAC,EAAEuB,MAAM;MACT1B,CAAC,EAAE2B,MAAM;MACTf,CAAC,EAAEgB,KAAK;MACRjB,CAAC,EAAEmB;IACL,CAAC;EACH;EACA,IAAIC,WAAW,GAAGf,aAAa,CAACe,WAAW;EAC3C,OAAO;IACL5B,CAAC,EAAEA,CAAC;IACJH,CAAC,EAAEA,CAAC;IACJW,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJJ,UAAU,EAAEA,UAAU;IACtBgB,OAAO,EAAEA,OAAO;IAChBO,WAAW,EAAEA;EACf,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,UAAU,EAAEC,UAAU,EAAE;EACpD,IAAIA,UAAU,IAAI,IAAI,EAAE;IACtB,OAAOf,IAAI,CAACC,KAAK,CAACc,UAAU,CAAC;EAC/B;EACA,OAAOD,UAAU,GAAG7C,gBAAgB,GAAGC,mBAAmB;AAC5D;AACA;AACA;AACA;AACA,OAAO,IAAI8C,eAAe,GAAG,YAAY;EACvC,IAAI;IACF,IAAIC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,IAAID,MAAM,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}