{"ast": null, "code": "/*\nLanguage: F#\nAuthor: <PERSON> <jona<PERSON>@follesoe.no>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <hen<PERSON>@haf.se>\nWebsite: https://docs.microsoft.com/en-us/dotnet/fsharp/\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction fsharp(hljs) {\n  const TYPEPARAM = {\n    begin: '<',\n    end: '>',\n    contains: [hljs.inherit(hljs.TITLE_MODE, {\n      begin: /'[a-zA-Z0-9_]+/\n    })]\n  };\n  return {\n    name: 'F#',\n    aliases: ['fs'],\n    keywords: 'abstract and as assert base begin class default delegate do done ' + 'downcast downto elif else end exception extern false finally for ' + 'fun function global if in inherit inline interface internal lazy let ' + 'match member module mutable namespace new null of open or ' + 'override private public rec return sig static struct then to ' + 'true try type upcast use val void when while with yield',\n    illegal: /\\/\\*/,\n    contains: [{\n      // monad builder keywords (matches before non-bang kws)\n      className: 'keyword',\n      begin: /\\b(yield|return|let|do)!/\n    }, {\n      className: 'string',\n      begin: '@\"',\n      end: '\"',\n      contains: [{\n        begin: '\"\"'\n      }]\n    }, {\n      className: 'string',\n      begin: '\"\"\"',\n      end: '\"\"\"'\n    }, hljs.COMMENT('\\\\(\\\\*(\\\\s)', '\\\\*\\\\)', {\n      contains: [\"self\"]\n    }), {\n      className: 'class',\n      beginKeywords: 'type',\n      end: '\\\\(|=|$',\n      excludeEnd: true,\n      contains: [hljs.UNDERSCORE_TITLE_MODE, TYPEPARAM]\n    }, {\n      className: 'meta',\n      begin: '\\\\[<',\n      end: '>\\\\]',\n      relevance: 10\n    }, {\n      className: 'symbol',\n      begin: '\\\\B(\\'[A-Za-z])\\\\b',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, hljs.C_LINE_COMMENT_MODE, hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      illegal: null\n    }), hljs.C_NUMBER_MODE]\n  };\n}\nmodule.exports = fsharp;", "map": {"version": 3, "names": ["fsharp", "hljs", "TYPEPARAM", "begin", "end", "contains", "inherit", "TITLE_MODE", "name", "aliases", "keywords", "illegal", "className", "COMMENT", "beginKeywords", "excludeEnd", "UNDERSCORE_TITLE_MODE", "relevance", "BACKSLASH_ESCAPE", "C_LINE_COMMENT_MODE", "QUOTE_STRING_MODE", "C_NUMBER_MODE", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/fsharp.js"], "sourcesContent": ["/*\nLanguage: F#\nAuthor: <PERSON> <jona<PERSON>@follesoe.no>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <hen<PERSON>@haf.se>\nWebsite: https://docs.microsoft.com/en-us/dotnet/fsharp/\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction fsharp(hljs) {\n  const TYPEPARAM = {\n    begin: '<',\n    end: '>',\n    contains: [\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: /'[a-zA-Z0-9_]+/\n      })\n    ]\n  };\n\n  return {\n    name: 'F#',\n    aliases: ['fs'],\n    keywords:\n      'abstract and as assert base begin class default delegate do done ' +\n      'downcast downto elif else end exception extern false finally for ' +\n      'fun function global if in inherit inline interface internal lazy let ' +\n      'match member module mutable namespace new null of open or ' +\n      'override private public rec return sig static struct then to ' +\n      'true try type upcast use val void when while with yield',\n    illegal: /\\/\\*/,\n    contains: [\n      {\n        // monad builder keywords (matches before non-bang kws)\n        className: 'keyword',\n        begin: /\\b(yield|return|let|do)!/\n      },\n      {\n        className: 'string',\n        begin: '@\"',\n        end: '\"',\n        contains: [\n          {\n            begin: '\"\"'\n          }\n        ]\n      },\n      {\n        className: 'string',\n        begin: '\"\"\"',\n        end: '\"\"\"'\n      },\n      hljs.COMMENT('\\\\(\\\\*(\\\\s)', '\\\\*\\\\)', {\n        contains: [\"self\"]\n      }),\n      {\n        className: 'class',\n        beginKeywords: 'type',\n        end: '\\\\(|=|$',\n        excludeEnd: true,\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          TYPEPARAM\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '\\\\[<',\n        end: '>\\\\]',\n        relevance: 10\n      },\n      {\n        className: 'symbol',\n        begin: '\\\\B(\\'[A-Za-z])\\\\b',\n        contains: [hljs.BACKSLASH_ESCAPE]\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        illegal: null\n      }),\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = fsharp;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,MAAMC,SAAS,GAAG;IAChBC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CACRJ,IAAI,CAACK,OAAO,CAACL,IAAI,CAACM,UAAU,EAAE;MAC5BJ,KAAK,EAAE;IACT,CAAC,CAAC;EAEN,CAAC;EAED,OAAO;IACLK,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,CAAC,IAAI,CAAC;IACfC,QAAQ,EACN,mEAAmE,GACnE,mEAAmE,GACnE,uEAAuE,GACvE,4DAA4D,GAC5D,+DAA+D,GAC/D,yDAAyD;IAC3DC,OAAO,EAAE,MAAM;IACfN,QAAQ,EAAE,CACR;MACE;MACAO,SAAS,EAAE,SAAS;MACpBT,KAAK,EAAE;IACT,CAAC,EACD;MACES,SAAS,EAAE,QAAQ;MACnBT,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,CACR;QACEF,KAAK,EAAE;MACT,CAAC;IAEL,CAAC,EACD;MACES,SAAS,EAAE,QAAQ;MACnBT,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE;IACP,CAAC,EACDH,IAAI,CAACY,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE;MACpCR,QAAQ,EAAE,CAAC,MAAM;IACnB,CAAC,CAAC,EACF;MACEO,SAAS,EAAE,OAAO;MAClBE,aAAa,EAAE,MAAM;MACrBV,GAAG,EAAE,SAAS;MACdW,UAAU,EAAE,IAAI;MAChBV,QAAQ,EAAE,CACRJ,IAAI,CAACe,qBAAqB,EAC1Bd,SAAS;IAEb,CAAC,EACD;MACEU,SAAS,EAAE,MAAM;MACjBT,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,MAAM;MACXa,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,QAAQ;MACnBT,KAAK,EAAE,oBAAoB;MAC3BE,QAAQ,EAAE,CAACJ,IAAI,CAACiB,gBAAgB;IAClC,CAAC,EACDjB,IAAI,CAACkB,mBAAmB,EACxBlB,IAAI,CAACK,OAAO,CAACL,IAAI,CAACmB,iBAAiB,EAAE;MACnCT,OAAO,EAAE;IACX,CAAC,CAAC,EACFV,IAAI,CAACoB,aAAa;EAEtB,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGvB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}