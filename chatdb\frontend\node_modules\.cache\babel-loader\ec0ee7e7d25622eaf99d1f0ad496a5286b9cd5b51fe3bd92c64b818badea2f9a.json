{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, parseNDigits } from \"../utils.js\";\nexport var FractionOfSecondParser = /*#__PURE__*/function (_Parser) {\n  _inherits(FractionOfSecondParser, _Parser);\n  var _super = _createSuper(FractionOfSecondParser);\n  function FractionOfSecondParser() {\n    var _this;\n    _classCallCheck(this, FractionOfSecondParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 30);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['t', 'T']);\n    return _this;\n  }\n  _createClass(FractionOfSecondParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token) {\n      var valueCallback = function valueCallback(value) {\n        return Math.floor(value * Math.pow(10, -token.length + 3));\n      };\n      return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      date.setUTCMilliseconds(value);\n      return date;\n    }\n  }]);\n  return FractionOfSecondParser;\n}(Parser);", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "<PERSON><PERSON><PERSON>", "mapValue", "parseNDigits", "FractionOfSecondParser", "_<PERSON><PERSON>r", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "key", "value", "parse", "dateString", "token", "valueCallback", "Math", "floor", "pow", "set", "date", "_flags", "setUTCMilliseconds"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/date-fns/esm/parse/_lib/parsers/FractionOfSecondParser.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, parseNDigits } from \"../utils.js\";\nexport var FractionOfSecondParser = /*#__PURE__*/function (_Parser) {\n  _inherits(FractionOfSecondParser, _Parser);\n  var _super = _createSuper(FractionOfSecondParser);\n  function FractionOfSecondParser() {\n    var _this;\n    _classCallCheck(this, FractionOfSecondParser);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"priority\", 30);\n    _defineProperty(_assertThisInitialized(_this), \"incompatibleTokens\", ['t', 'T']);\n    return _this;\n  }\n  _createClass(FractionOfSecondParser, [{\n    key: \"parse\",\n    value: function parse(dateString, token) {\n      var valueCallback = function valueCallback(value) {\n        return Math.floor(value * Math.pow(10, -token.length + 3));\n      };\n      return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }, {\n    key: \"set\",\n    value: function set(date, _flags, value) {\n      date.setUTCMilliseconds(value);\n      return date;\n    }\n  }]);\n  return FractionOfSecondParser;\n}(Parser);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,aAAa;AACpD,OAAO,IAAIC,sBAAsB,GAAG,aAAa,UAAUC,OAAO,EAAE;EAClEP,SAAS,CAACM,sBAAsB,EAAEC,OAAO,CAAC;EAC1C,IAAIC,MAAM,GAAGP,YAAY,CAACK,sBAAsB,CAAC;EACjD,SAASA,sBAAsBA,CAAA,EAAG;IAChC,IAAIG,KAAK;IACTZ,eAAe,CAAC,IAAI,EAAES,sBAAsB,CAAC;IAC7C,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDX,eAAe,CAACH,sBAAsB,CAACU,KAAK,CAAC,EAAE,UAAU,EAAE,EAAE,CAAC;IAC9DP,eAAe,CAACH,sBAAsB,CAACU,KAAK,CAAC,EAAE,oBAAoB,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAChF,OAAOA,KAAK;EACd;EACAX,YAAY,CAACQ,sBAAsB,EAAE,CAAC;IACpCa,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAE;MACvC,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACJ,KAAK,EAAE;QAChD,OAAOK,IAAI,CAACC,KAAK,CAACN,KAAK,GAAGK,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE,CAACJ,KAAK,CAACX,MAAM,GAAG,CAAC,CAAC,CAAC;MAC5D,CAAC;MACD,OAAOR,QAAQ,CAACC,YAAY,CAACkB,KAAK,CAACX,MAAM,EAAEU,UAAU,CAAC,EAAEE,aAAa,CAAC;IACxE;EACF,CAAC,EAAE;IACDL,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASQ,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEV,KAAK,EAAE;MACvCS,IAAI,CAACE,kBAAkB,CAACX,KAAK,CAAC;MAC9B,OAAOS,IAAI;IACb;EACF,CAAC,CAAC,CAAC;EACH,OAAOvB,sBAAsB;AAC/B,CAAC,CAACH,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}