{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nexport default function Icon(_ref) {\n  var icon = _ref.icon,\n    props = _ref.props,\n    children = _ref.children;\n  var iconNode;\n  if (icon === null || icon === false) {\n    return null;\n  }\n  if (typeof icon === 'function') {\n    iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, props));\n  } else if (typeof icon !== \"boolean\") {\n    // Compatible for origin definition\n    iconNode = icon;\n  }\n  return iconNode || children || null;\n}", "map": {"version": 3, "names": ["_objectSpread", "React", "Icon", "_ref", "icon", "props", "children", "iconNode", "createElement"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/rc-menu/es/Icon.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nexport default function Icon(_ref) {\n  var icon = _ref.icon,\n    props = _ref.props,\n    children = _ref.children;\n  var iconNode;\n  if (icon === null || icon === false) {\n    return null;\n  }\n  if (typeof icon === 'function') {\n    iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, props));\n  } else if (typeof icon !== \"boolean\") {\n    // Compatible for origin definition\n    iconNode = icon;\n  }\n  return iconNode || children || null;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,IAAIA,CAACC,IAAI,EAAE;EACjC,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,KAAK,GAAGF,IAAI,CAACE,KAAK;IAClBC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;EAC1B,IAAIC,QAAQ;EACZ,IAAIH,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,EAAE;IACnC,OAAO,IAAI;EACb;EACA,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAC9BG,QAAQ,GAAG,aAAaN,KAAK,CAACO,aAAa,CAACJ,IAAI,EAAEJ,aAAa,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,CAAC;EAC7E,CAAC,MAAM,IAAI,OAAOD,IAAI,KAAK,SAAS,EAAE;IACpC;IACAG,QAAQ,GAAGH,IAAI;EACjB;EACA,OAAOG,QAAQ,IAAID,QAAQ,IAAI,IAAI;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}