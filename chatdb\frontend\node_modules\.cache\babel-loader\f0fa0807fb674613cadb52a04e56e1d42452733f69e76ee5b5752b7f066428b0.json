{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CameraOutlinedSvg from \"@ant-design/icons-svg/es/asn/CameraOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CameraOutlined = function CameraOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CameraOutlinedSvg\n  }));\n};\n\n/**![camera](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NCAyNDhINzI4bC0zMi40LTkwLjhhMzIuMDcgMzIuMDcgMCAwMC0zMC4yLTIxLjJIMzU4LjZjLTEzLjUgMC0yNS42IDguNS0zMC4xIDIxLjJMMjk2IDI0OEgxNjBjLTQ0LjIgMC04MCAzNS44LTgwIDgwdjQ1NmMwIDQ0LjIgMzUuOCA4MCA4MCA4MGg3MDRjNDQuMiAwIDgwLTM1LjggODAtODBWMzI4YzAtNDQuMi0zNS44LTgwLTgwLTgwem04IDUzNmMwIDQuNC0zLjYgOC04IDhIMTYwYy00LjQgMC04LTMuNi04LThWMzI4YzAtNC40IDMuNi04IDgtOGgxODYuN2wxNy4xLTQ3LjggMjIuOS02NC4yaDI1MC41bDIyLjkgNjQuMiAxNy4xIDQ3LjhIODY0YzQuNCAwIDggMy42IDggOHY0NTZ6TTUxMiAzODRjLTg4LjQgMC0xNjAgNzEuNi0xNjAgMTYwczcxLjYgMTYwIDE2MCAxNjAgMTYwLTcxLjYgMTYwLTE2MC03MS42LTE2MC0xNjAtMTYwem0wIDI1NmMtNTMgMC05Ni00My05Ni05NnM0My05NiA5Ni05NiA5NiA0MyA5NiA5Ni00MyA5Ni05NiA5NnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CameraOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CameraOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "CameraOutlinedSvg", "AntdIcon", "CameraOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/CameraOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CameraOutlinedSvg from \"@ant-design/icons-svg/es/asn/CameraOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CameraOutlined = function CameraOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CameraOutlinedSvg\n  }));\n};\n\n/**![camera](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NCAyNDhINzI4bC0zMi40LTkwLjhhMzIuMDcgMzIuMDcgMCAwMC0zMC4yLTIxLjJIMzU4LjZjLTEzLjUgMC0yNS42IDguNS0zMC4xIDIxLjJMMjk2IDI0OEgxNjBjLTQ0LjIgMC04MCAzNS44LTgwIDgwdjQ1NmMwIDQ0LjIgMzUuOCA4MCA4MCA4MGg3MDRjNDQuMiAwIDgwLTM1LjggODAtODBWMzI4YzAtNDQuMi0zNS44LTgwLTgwLTgwem04IDUzNmMwIDQuNC0zLjYgOC04IDhIMTYwYy00LjQgMC04LTMuNi04LThWMzI4YzAtNC40IDMuNi04IDgtOGgxODYuN2wxNy4xLTQ3LjggMjIuOS02NC4yaDI1MC41bDIyLjkgNjQuMiAxNy4xIDQ3LjhIODY0YzQuNCAwIDggMy42IDggOHY0NTZ6TTUxMiAzODRjLTg4LjQgMC0xNjAgNzEuNi0xNjAgMTYwczcxLjYgMTYwIDE2MCAxNjAgMTYwLTcxLjYgMTYwLTE2MC03MS42LTE2MC0xNjAtMTYwem0wIDI1NmMtNTMgMC05Ni00My05Ni05NnM0My05NiA5Ni05NiA5NiA0MyA5NiA5Ni00MyA5Ni05NiA5NnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CameraOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CameraOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,cAAc,CAAC;AAC3D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,gBAAgB;AACxC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}