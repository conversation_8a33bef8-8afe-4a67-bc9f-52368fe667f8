{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport { devUseWarning } from '../_util/warning';\nexport default function useIcons(_ref) {\n  let {\n    suffixIcon,\n    clearIcon,\n    menuItemSelectedIcon,\n    removeIcon,\n    loading,\n    multiple,\n    hasFeedback,\n    prefixCls,\n    showSuffixIcon,\n    feedbackIcon,\n    showArrow,\n    componentName\n  } = _ref;\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning(componentName);\n    warning.deprecated(!clearIcon, 'clearIcon', 'allowClear={{ clearIcon: React.ReactNode }}');\n  }\n  // Clear Icon\n  const mergedClearIcon = clearIcon !== null && clearIcon !== void 0 ? clearIcon : /*#__PURE__*/React.createElement(CloseCircleFilled, null);\n  // Validation Feedback Icon\n  const getSuffixIconNode = arrowIcon => {\n    if (suffixIcon === null && !hasFeedback && !showArrow) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, null, showSuffixIcon !== false && arrowIcon, hasFeedback && feedbackIcon);\n  };\n  // Arrow item icon\n  let mergedSuffixIcon = null;\n  if (suffixIcon !== undefined) {\n    mergedSuffixIcon = getSuffixIconNode(suffixIcon);\n  } else if (loading) {\n    mergedSuffixIcon = getSuffixIconNode(/*#__PURE__*/React.createElement(LoadingOutlined, {\n      spin: true\n    }));\n  } else {\n    const iconCls = `${prefixCls}-suffix`;\n    mergedSuffixIcon = _ref2 => {\n      let {\n        open,\n        showSearch\n      } = _ref2;\n      if (open && showSearch) {\n        return getSuffixIconNode(/*#__PURE__*/React.createElement(SearchOutlined, {\n          className: iconCls\n        }));\n      }\n      return getSuffixIconNode(/*#__PURE__*/React.createElement(DownOutlined, {\n        className: iconCls\n      }));\n    };\n  }\n  // Checked item icon\n  let mergedItemIcon = null;\n  if (menuItemSelectedIcon !== undefined) {\n    mergedItemIcon = menuItemSelectedIcon;\n  } else if (multiple) {\n    mergedItemIcon = /*#__PURE__*/React.createElement(CheckOutlined, null);\n  } else {\n    mergedItemIcon = null;\n  }\n  let mergedRemoveIcon = null;\n  if (removeIcon !== undefined) {\n    mergedRemoveIcon = removeIcon;\n  } else {\n    mergedRemoveIcon = /*#__PURE__*/React.createElement(CloseOutlined, null);\n  }\n  return {\n    clearIcon: mergedClearIcon,\n    suffixIcon: mergedSuffixIcon,\n    itemIcon: mergedItemIcon,\n    removeIcon: mergedRemoveIcon\n  };\n}", "map": {"version": 3, "names": ["React", "CheckOutlined", "CloseCircleFilled", "CloseOutlined", "DownOutlined", "LoadingOutlined", "SearchOutlined", "devUseW<PERSON>ning", "useIcons", "_ref", "suffixIcon", "clearIcon", "menuItemSelectedIcon", "removeIcon", "loading", "multiple", "hasFeedback", "prefixCls", "showSuffixIcon", "feedbackIcon", "showArrow", "componentName", "process", "env", "NODE_ENV", "warning", "deprecated", "mergedClearIcon", "createElement", "getSuffixIconNode", "arrowIcon", "Fragment", "mergedSuffixIcon", "undefined", "spin", "iconCls", "_ref2", "open", "showSearch", "className", "mergedItemIcon", "mergedRemoveIcon", "itemIcon"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/select/useIcons.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CloseCircleFilled from \"@ant-design/icons/es/icons/CloseCircleFilled\";\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport DownOutlined from \"@ant-design/icons/es/icons/DownOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport { devUseWarning } from '../_util/warning';\nexport default function useIcons(_ref) {\n  let {\n    suffixIcon,\n    clearIcon,\n    menuItemSelectedIcon,\n    removeIcon,\n    loading,\n    multiple,\n    hasFeedback,\n    prefixCls,\n    showSuffixIcon,\n    feedbackIcon,\n    showArrow,\n    componentName\n  } = _ref;\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning(componentName);\n    warning.deprecated(!clearIcon, 'clearIcon', 'allowClear={{ clearIcon: React.ReactNode }}');\n  }\n  // Clear Icon\n  const mergedClearIcon = clearIcon !== null && clearIcon !== void 0 ? clearIcon : /*#__PURE__*/React.createElement(CloseCircleFilled, null);\n  // Validation Feedback Icon\n  const getSuffixIconNode = arrowIcon => {\n    if (suffixIcon === null && !hasFeedback && !showArrow) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, null, showSuffixIcon !== false && arrowIcon, hasFeedback && feedbackIcon);\n  };\n  // Arrow item icon\n  let mergedSuffixIcon = null;\n  if (suffixIcon !== undefined) {\n    mergedSuffixIcon = getSuffixIconNode(suffixIcon);\n  } else if (loading) {\n    mergedSuffixIcon = getSuffixIconNode(/*#__PURE__*/React.createElement(LoadingOutlined, {\n      spin: true\n    }));\n  } else {\n    const iconCls = `${prefixCls}-suffix`;\n    mergedSuffixIcon = _ref2 => {\n      let {\n        open,\n        showSearch\n      } = _ref2;\n      if (open && showSearch) {\n        return getSuffixIconNode(/*#__PURE__*/React.createElement(SearchOutlined, {\n          className: iconCls\n        }));\n      }\n      return getSuffixIconNode(/*#__PURE__*/React.createElement(DownOutlined, {\n        className: iconCls\n      }));\n    };\n  }\n  // Checked item icon\n  let mergedItemIcon = null;\n  if (menuItemSelectedIcon !== undefined) {\n    mergedItemIcon = menuItemSelectedIcon;\n  } else if (multiple) {\n    mergedItemIcon = /*#__PURE__*/React.createElement(CheckOutlined, null);\n  } else {\n    mergedItemIcon = null;\n  }\n  let mergedRemoveIcon = null;\n  if (removeIcon !== undefined) {\n    mergedRemoveIcon = removeIcon;\n  } else {\n    mergedRemoveIcon = /*#__PURE__*/React.createElement(CloseOutlined, null);\n  }\n  return {\n    clearIcon: mergedClearIcon,\n    suffixIcon: mergedSuffixIcon,\n    itemIcon: mergedItemIcon,\n    removeIcon: mergedRemoveIcon\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,iBAAiB,MAAM,8CAA8C;AAC5E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAI;IACFC,UAAU;IACVC,SAAS;IACTC,oBAAoB;IACpBC,UAAU;IACVC,OAAO;IACPC,QAAQ;IACRC,WAAW;IACXC,SAAS;IACTC,cAAc;IACdC,YAAY;IACZC,SAAS;IACTC;EACF,CAAC,GAAGZ,IAAI;EACR,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGlB,aAAa,CAACc,aAAa,CAAC;IAC5CI,OAAO,CAACC,UAAU,CAAC,CAACf,SAAS,EAAE,WAAW,EAAE,6CAA6C,CAAC;EAC5F;EACA;EACA,MAAMgB,eAAe,GAAGhB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,aAAaX,KAAK,CAAC4B,aAAa,CAAC1B,iBAAiB,EAAE,IAAI,CAAC;EAC1I;EACA,MAAM2B,iBAAiB,GAAGC,SAAS,IAAI;IACrC,IAAIpB,UAAU,KAAK,IAAI,IAAI,CAACM,WAAW,IAAI,CAACI,SAAS,EAAE;MACrD,OAAO,IAAI;IACb;IACA,OAAO,aAAapB,KAAK,CAAC4B,aAAa,CAAC5B,KAAK,CAAC+B,QAAQ,EAAE,IAAI,EAAEb,cAAc,KAAK,KAAK,IAAIY,SAAS,EAAEd,WAAW,IAAIG,YAAY,CAAC;EACnI,CAAC;EACD;EACA,IAAIa,gBAAgB,GAAG,IAAI;EAC3B,IAAItB,UAAU,KAAKuB,SAAS,EAAE;IAC5BD,gBAAgB,GAAGH,iBAAiB,CAACnB,UAAU,CAAC;EAClD,CAAC,MAAM,IAAII,OAAO,EAAE;IAClBkB,gBAAgB,GAAGH,iBAAiB,CAAC,aAAa7B,KAAK,CAAC4B,aAAa,CAACvB,eAAe,EAAE;MACrF6B,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,MAAM;IACL,MAAMC,OAAO,GAAG,GAAGlB,SAAS,SAAS;IACrCe,gBAAgB,GAAGI,KAAK,IAAI;MAC1B,IAAI;QACFC,IAAI;QACJC;MACF,CAAC,GAAGF,KAAK;MACT,IAAIC,IAAI,IAAIC,UAAU,EAAE;QACtB,OAAOT,iBAAiB,CAAC,aAAa7B,KAAK,CAAC4B,aAAa,CAACtB,cAAc,EAAE;UACxEiC,SAAS,EAAEJ;QACb,CAAC,CAAC,CAAC;MACL;MACA,OAAON,iBAAiB,CAAC,aAAa7B,KAAK,CAAC4B,aAAa,CAACxB,YAAY,EAAE;QACtEmC,SAAS,EAAEJ;MACb,CAAC,CAAC,CAAC;IACL,CAAC;EACH;EACA;EACA,IAAIK,cAAc,GAAG,IAAI;EACzB,IAAI5B,oBAAoB,KAAKqB,SAAS,EAAE;IACtCO,cAAc,GAAG5B,oBAAoB;EACvC,CAAC,MAAM,IAAIG,QAAQ,EAAE;IACnByB,cAAc,GAAG,aAAaxC,KAAK,CAAC4B,aAAa,CAAC3B,aAAa,EAAE,IAAI,CAAC;EACxE,CAAC,MAAM;IACLuC,cAAc,GAAG,IAAI;EACvB;EACA,IAAIC,gBAAgB,GAAG,IAAI;EAC3B,IAAI5B,UAAU,KAAKoB,SAAS,EAAE;IAC5BQ,gBAAgB,GAAG5B,UAAU;EAC/B,CAAC,MAAM;IACL4B,gBAAgB,GAAG,aAAazC,KAAK,CAAC4B,aAAa,CAACzB,aAAa,EAAE,IAAI,CAAC;EAC1E;EACA,OAAO;IACLQ,SAAS,EAAEgB,eAAe;IAC1BjB,UAAU,EAAEsB,gBAAgB;IAC5BU,QAAQ,EAAEF,cAAc;IACxB3B,UAAU,EAAE4B;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}