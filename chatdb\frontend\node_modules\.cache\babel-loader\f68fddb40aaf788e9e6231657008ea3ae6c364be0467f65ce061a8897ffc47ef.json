{"ast": null, "code": "var pi = Math.PI,\n  halfPi = pi / 2;\nexport function sinIn(t) {\n  return +t === 1 ? 1 : 1 - Math.cos(t * halfPi);\n}\nexport function sinOut(t) {\n  return Math.sin(t * halfPi);\n}\nexport function sinInOut(t) {\n  return (1 - Math.cos(pi * t)) / 2;\n}", "map": {"version": 3, "names": ["pi", "Math", "PI", "halfPi", "sinIn", "t", "cos", "sinOut", "sin", "sinInOut"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/d3-ease/src/sin.js"], "sourcesContent": ["var pi = Math.PI,\n    halfPi = pi / 2;\n\nexport function sinIn(t) {\n  return (+t === 1) ? 1 : 1 - Math.cos(t * halfPi);\n}\n\nexport function sinOut(t) {\n  return Math.sin(t * halfPi);\n}\n\nexport function sinInOut(t) {\n  return (1 - Math.cos(pi * t)) / 2;\n}\n"], "mappings": "AAAA,IAAIA,EAAE,GAAGC,IAAI,CAACC,EAAE;EACZC,MAAM,GAAGH,EAAE,GAAG,CAAC;AAEnB,OAAO,SAASI,KAAKA,CAACC,CAAC,EAAE;EACvB,OAAQ,CAACA,CAAC,KAAK,CAAC,GAAI,CAAC,GAAG,CAAC,GAAGJ,IAAI,CAACK,GAAG,CAACD,CAAC,GAAGF,MAAM,CAAC;AAClD;AAEA,OAAO,SAASI,MAAMA,CAACF,CAAC,EAAE;EACxB,OAAOJ,IAAI,CAACO,GAAG,CAACH,CAAC,GAAGF,MAAM,CAAC;AAC7B;AAEA,OAAO,SAASM,QAAQA,CAACJ,CAAC,EAAE;EAC1B,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAACK,GAAG,CAACN,EAAE,GAAGK,CAAC,CAAC,IAAI,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}