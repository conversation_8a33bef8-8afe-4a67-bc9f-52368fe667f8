{"ast": null, "code": "import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextThursday\n * @category Weekday Helpers\n * @summary When is the next Thursday?\n *\n * @description\n * When is the next Thursday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Thursday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Thursday after Mar, 22, 2020?\n * const result = nextThursday(new Date(2020, 2, 22))\n * //=> Thur Mar 26 2020 00:00:00\n */\nexport default function nextThursday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 4);\n}", "map": {"version": 3, "names": ["nextDay", "requiredArgs", "nextThursday", "date", "arguments"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/date-fns/esm/nextThursday/index.js"], "sourcesContent": ["import nextDay from \"../nextDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name nextThursday\n * @category Weekday Helpers\n * @summary When is the next Thursday?\n *\n * @description\n * When is the next Thursday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the next Thursday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the next Thursday after Mar, 22, 2020?\n * const result = nextThursday(new Date(2020, 2, 22))\n * //=> Thur Mar 26 2020 00:00:00\n */\nexport default function nextThursday(date) {\n  requiredArgs(1, arguments);\n  return nextDay(date, 4);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,qBAAqB;AACzC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,IAAI,EAAE;EACzCF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,OAAO,CAACG,IAAI,EAAE,CAAC,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}