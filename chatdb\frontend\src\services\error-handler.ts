/**
 * 统一错误处理和用户反馈系统
 * 提供错误分类、用户友好的错误消息和自动恢复机制
 */
import { message, notification } from 'antd';

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'NETWORK',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN'
}

// 错误严重程度
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  code?: string;
  details?: any;
  timestamp: Date;
  context?: Record<string, any>;
  recoverable: boolean;
  retryable: boolean;
}

// 错误处理配置
interface ErrorHandlerConfig {
  enableLogging: boolean;
  enableUserNotification: boolean;
  enableAutoRetry: boolean;
  maxRetryAttempts: number;
  retryDelay: number;
}

class ErrorHandler {
  private config: ErrorHandlerConfig;
  private errorLog: ErrorInfo[] = [];
  private retryAttempts: Map<string, number> = new Map();

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableLogging: true,
      enableUserNotification: true,
      enableAutoRetry: true,
      maxRetryAttempts: 3,
      retryDelay: 1000,
      ...config
    };
  }

  /**
   * 处理错误的主要方法
   */
  handle(error: Error | any, context?: Record<string, any>): ErrorInfo {
    const errorInfo = this.classifyError(error, context);
    
    if (this.config.enableLogging) {
      this.logError(errorInfo);
    }

    if (this.config.enableUserNotification) {
      this.notifyUser(errorInfo);
    }

    return errorInfo;
  }

  /**
   * 错误分类和信息提取
   */
  private classifyError(error: Error | any, context?: Record<string, any>): ErrorInfo {
    let type = ErrorType.UNKNOWN;
    let severity = ErrorSeverity.MEDIUM;
    let userMessage = '操作失败，请稍后再试';
    let recoverable = true;
    let retryable = false;

    const message = error?.message || String(error);
    const code = error?.code || error?.status?.toString();

    // 网络错误
    if (message.includes('网络') || message.includes('连接') || 
        message.includes('Network') || error?.code === 'NETWORK_ERROR') {
      type = ErrorType.NETWORK;
      severity = ErrorSeverity.HIGH;
      userMessage = '网络连接失败，请检查网络连接后重试';
      retryable = true;
    }
    // 服务器错误 (5xx)
    else if (error?.status >= 500 && error?.status < 600) {
      type = ErrorType.SERVER;
      severity = ErrorSeverity.HIGH;
      userMessage = '服务器暂时不可用，请稍后再试';
      retryable = true;
    }
    // 客户端错误 (4xx)
    else if (error?.status >= 400 && error?.status < 500) {
      type = ErrorType.CLIENT;
      severity = ErrorSeverity.MEDIUM;
      
      if (error?.status === 401) {
        type = ErrorType.AUTHENTICATION;
        userMessage = '身份验证失败，请重新登录';
        recoverable = false;
      } else if (error?.status === 403) {
        type = ErrorType.AUTHORIZATION;
        userMessage = '权限不足，无法执行此操作';
        recoverable = false;
      } else if (error?.status === 400) {
        type = ErrorType.VALIDATION;
        userMessage = '请求参数有误，请检查输入内容';
      } else if (error?.status === 404) {
        userMessage = '请求的资源不存在';
      } else if (error?.status === 429) {
        userMessage = '请求过于频繁，请稍后再试';
        retryable = true;
      }
    }
    // 超时错误
    else if (message.includes('timeout') || message.includes('超时')) {
      type = ErrorType.TIMEOUT;
      severity = ErrorSeverity.MEDIUM;
      userMessage = '请求超时，请稍后再试';
      retryable = true;
    }
    // 验证错误
    else if (message.includes('validation') || message.includes('验证')) {
      type = ErrorType.VALIDATION;
      severity = ErrorSeverity.LOW;
      userMessage = '输入内容有误，请检查后重试';
    }

    return {
      type,
      severity,
      message,
      userMessage,
      code,
      details: error,
      timestamp: new Date(),
      context,
      recoverable,
      retryable
    };
  }

  /**
   * 记录错误日志
   */
  private logError(errorInfo: ErrorInfo): void {
    this.errorLog.push(errorInfo);
    
    // 只保留最近1000条错误记录
    if (this.errorLog.length > 1000) {
      this.errorLog = this.errorLog.slice(-1000);
    }

    // 控制台输出
    const logLevel = this.getLogLevel(errorInfo.severity);
    console[logLevel]('错误处理:', {
      type: errorInfo.type,
      severity: errorInfo.severity,
      message: errorInfo.message,
      userMessage: errorInfo.userMessage,
      code: errorInfo.code,
      context: errorInfo.context,
      timestamp: errorInfo.timestamp
    });
  }

  private getLogLevel(severity: ErrorSeverity): 'error' | 'warn' | 'info' {
    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        return 'error';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.LOW:
        return 'info';
      default:
        return 'error';
    }
  }

  /**
   * 用户通知
   */
  private notifyUser(errorInfo: ErrorInfo): void {
    const { severity, userMessage, type, recoverable } = errorInfo;

    if (severity === ErrorSeverity.CRITICAL || severity === ErrorSeverity.HIGH) {
      notification.error({
        message: '操作失败',
        description: userMessage,
        duration: 6,
        placement: 'topRight',
      });
    } else if (severity === ErrorSeverity.MEDIUM) {
      message.error(userMessage, 4);
    } else {
      message.warning(userMessage, 3);
    }

    // 对于不可恢复的错误，提供额外指导
    if (!recoverable) {
      setTimeout(() => {
        notification.info({
          message: '需要您的操作',
          description: this.getRecoveryGuidance(type),
          duration: 8,
          placement: 'topRight',
        });
      }, 1000);
    }
  }

  private getRecoveryGuidance(type: ErrorType): string {
    switch (type) {
      case ErrorType.AUTHENTICATION:
        return '请刷新页面重新登录，或联系管理员检查账户状态';
      case ErrorType.AUTHORIZATION:
        return '如需相关权限，请联系系统管理员';
      case ErrorType.NETWORK:
        return '请检查网络连接，确保能够访问服务器';
      default:
        return '如问题持续存在，请联系技术支持';
    }
  }

  /**
   * 自动重试机制
   */
  async retry<T>(
    operation: () => Promise<T>,
    errorInfo: ErrorInfo,
    operationId?: string
  ): Promise<T> {
    if (!errorInfo.retryable || !this.config.enableAutoRetry) {
      throw new Error(errorInfo.userMessage);
    }

    const id = operationId || `${Date.now()}_${Math.random()}`;
    const attempts = this.retryAttempts.get(id) || 0;

    if (attempts >= this.config.maxRetryAttempts) {
      this.retryAttempts.delete(id);
      throw new Error(`重试${this.config.maxRetryAttempts}次后仍然失败: ${errorInfo.userMessage}`);
    }

    this.retryAttempts.set(id, attempts + 1);

    // 指数退避延迟
    const delay = this.config.retryDelay * Math.pow(2, attempts);
    await new Promise(resolve => setTimeout(resolve, delay));

    try {
      const result = await operation();
      this.retryAttempts.delete(id);
      
      if (attempts > 0) {
        message.success(`操作成功（重试${attempts + 1}次）`);
      }
      
      return result;
    } catch (error) {
      const newErrorInfo = this.classifyError(error);
      return this.retry(operation, newErrorInfo, id);
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recent: ErrorInfo[];
  } {
    const byType = {} as Record<ErrorType, number>;
    const bySeverity = {} as Record<ErrorSeverity, number>;

    // 初始化计数器
    Object.values(ErrorType).forEach(type => byType[type] = 0);
    Object.values(ErrorSeverity).forEach(severity => bySeverity[severity] = 0);

    // 统计错误
    this.errorLog.forEach(error => {
      byType[error.type]++;
      bySeverity[error.severity]++;
    });

    return {
      total: this.errorLog.length,
      byType,
      bySeverity,
      recent: this.errorLog.slice(-10) // 最近10条错误
    };
  }

  /**
   * 清空错误日志
   */
  clearErrorLog(): void {
    this.errorLog = [];
    this.retryAttempts.clear();
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler();

// 便捷方法
export const handleError = (error: Error | any, context?: Record<string, any>) => {
  return errorHandler.handle(error, context);
};

export const retryOperation = <T>(
  operation: () => Promise<T>,
  context?: Record<string, any>
) => {
  return async (): Promise<T> => {
    try {
      return await operation();
    } catch (error) {
      const errorInfo = errorHandler.handle(error, context);
      return errorHandler.retry(operation, errorInfo);
    }
  };
};

// React Hook for error handling
export const useErrorHandler = () => {
  const handleError = (error: Error | any, context?: Record<string, any>) => {
    return errorHandler.handle(error, context);
  };

  const retry = async <T>(operation: () => Promise<T>, context?: Record<string, any>) => {
    try {
      return await operation();
    } catch (error) {
      const errorInfo = errorHandler.handle(error, context);
      return errorHandler.retry(operation, errorInfo);
    }
  };

  return { handleError, retry, getStats: () => errorHandler.getErrorStats() };
};
