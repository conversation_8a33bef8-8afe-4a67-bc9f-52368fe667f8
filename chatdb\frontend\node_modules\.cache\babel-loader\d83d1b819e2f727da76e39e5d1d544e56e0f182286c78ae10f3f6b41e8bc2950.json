{"ast": null, "code": "import * as React from 'react';\nexport const defaultPrefixCls = 'ant';\nexport const defaultIconPrefixCls = 'anticon';\nexport const Variants = ['outlined', 'borderless', 'filled', 'underlined'];\nconst defaultGetPrefixCls = (suffixCls, customizePrefixCls) => {\n  if (customizePrefixCls) {\n    return customizePrefixCls;\n  }\n  return suffixCls ? `${defaultPrefixCls}-${suffixCls}` : defaultPrefixCls;\n};\n// zombieJ: 🚨 Do not pass `defaultRenderEmpty` here since it will cause circular dependency.\nexport const ConfigContext = /*#__PURE__*/React.createContext({\n  // We provide a default function for Context without provider\n  getPrefixCls: defaultGetPrefixCls,\n  iconPrefixCls: defaultIconPrefixCls\n});\nexport const {\n  Consumer: ConfigConsumer\n} = ConfigContext;\nconst EMPTY_OBJECT = {};\n/**\n * Get ConfigProvider configured component props.\n * This help to reduce bundle size for saving `?.` operator.\n * Do not use as `useMemo` deps since we do not cache the object here.\n *\n * NOTE: not refactor this with `useMemo` since memo will cost another memory space,\n * which will waste both compare calculation & memory.\n */\nexport function useComponentConfig(propName) {\n  const context = React.useContext(ConfigContext);\n  const {\n    getPrefixCls,\n    direction,\n    getPopupContainer\n  } = context;\n  const propValue = context[propName];\n  return Object.assign(Object.assign({\n    classNames: EMPTY_OBJECT,\n    styles: EMPTY_OBJECT\n  }, propValue), {\n    getPrefixCls,\n    direction,\n    getPopupContainer\n  });\n}", "map": {"version": 3, "names": ["React", "defaultPrefixCls", "defaultIconPrefixCls", "Variants", "defaultGetPrefixCls", "suffixCls", "customizePrefixCls", "ConfigContext", "createContext", "getPrefixCls", "iconPrefixCls", "Consumer", "ConfigConsumer", "EMPTY_OBJECT", "useComponentConfig", "propName", "context", "useContext", "direction", "getPopupContainer", "propValue", "Object", "assign", "classNames", "styles"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/config-provider/context.js"], "sourcesContent": ["import * as React from 'react';\nexport const defaultPrefixCls = 'ant';\nexport const defaultIconPrefixCls = 'anticon';\nexport const Variants = ['outlined', 'borderless', 'filled', 'underlined'];\nconst defaultGetPrefixCls = (suffixCls, customizePrefixCls) => {\n  if (customizePrefixCls) {\n    return customizePrefixCls;\n  }\n  return suffixCls ? `${defaultPrefixCls}-${suffixCls}` : defaultPrefixCls;\n};\n// zombieJ: 🚨 Do not pass `defaultRenderEmpty` here since it will cause circular dependency.\nexport const ConfigContext = /*#__PURE__*/React.createContext({\n  // We provide a default function for Context without provider\n  getPrefixCls: defaultGetPrefixCls,\n  iconPrefixCls: defaultIconPrefixCls\n});\nexport const {\n  Consumer: ConfigConsumer\n} = ConfigContext;\nconst EMPTY_OBJECT = {};\n/**\n * Get ConfigProvider configured component props.\n * This help to reduce bundle size for saving `?.` operator.\n * Do not use as `useMemo` deps since we do not cache the object here.\n *\n * NOTE: not refactor this with `useMemo` since memo will cost another memory space,\n * which will waste both compare calculation & memory.\n */\nexport function useComponentConfig(propName) {\n  const context = React.useContext(ConfigContext);\n  const {\n    getPrefixCls,\n    direction,\n    getPopupContainer\n  } = context;\n  const propValue = context[propName];\n  return Object.assign(Object.assign({\n    classNames: EMPTY_OBJECT,\n    styles: EMPTY_OBJECT\n  }, propValue), {\n    getPrefixCls,\n    direction,\n    getPopupContainer\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,gBAAgB,GAAG,KAAK;AACrC,OAAO,MAAMC,oBAAoB,GAAG,SAAS;AAC7C,OAAO,MAAMC,QAAQ,GAAG,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC;AAC1E,MAAMC,mBAAmB,GAAGA,CAACC,SAAS,EAAEC,kBAAkB,KAAK;EAC7D,IAAIA,kBAAkB,EAAE;IACtB,OAAOA,kBAAkB;EAC3B;EACA,OAAOD,SAAS,GAAG,GAAGJ,gBAAgB,IAAII,SAAS,EAAE,GAAGJ,gBAAgB;AAC1E,CAAC;AACD;AACA,OAAO,MAAMM,aAAa,GAAG,aAAaP,KAAK,CAACQ,aAAa,CAAC;EAC5D;EACAC,YAAY,EAAEL,mBAAmB;EACjCM,aAAa,EAAER;AACjB,CAAC,CAAC;AACF,OAAO,MAAM;EACXS,QAAQ,EAAEC;AACZ,CAAC,GAAGL,aAAa;AACjB,MAAMM,YAAY,GAAG,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,QAAQ,EAAE;EAC3C,MAAMC,OAAO,GAAGhB,KAAK,CAACiB,UAAU,CAACV,aAAa,CAAC;EAC/C,MAAM;IACJE,YAAY;IACZS,SAAS;IACTC;EACF,CAAC,GAAGH,OAAO;EACX,MAAMI,SAAS,GAAGJ,OAAO,CAACD,QAAQ,CAAC;EACnC,OAAOM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;IACjCC,UAAU,EAAEV,YAAY;IACxBW,MAAM,EAAEX;EACV,CAAC,EAAEO,SAAS,CAAC,EAAE;IACbX,YAAY;IACZS,SAAS;IACTC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}