{"ast": null, "code": "const t = t => \"object\" == typeof t && null != t && 1 === t.nodeType,\n  e = (t, e) => (!e || \"hidden\" !== t) && \"visible\" !== t && \"clip\" !== t,\n  n = (t, n) => {\n    if (t.clientHeight < t.scrollHeight || t.clientWidth < t.scrollWidth) {\n      const o = getComputedStyle(t, null);\n      return e(o.overflowY, n) || e(o.overflowX, n) || (t => {\n        const e = (t => {\n          if (!t.ownerDocument || !t.ownerDocument.defaultView) return null;\n          try {\n            return t.ownerDocument.defaultView.frameElement;\n          } catch (t) {\n            return null;\n          }\n        })(t);\n        return !!e && (e.clientHeight < t.scrollHeight || e.clientWidth < t.scrollWidth);\n      })(t);\n    }\n    return !1;\n  },\n  o = (t, e, n, o, l, r, i, s) => r < t && i > e || r > t && i < e ? 0 : r <= t && s <= n || i >= e && s >= n ? r - t - o : i > e && s < n || r < t && s > n ? i - e + l : 0,\n  l = t => {\n    const e = t.parentElement;\n    return null == e ? t.getRootNode().host || null : e;\n  },\n  r = (e, r) => {\n    var i, s, d, h;\n    if (\"undefined\" == typeof document) return [];\n    const {\n        scrollMode: c,\n        block: f,\n        inline: u,\n        boundary: a,\n        skipOverflowHiddenElements: g\n      } = r,\n      p = \"function\" == typeof a ? a : t => t !== a;\n    if (!t(e)) throw new TypeError(\"Invalid target\");\n    const m = document.scrollingElement || document.documentElement,\n      w = [];\n    let W = e;\n    for (; t(W) && p(W);) {\n      if (W = l(W), W === m) {\n        w.push(W);\n        break;\n      }\n      null != W && W === document.body && n(W) && !n(document.documentElement) || null != W && n(W, g) && w.push(W);\n    }\n    const b = null != (s = null == (i = window.visualViewport) ? void 0 : i.width) ? s : innerWidth,\n      H = null != (h = null == (d = window.visualViewport) ? void 0 : d.height) ? h : innerHeight,\n      {\n        scrollX: y,\n        scrollY: M\n      } = window,\n      {\n        height: v,\n        width: E,\n        top: x,\n        right: C,\n        bottom: I,\n        left: R\n      } = e.getBoundingClientRect(),\n      {\n        top: T,\n        right: B,\n        bottom: F,\n        left: V\n      } = (t => {\n        const e = window.getComputedStyle(t);\n        return {\n          top: parseFloat(e.scrollMarginTop) || 0,\n          right: parseFloat(e.scrollMarginRight) || 0,\n          bottom: parseFloat(e.scrollMarginBottom) || 0,\n          left: parseFloat(e.scrollMarginLeft) || 0\n        };\n      })(e);\n    let k = \"start\" === f || \"nearest\" === f ? x - T : \"end\" === f ? I + F : x + v / 2 - T + F,\n      D = \"center\" === u ? R + E / 2 - V + B : \"end\" === u ? C + B : R - V;\n    const L = [];\n    for (let t = 0; t < w.length; t++) {\n      const e = w[t],\n        {\n          height: l,\n          width: r,\n          top: i,\n          right: s,\n          bottom: d,\n          left: h\n        } = e.getBoundingClientRect();\n      if (\"if-needed\" === c && x >= 0 && R >= 0 && I <= H && C <= b && (e === m && !n(e) || x >= i && I <= d && R >= h && C <= s)) return L;\n      const a = getComputedStyle(e),\n        g = parseInt(a.borderLeftWidth, 10),\n        p = parseInt(a.borderTopWidth, 10),\n        W = parseInt(a.borderRightWidth, 10),\n        T = parseInt(a.borderBottomWidth, 10);\n      let B = 0,\n        F = 0;\n      const V = \"offsetWidth\" in e ? e.offsetWidth - e.clientWidth - g - W : 0,\n        S = \"offsetHeight\" in e ? e.offsetHeight - e.clientHeight - p - T : 0,\n        X = \"offsetWidth\" in e ? 0 === e.offsetWidth ? 0 : r / e.offsetWidth : 0,\n        Y = \"offsetHeight\" in e ? 0 === e.offsetHeight ? 0 : l / e.offsetHeight : 0;\n      if (m === e) B = \"start\" === f ? k : \"end\" === f ? k - H : \"nearest\" === f ? o(M, M + H, H, p, T, M + k, M + k + v, v) : k - H / 2, F = \"start\" === u ? D : \"center\" === u ? D - b / 2 : \"end\" === u ? D - b : o(y, y + b, b, g, W, y + D, y + D + E, E), B = Math.max(0, B + M), F = Math.max(0, F + y);else {\n        B = \"start\" === f ? k - i - p : \"end\" === f ? k - d + T + S : \"nearest\" === f ? o(i, d, l, p, T + S, k, k + v, v) : k - (i + l / 2) + S / 2, F = \"start\" === u ? D - h - g : \"center\" === u ? D - (h + r / 2) + V / 2 : \"end\" === u ? D - s + W + V : o(h, s, r, g, W + V, D, D + E, E);\n        const {\n          scrollLeft: t,\n          scrollTop: n\n        } = e;\n        B = 0 === Y ? 0 : Math.max(0, Math.min(n + B / Y, e.scrollHeight - l / Y + S)), F = 0 === X ? 0 : Math.max(0, Math.min(t + F / X, e.scrollWidth - r / X + V)), k += n - B, D += t - F;\n      }\n      L.push({\n        el: e,\n        top: B,\n        left: F\n      });\n    }\n    return L;\n  };\nexport { r as compute };", "map": {"version": 3, "names": ["t", "nodeType", "e", "n", "clientHeight", "scrollHeight", "clientWidth", "scrollWidth", "o", "getComputedStyle", "overflowY", "overflowX", "ownerDocument", "defaultView", "frameElement", "l", "r", "i", "s", "parentElement", "getRootNode", "host", "d", "h", "document", "scrollMode", "c", "block", "f", "inline", "u", "boundary", "a", "skipOverflowHiddenElements", "g", "p", "TypeError", "m", "scrollingElement", "documentElement", "w", "W", "push", "body", "b", "window", "visualViewport", "width", "innerWidth", "H", "height", "innerHeight", "scrollX", "y", "scrollY", "M", "v", "E", "top", "x", "right", "C", "bottom", "I", "left", "R", "getBoundingClientRect", "T", "B", "F", "V", "parseFloat", "scrollMarginTop", "scrollMarginRight", "scrollMarginBottom", "scrollMarginLeft", "k", "D", "L", "length", "parseInt", "borderLeftWidth", "borderTopWidth", "borderRightWidth", "borderBottomWidth", "offsetWidth", "S", "offsetHeight", "X", "Y", "Math", "max", "scrollLeft", "scrollTop", "min", "el", "compute"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\chatdb\\frontend\\node_modules\\compute-scroll-into-view\\src\\index.ts"], "sourcesContent": ["// Compute what scrolling needs to be done on required scrolling boxes for target to be in view\n\n// The type names here are named after the spec to make it easier to find more information around what they mean:\n// To reduce churn and reduce things that need be maintained things from the official TS DOM library is used here\n// https://drafts.csswg.org/cssom-view/\n\n// For a definition on what is \"block flow direction\" exactly, check this: https://drafts.csswg.org/css-writing-modes-4/#block-flow-direction\n\n/**\n * This new option is tracked in this PR, which is the most likely candidate at the time: https://github.com/w3c/csswg-drafts/pull/1805\n * @public\n */\nexport type ScrollMode = 'always' | 'if-needed'\n\n/** @public */\nexport interface Options {\n  /**\n   * Control the logical scroll position on the y-axis. The spec states that the `block` direction is related to the [writing-mode](https://developer.mozilla.org/en-US/docs/Web/CSS/writing-mode), but this is not implemented yet in this library.\n   * This means that `block: 'start'` aligns to the top edge and `block: 'end'` to the bottom.\n   * @defaultValue 'center'\n   */\n  block?: ScrollLogicalPosition\n  /**\n   * Like `block` this is affected by the [writing-mode](https://developer.mozilla.org/en-US/docs/Web/CSS/writing-mode). In left-to-right pages `inline: 'start'` will align to the left edge. In right-to-left it should be flipped. This will be supported in a future release.\n   * @defaultValue 'nearest'\n   */\n  inline?: ScrollLogicalPosition\n  /**\n   * This is a proposed addition to the spec that you can track here: https://github.com/w3c/csswg-drafts/pull/5677\n   *\n   * This library will be updated to reflect any changes to the spec and will provide a migration path.\n   * To be backwards compatible with `Element.scrollIntoViewIfNeeded` if something is not 100% visible it will count as \"needs scrolling\". If you need a different visibility ratio your best option would be to implement an [Intersection Observer](https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API).\n   * @defaultValue 'always'\n   */\n  scrollMode?: ScrollMode\n  /**\n   * By default there is no boundary. All the parent elements of your target is checked until it reaches the viewport ([`document.scrollingElement`](https://developer.mozilla.org/en-US/docs/Web/API/document/scrollingElement)) when calculating layout and what to scroll.\n   * By passing a boundary you can short-circuit this loop depending on your needs:\n   * \n   * - Prevent the browser window from scrolling.\n   * - Scroll elements into view in a list, without scrolling container elements.\n   * \n   * You can also pass a function to do more dynamic checks to override the scroll scoping:\n   * \n   * ```js\n   * let actions = compute(target, {\n   *   boundary: (parent) => {\n   *     // By default `overflow: hidden` elements are allowed, only `overflow: visible | clip` is skipped as\n   *     // this is required by the CSSOM spec\n   *     if (getComputedStyle(parent)['overflow'] === 'hidden') {\n   *       return false\n   *     }\n\n   *     return true\n   *   },\n   * })\n   * ```\n   * @defaultValue null\n   */\n  boundary?: Element | ((parent: Element) => boolean) | null\n  /**\n   * New option that skips auto-scrolling all nodes with overflow: hidden set\n   * See FF implementation: https://hg.mozilla.org/integration/fx-team/rev/c48c3ec05012#l7.18\n   * @defaultValue false\n   * @public\n   */\n  skipOverflowHiddenElements?: boolean\n}\n\n/** @public */\nexport interface ScrollAction {\n  el: Element\n  top: number\n  left: number\n}\n\n// @TODO better shadowdom test, 11 = document fragment\nconst isElement = (el: any): el is Element =>\n  typeof el === 'object' && el != null && el.nodeType === 1\n\nconst canOverflow = (\n  overflow: string | null,\n  skipOverflowHiddenElements?: boolean\n) => {\n  if (skipOverflowHiddenElements && overflow === 'hidden') {\n    return false\n  }\n\n  return overflow !== 'visible' && overflow !== 'clip'\n}\n\nconst getFrameElement = (el: Element) => {\n  if (!el.ownerDocument || !el.ownerDocument.defaultView) {\n    return null\n  }\n\n  try {\n    return el.ownerDocument.defaultView.frameElement\n  } catch (e) {\n    return null\n  }\n}\n\nconst isHiddenByFrame = (el: Element): boolean => {\n  const frame = getFrameElement(el)\n  if (!frame) {\n    return false\n  }\n\n  return (\n    frame.clientHeight < el.scrollHeight || frame.clientWidth < el.scrollWidth\n  )\n}\n\nconst isScrollable = (el: Element, skipOverflowHiddenElements?: boolean) => {\n  if (el.clientHeight < el.scrollHeight || el.clientWidth < el.scrollWidth) {\n    const style = getComputedStyle(el, null)\n    return (\n      canOverflow(style.overflowY, skipOverflowHiddenElements) ||\n      canOverflow(style.overflowX, skipOverflowHiddenElements) ||\n      isHiddenByFrame(el)\n    )\n  }\n\n  return false\n}\n/**\n * Find out which edge to align against when logical scroll position is \"nearest\"\n * Interesting fact: \"nearest\" works similarily to \"if-needed\", if the element is fully visible it will not scroll it\n *\n * Legends:\n * ┌────────┐ ┏ ━ ━ ━ ┓\n * │ target │   frame\n * └────────┘ ┗ ━ ━ ━ ┛\n */\nconst alignNearest = (\n  scrollingEdgeStart: number,\n  scrollingEdgeEnd: number,\n  scrollingSize: number,\n  scrollingBorderStart: number,\n  scrollingBorderEnd: number,\n  elementEdgeStart: number,\n  elementEdgeEnd: number,\n  elementSize: number\n) => {\n  /**\n   * If element edge A and element edge B are both outside scrolling box edge A and scrolling box edge B\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓\n   *          │  │\n   *        ┃ │  │ ┃        do nothing\n   *          │  │\n   *        ┗━│━━│━┛\n   *          └──┘\n   *\n   *  If element edge C and element edge D are both outside scrolling box edge C and scrolling box edge D\n   *\n   *    ┏ ━ ━ ━ ━ ┓\n   *   ┌───────────┐\n   *   │┃         ┃│        do nothing\n   *   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart < scrollingEdgeStart &&\n      elementEdgeEnd > scrollingEdgeEnd) ||\n    (elementEdgeStart > scrollingEdgeStart && elementEdgeEnd < scrollingEdgeEnd)\n  ) {\n    return 0\n  }\n\n  /**\n   * If element edge A is outside scrolling box edge A and element height is less than scrolling box height\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓         ┏━┌━━┐━┓\n   *          └──┘             │  │\n   *  from  ┃      ┃     to  ┃ └──┘ ┃\n   *\n   *        ┗━ ━━ ━┛         ┗━ ━━ ━┛\n   *\n   * If element edge B is outside scrolling box edge B and element height is greater than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━┌━━┐━┓\n   *                           │  │\n   *  from  ┃ ┌──┐ ┃     to  ┃ │  │ ┃\n   *          │  │             │  │\n   *        ┗━│━━│━┛         ┗━│━━│━┛\n   *          │  │             └──┘\n   *          │  │\n   *          └──┘\n   *\n   * If element edge C is outside scrolling box edge C and element width is less than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───┐                 ┌───┐\n   *  │ ┃ │       ┃         ┃   │     ┃\n   *  └───┘                 └───┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is greater than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *        ┌───────────┐   ┌───────────┐\n   *    ┃   │     ┃     │   ┃         ┃ │\n   *        └───────────┘   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart <= scrollingEdgeStart && elementSize <= scrollingSize) ||\n    (elementEdgeEnd >= scrollingEdgeEnd && elementSize >= scrollingSize)\n  ) {\n    return elementEdgeStart - scrollingEdgeStart - scrollingBorderStart\n  }\n\n  /**\n   * If element edge B is outside scrolling box edge B and element height is less than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━ ━━ ━┓\n   *\n   *  from  ┃      ┃     to  ┃ ┌──┐ ┃\n   *          ┌──┐             │  │\n   *        ┗━│━━│━┛         ┗━└━━┘━┛\n   *          └──┘\n   *\n   * If element edge A is outside scrolling box edge A and element height is greater than scrolling box height\n   *\n   *          ┌──┐\n   *          │  │\n   *          │  │             ┌──┐\n   *        ┏━│━━│━┓         ┏━│━━│━┓\n   *          │  │             │  │\n   *  from  ┃ └──┘ ┃     to  ┃ │  │ ┃\n   *                           │  │\n   *        ┗━ ━━ ━┛         ┗━└━━┘━┛\n   *\n   * If element edge C is outside scrolling box edge C and element width is greater than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───────────┐           ┌───────────┐\n   *  │     ┃     │   ┃       │ ┃         ┃\n   *  └───────────┘           └───────────┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is less than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *                ┌───┐             ┌───┐\n   *        ┃       │ ┃ │       ┃     │   ┃\n   *                └───┘             └───┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   */\n  if (\n    (elementEdgeEnd > scrollingEdgeEnd && elementSize < scrollingSize) ||\n    (elementEdgeStart < scrollingEdgeStart && elementSize > scrollingSize)\n  ) {\n    return elementEdgeEnd - scrollingEdgeEnd + scrollingBorderEnd\n  }\n\n  return 0\n}\n\nconst getParentElement = (element: Node): Element | null => {\n  const parent = element.parentElement\n  if (parent == null) {\n    return (element.getRootNode() as ShadowRoot).host || null\n  }\n  return parent\n}\n\nconst getScrollMargins = (target: Element) => {\n  const computedStyle = window.getComputedStyle(target)\n  return {\n    top: parseFloat(computedStyle.scrollMarginTop) || 0,\n    right: parseFloat(computedStyle.scrollMarginRight) || 0,\n    bottom: parseFloat(computedStyle.scrollMarginBottom) || 0,\n    left: parseFloat(computedStyle.scrollMarginLeft) || 0,\n  }\n}\n\n/** @public */\nexport const compute = (target: Element, options: Options): ScrollAction[] => {\n  if (typeof document === 'undefined') {\n    // If there's no DOM we assume it's not in a browser environment\n    return []\n  }\n\n  const { scrollMode, block, inline, boundary, skipOverflowHiddenElements } =\n    options\n  // Allow using a callback to check the boundary\n  // The default behavior is to check if the current target matches the boundary element or not\n  // If undefined it'll check that target is never undefined (can happen as we recurse up the tree)\n  const checkBoundary =\n    typeof boundary === 'function' ? boundary : (node: any) => node !== boundary\n\n  if (!isElement(target)) {\n    throw new TypeError('Invalid target')\n  }\n\n  // Used to handle the top most element that can be scrolled\n  const scrollingElement = document.scrollingElement || document.documentElement\n\n  // Collect all the scrolling boxes, as defined in the spec: https://drafts.csswg.org/cssom-view/#scrolling-box\n  const frames: Element[] = []\n  let cursor: Element | null = target\n  while (isElement(cursor) && checkBoundary(cursor)) {\n    // Move cursor to parent\n    cursor = getParentElement(cursor)\n\n    // Stop when we reach the viewport\n    if (cursor === scrollingElement) {\n      frames.push(cursor)\n      break\n    }\n\n    // Skip document.body if it's not the scrollingElement and documentElement isn't independently scrollable\n    if (\n      cursor != null &&\n      cursor === document.body &&\n      isScrollable(cursor) &&\n      !isScrollable(document.documentElement)\n    ) {\n      continue\n    }\n\n    // Now we check if the element is scrollable, this code only runs if the loop haven't already hit the viewport or a custom boundary\n    if (cursor != null && isScrollable(cursor, skipOverflowHiddenElements)) {\n      frames.push(cursor)\n    }\n  }\n\n  // Support pinch-zooming properly, making sure elements scroll into the visual viewport\n  // Browsers that don't support visualViewport will report the layout viewport dimensions on document.documentElement.clientWidth/Height\n  // and viewport dimensions on window.innerWidth/Height\n  // https://www.quirksmode.org/mobile/viewports2.html\n  // https://bokand.github.io/viewport/index.html\n  const viewportWidth = window.visualViewport?.width ?? innerWidth\n  const viewportHeight = window.visualViewport?.height ?? innerHeight\n  const { scrollX, scrollY } = window\n\n  const {\n    height: targetHeight,\n    width: targetWidth,\n    top: targetTop,\n    right: targetRight,\n    bottom: targetBottom,\n    left: targetLeft,\n  } = target.getBoundingClientRect()\n  const {\n    top: marginTop,\n    right: marginRight,\n    bottom: marginBottom,\n    left: marginLeft,\n  } = getScrollMargins(target)\n\n  // These values mutate as we loop through and generate scroll coordinates\n  let targetBlock: number =\n    block === 'start' || block === 'nearest'\n      ? targetTop - marginTop\n      : block === 'end'\n      ? targetBottom + marginBottom\n      : targetTop + targetHeight / 2 - marginTop + marginBottom // block === 'center\n  let targetInline: number =\n    inline === 'center'\n      ? targetLeft + targetWidth / 2 - marginLeft + marginRight\n      : inline === 'end'\n      ? targetRight + marginRight\n      : targetLeft - marginLeft // inline === 'start || inline === 'nearest\n\n  // Collect new scroll positions\n  const computations: ScrollAction[] = []\n  // In chrome there's no longer a difference between caching the `frames.length` to a var or not, so we don't in this case (size > speed anyways)\n  for (let index = 0; index < frames.length; index++) {\n    const frame = frames[index]\n\n    // @TODO add a shouldScroll hook here that allows userland code to take control\n\n    const { height, width, top, right, bottom, left } =\n      frame.getBoundingClientRect()\n\n    // If the element is already visible we can end it here\n    // @TODO targetBlock and targetInline should be taken into account to be compliant with https://github.com/w3c/csswg-drafts/pull/1805/files#diff-3c17f0e43c20f8ecf89419d49e7ef5e0R1333\n    if (\n      scrollMode === 'if-needed' &&\n      targetTop >= 0 &&\n      targetLeft >= 0 &&\n      targetBottom <= viewportHeight &&\n      targetRight <= viewportWidth &&\n      // scrollingElement is added to the frames array even if it's not scrollable, in which case checking its bounds is not required\n      ((frame === scrollingElement && !isScrollable(frame)) ||\n        (targetTop >= top &&\n          targetBottom <= bottom &&\n          targetLeft >= left &&\n          targetRight <= right))\n    ) {\n      // Break the loop and return the computations for things that are not fully visible\n      return computations\n    }\n\n    const frameStyle = getComputedStyle(frame)\n    const borderLeft = parseInt(frameStyle.borderLeftWidth as string, 10)\n    const borderTop = parseInt(frameStyle.borderTopWidth as string, 10)\n    const borderRight = parseInt(frameStyle.borderRightWidth as string, 10)\n    const borderBottom = parseInt(frameStyle.borderBottomWidth as string, 10)\n\n    let blockScroll: number = 0\n    let inlineScroll: number = 0\n\n    // The property existance checks for offfset[Width|Height] is because only HTMLElement objects have them, but any Element might pass by here\n    // @TODO find out if the \"as HTMLElement\" overrides can be dropped\n    const scrollbarWidth =\n      'offsetWidth' in frame\n        ? (frame as HTMLElement).offsetWidth -\n          (frame as HTMLElement).clientWidth -\n          borderLeft -\n          borderRight\n        : 0\n    const scrollbarHeight =\n      'offsetHeight' in frame\n        ? (frame as HTMLElement).offsetHeight -\n          (frame as HTMLElement).clientHeight -\n          borderTop -\n          borderBottom\n        : 0\n\n    const scaleX =\n      'offsetWidth' in frame\n        ? (frame as HTMLElement).offsetWidth === 0\n          ? 0\n          : width / (frame as HTMLElement).offsetWidth\n        : 0\n    const scaleY =\n      'offsetHeight' in frame\n        ? (frame as HTMLElement).offsetHeight === 0\n          ? 0\n          : height / (frame as HTMLElement).offsetHeight\n        : 0\n\n    if (scrollingElement === frame) {\n      // Handle viewport logic (document.documentElement or document.body)\n\n      if (block === 'start') {\n        blockScroll = targetBlock\n      } else if (block === 'end') {\n        blockScroll = targetBlock - viewportHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          scrollY,\n          scrollY + viewportHeight,\n          viewportHeight,\n          borderTop,\n          borderBottom,\n          scrollY + targetBlock,\n          scrollY + targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - viewportHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - viewportWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - viewportWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          scrollX,\n          scrollX + viewportWidth,\n          viewportWidth,\n          borderLeft,\n          borderRight,\n          scrollX + targetInline,\n          scrollX + targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      // Apply scroll position offsets and ensure they are within bounds\n      // @TODO add more test cases to cover this 100%\n      blockScroll = Math.max(0, blockScroll + scrollY)\n      inlineScroll = Math.max(0, inlineScroll + scrollX)\n    } else {\n      // Handle each scrolling frame that might exist between the target and the viewport\n      if (block === 'start') {\n        blockScroll = targetBlock - top - borderTop\n      } else if (block === 'end') {\n        blockScroll = targetBlock - bottom + borderBottom + scrollbarHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          top,\n          bottom,\n          height,\n          borderTop,\n          borderBottom + scrollbarHeight,\n          targetBlock,\n          targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - (top + height / 2) + scrollbarHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline - left - borderLeft\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - (left + width / 2) + scrollbarWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - right + borderRight + scrollbarWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          left,\n          right,\n          width,\n          borderLeft,\n          borderRight + scrollbarWidth,\n          targetInline,\n          targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      const { scrollLeft, scrollTop } = frame\n      // Ensure scroll coordinates are not out of bounds while applying scroll offsets\n      blockScroll =\n        scaleY === 0\n          ? 0\n          : Math.max(\n              0,\n              Math.min(\n                scrollTop + blockScroll / scaleY,\n                frame.scrollHeight - height / scaleY + scrollbarHeight\n              )\n            )\n      inlineScroll =\n        scaleX === 0\n          ? 0\n          : Math.max(\n              0,\n              Math.min(\n                scrollLeft + inlineScroll / scaleX,\n                frame.scrollWidth - width / scaleX + scrollbarWidth\n              )\n            )\n\n      // Cache the offset so that parent frames can scroll this into view correctly\n      targetBlock += scrollTop - blockScroll\n      targetInline += scrollLeft - inlineScroll\n    }\n\n    computations.push({ el: frame, top: blockScroll, left: inlineScroll })\n  }\n\n  return computations\n}\n"], "mappings": "AA6EA,MAAMA,CAAA,GAAaA,CAAA,IACH,mBAAPA,CAAA,IAAyB,QAANA,CAAA,IAA8B,MAAhBA,CAAA,CAAGC,QAAA;EAEvCC,CAAA,GAAcA,CAClBF,CAAA,EACAE,CAAA,OAEIA,CAAA,IAA2C,aAAbF,CAAA,KAId,cAAbA,CAAA,IAAuC,WAAbA,CAAA;EA0B7BG,CAAA,GAAeA,CAACH,CAAA,EAAaG,CAAA;IACjC,IAAIH,CAAA,CAAGI,YAAA,GAAeJ,CAAA,CAAGK,YAAA,IAAgBL,CAAA,CAAGM,WAAA,GAAcN,CAAA,CAAGO,WAAA,EAAa;MAClE,MAAAC,CAAA,GAAQC,gBAAA,CAAiBT,CAAA,EAAI;MAEjC,OAAAE,CAAA,CAAYM,CAAA,CAAME,SAAA,EAAWP,CAAA,KAC7BD,CAAA,CAAYM,CAAA,CAAMG,SAAA,EAAWR,CAAA,KAhBV,CAAAH,CAAA;QACjB,MAAAE,CAAA,GAbiB,CAAAF,CAAA;UACvB,KAAKA,CAAA,CAAGY,aAAA,KAAkBZ,CAAA,CAAGY,aAAA,CAAcC,WAAA,EAClC;UAGL;YACK,OAAAb,CAAA,CAAGY,aAAA,CAAcC,WAAA,CAAYC,YAAA;UAAA,SAC7Bd,CAAA;YACA,WACT;UAAA;QAAA,GAI8BA,CAAA;QAC9B,SAAKE,CAAA,KAKHA,CAAA,CAAME,YAAA,GAAeJ,CAAA,CAAGK,YAAA,IAAgBH,CAAA,CAAMI,WAAA,GAAcN,CAAA,CAAGO,WAAA;MAAA,GAU7CP,CAAA,CAEpB;IAAA;IAEO;EAAA;EAWHQ,CAAA,GAAeA,CACnBR,CAAA,EACAE,CAAA,EACAC,CAAA,EACAK,CAAA,EACAO,CAAA,EACAC,CAAA,EACAC,CAAA,EACAC,CAAA,KAsBGF,CAAA,GAAmBhB,CAAA,IAClBiB,CAAA,GAAiBf,CAAA,IAClBc,CAAA,GAAmBhB,CAAA,IAAsBiB,CAAA,GAAiBf,CAAA,GAEpD,IA2CNc,CAAA,IAAoBhB,CAAA,IAAsBkB,CAAA,IAAef,CAAA,IACzDc,CAAA,IAAkBf,CAAA,IAAoBgB,CAAA,IAAef,CAAA,GAE/Ca,CAAA,GAAmBhB,CAAA,GAAqBQ,CAAA,GA4C9CS,CAAA,GAAiBf,CAAA,IAAoBgB,CAAA,GAAcf,CAAA,IACnDa,CAAA,GAAmBhB,CAAA,IAAsBkB,CAAA,GAAcf,CAAA,GAEjDc,CAAA,GAAiBf,CAAA,GAAmBa,CAAA,GAGtC;EAGHA,CAAA,GAAoBf,CAAA;IACxB,MAAME,CAAA,GAASF,CAAA,CAAQmB,aAAA;IACvB,OAAc,QAAVjB,CAAA,GACMF,CAAA,CAAQoB,WAAA,GAA6BC,IAAA,IAAQ,OAEhDnB,CAAA;EAAA;EAcIc,CAAA,GAAUA,CAACd,CAAA,EAAiBc,CAAA;IA/RzC,IAAAC,CAAA,EAAAC,CAAA,EAAAI,CAAA,EAAAC,CAAA;IAgSM,IAAoB,sBAAbC,QAAA,EAET,OAAO;IAGT;QAAMC,UAAA,EAAEC,CAAA;QAAYC,KAAA,EAAAC,CAAA;QAAAC,MAAA,EAAOC,CAAA;QAAQC,QAAA,EAAAC,CAAA;QAAAC,0BAAA,EAAUC;MAAA,IAC3ClB,CAAA;MAIImB,CAAA,GACgB,qBAAbH,CAAA,GAA0BA,CAAA,GAAYhC,CAAA,IAAcA,CAAA,KAASgC,CAAA;IAElE,KAAChC,CAAA,CAAUE,CAAA,GACP,UAAIkC,SAAA,CAAU;IAIhB,MAAAC,CAAA,GAAmBb,QAAA,CAASc,gBAAA,IAAoBd,QAAA,CAASe,eAAA;MAGzDC,CAAA,GAAoB;IAC1B,IAAIC,CAAA,GAAyBvC,CAAA;IAC7B,OAAOF,CAAA,CAAUyC,CAAA,KAAWN,CAAA,CAAcM,CAAA,IAAS;MAKjD,IAHAA,CAAA,GAAS1B,CAAA,CAAiB0B,CAAA,GAGtBA,CAAA,KAAWJ,CAAA,EAAkB;QAC/BG,CAAA,CAAOE,IAAA,CAAKD,CAAA;QACZ;MACF;MAIY,QAAVA,CAAA,IACAA,CAAA,KAAWjB,QAAA,CAASmB,IAAA,IACpBxC,CAAA,CAAasC,CAAA,MACZtC,CAAA,CAAaqB,QAAA,CAASe,eAAA,KAMX,QAAVE,CAAA,IAAkBtC,CAAA,CAAasC,CAAA,EAAQP,CAAA,KACzCM,CAAA,CAAOE,IAAA,CAAKD,CAAA,CAEhB;IAAA;IAOA,MAAMG,CAAA,GAAgB,SAAA1B,CAAA,YAAAD,CAAA,GAAA4B,MAAA,CAAOC,cAAA,SAAP,IAAA7B,CAAA,CAAuB8B,KAAA,IAAS7B,CAAA,GAAA8B,UAAA;MAChDC,CAAA,GAAiB,SAAA1B,CAAA,YAAAD,CAAA,GAAAuB,MAAA,CAAOC,cAAA,SAAP,IAAAxB,CAAA,CAAuB4B,MAAA,IAAU3B,CAAA,GAAA4B,WAAA;MAAA;QAClDC,OAAA,EAAEC,CAAA;QAASC,OAAA,EAAAC;MAAA,IAAYV,MAAA;MAAA;QAG3BK,MAAA,EAAQM,CAAA;QACRT,KAAA,EAAOU,CAAA;QACPC,GAAA,EAAKC,CAAA;QACLC,KAAA,EAAOC,CAAA;QACPC,MAAA,EAAQC,CAAA;QACRC,IAAA,EAAMC;MAAA,IACJ/D,CAAA,CAAOgE,qBAAA;MAAA;QAETR,GAAA,EAAKS,CAAA;QACLP,KAAA,EAAOQ,CAAA;QACPN,MAAA,EAAQO,CAAA;QACRL,IAAA,EAAMM;MAAA,IAlFgB,CAAAtE,CAAA;QAClB,MAAAE,CAAA,GAAgB2C,MAAA,CAAOpC,gBAAA,CAAiBT,CAAA;QACvC;UACL0D,GAAA,EAAKa,UAAA,CAAWrE,CAAA,CAAcsE,eAAA,KAAoB;UAClDZ,KAAA,EAAOW,UAAA,CAAWrE,CAAA,CAAcuE,iBAAA,KAAsB;UACtDX,MAAA,EAAQS,UAAA,CAAWrE,CAAA,CAAcwE,kBAAA,KAAuB;UACxDV,IAAA,EAAMO,UAAA,CAAWrE,CAAA,CAAcyE,gBAAA,KAAqB;QAAA,CACtD;MAAA,GA4EqBzE,CAAA;IAGrB,IAAI0E,CAAA,GACQ,YAAVhD,CAAA,IAA+B,cAAVA,CAAA,GACjB+B,CAAA,GAAYQ,CAAA,GACF,UAAVvC,CAAA,GACAmC,CAAA,GAAeM,CAAA,GACfV,CAAA,GAAYH,CAAA,GAAe,IAAIW,CAAA,GAAYE,CAAA;MAC7CQ,CAAA,GACS,aAAX/C,CAAA,GACImC,CAAA,GAAaR,CAAA,GAAc,IAAIa,CAAA,GAAaF,CAAA,GACjC,UAAXtC,CAAA,GACA+B,CAAA,GAAcO,CAAA,GACdH,CAAA,GAAaK,CAAA;IAGnB,MAAMQ,CAAA,GAA+B;IAErC,SAAS9E,CAAA,GAAQ,GAAGA,CAAA,GAAQwC,CAAA,CAAOuC,MAAA,EAAQ/E,CAAA,IAAS;MAC5C,MAAAE,CAAA,GAAQsC,CAAA,CAAOxC,CAAA;QAAA;UAIfkD,MAAA,EAAEnC,CAAA;UAAAgC,KAAA,EAAQ/B,CAAA;UAAO0C,GAAA,EAAAzC,CAAA;UAAA2C,KAAA,EAAK1C,CAAA;UAAA4C,MAAA,EAAOxC,CAAA;UAAQ0C,IAAA,EAAAzC;QAAA,IACzCrB,CAAA,CAAMgE,qBAAA;MAKN,IAAe,gBAAfxC,CAAA,IACAiC,CAAA,IAAa,KACbM,CAAA,IAAc,KACdF,CAAA,IAAgBd,CAAA,IAChBY,CAAA,IAAejB,CAAA,KAEb1C,CAAA,KAAUmC,CAAA,KAAqBlC,CAAA,CAAaD,CAAA,KAC3CyD,CAAA,IAAa1C,CAAA,IACZ8C,CAAA,IAAgBzC,CAAA,IAChB2C,CAAA,IAAc1C,CAAA,IACdsC,CAAA,IAAe3C,CAAA,GAGZ,OAAA4D,CAAA;MAGH,MAAA9C,CAAA,GAAavB,gBAAA,CAAiBP,CAAA;QAC9BgC,CAAA,GAAa8C,QAAA,CAAShD,CAAA,CAAWiD,eAAA,EAA2B;QAC5D9C,CAAA,GAAY6C,QAAA,CAAShD,CAAA,CAAWkD,cAAA,EAA0B;QAC1DzC,CAAA,GAAcuC,QAAA,CAAShD,CAAA,CAAWmD,gBAAA,EAA4B;QAC9DhB,CAAA,GAAea,QAAA,CAAShD,CAAA,CAAWoD,iBAAA,EAA6B;MAEtE,IAAIhB,CAAA,GAAsB;QACtBC,CAAA,GAAuB;MAIrB,MAAAC,CAAA,GACJ,iBAAiBpE,CAAA,GACZA,CAAA,CAAsBmF,WAAA,GACtBnF,CAAA,CAAsBI,WAAA,GACvB4B,CAAA,GACAO,CAAA,GACA;QACA6C,CAAA,GACJ,kBAAkBpF,CAAA,GACbA,CAAA,CAAsBqF,YAAA,GACtBrF,CAAA,CAAsBE,YAAA,GACvB+B,CAAA,GACAgC,CAAA,GACA;QAEAqB,CAAA,GACJ,iBAAiBtF,CAAA,GAC0B,MAAtCA,CAAA,CAAsBmF,WAAA,GACrB,IACArE,CAAA,GAASd,CAAA,CAAsBmF,WAAA,GACjC;QACAI,CAAA,GACJ,kBAAkBvF,CAAA,GAC0B,MAAvCA,CAAA,CAAsBqF,YAAA,GACrB,IACAxE,CAAA,GAAUb,CAAA,CAAsBqF,YAAA,GAClC;MAEN,IAAIlD,CAAA,KAAqBnC,CAAA,EAIPkE,CAAA,GADF,YAAVxC,CAAA,GACYgD,CAAA,GACK,UAAVhD,CAAA,GACKgD,CAAA,GAAc3B,CAAA,GACT,cAAVrB,CAAA,GACKpB,CAAA,CACZ+C,CAAA,EACAA,CAAA,GAAUN,CAAA,EACVA,CAAA,EACAd,CAAA,EACAgC,CAAA,EACAZ,CAAA,GAAUqB,CAAA,EACVrB,CAAA,GAAUqB,CAAA,GAAcpB,CAAA,EACxBA,CAAA,IAIYoB,CAAA,GAAc3B,CAAA,GAAiB,GAI9BoB,CAAA,GADF,YAAXvC,CAAA,GACa+C,CAAA,GACK,aAAX/C,CAAA,GACM+C,CAAA,GAAejC,CAAA,GAAgB,IAC1B,UAAXd,CAAA,GACM+C,CAAA,GAAejC,CAAA,GAGfpC,CAAA,CACb6C,CAAA,EACAA,CAAA,GAAUT,CAAA,EACVA,CAAA,EACAV,CAAA,EACAO,CAAA,EACAY,CAAA,GAAUwB,CAAA,EACVxB,CAAA,GAAUwB,CAAA,GAAepB,CAAA,EACzBA,CAAA,GAMJW,CAAA,GAAcsB,IAAA,CAAKC,GAAA,CAAI,GAAGvB,CAAA,GAAcb,CAAA,GACxCc,CAAA,GAAeqB,IAAA,CAAKC,GAAA,CAAI,GAAGtB,CAAA,GAAehB,CAAA,OACrC;QAGHe,CAAA,GADY,YAAVxC,CAAA,GACYgD,CAAA,GAAc3D,CAAA,GAAMkB,CAAA,GACf,UAAVP,CAAA,GACKgD,CAAA,GAActD,CAAA,GAAS6C,CAAA,GAAemB,CAAA,GACjC,cAAV1D,CAAA,GACKpB,CAAA,CACZS,CAAA,EACAK,CAAA,EACAP,CAAA,EACAoB,CAAA,EACAgC,CAAA,GAAemB,CAAA,EACfV,CAAA,EACAA,CAAA,GAAcpB,CAAA,EACdA,CAAA,IAIYoB,CAAA,IAAe3D,CAAA,GAAMF,CAAA,GAAS,KAAKuE,CAAA,GAAkB,GAInEjB,CAAA,GADa,YAAXvC,CAAA,GACa+C,CAAA,GAAetD,CAAA,GAAOW,CAAA,GACjB,aAAXJ,CAAA,GACM+C,CAAA,IAAgBtD,CAAA,GAAOP,CAAA,GAAQ,KAAKsD,CAAA,GAAiB,IAChD,UAAXxC,CAAA,GACM+C,CAAA,GAAe3D,CAAA,GAAQuB,CAAA,GAAc6B,CAAA,GAGrC9D,CAAA,CACbe,CAAA,EACAL,CAAA,EACAF,CAAA,EACAkB,CAAA,EACAO,CAAA,GAAc6B,CAAA,EACdO,CAAA,EACAA,CAAA,GAAepB,CAAA,EACfA,CAAA;QAIE;UAAAmC,UAAA,EAAE5F,CAAA;UAAY6F,SAAA,EAAA1F;QAAA,IAAcD,CAAA;QAGhCkE,CAAA,GAAW,MAAXqB,CAAA,GACI,IACAC,IAAA,CAAKC,GAAA,CACH,GACAD,IAAA,CAAKI,GAAA,CACH3F,CAAA,GAAYiE,CAAA,GAAcqB,CAAA,EAC1BvF,CAAA,CAAMG,YAAA,GAAeU,CAAA,GAAS0E,CAAA,GAASH,CAAA,IAI/CjB,CAAA,GAAW,MAAXmB,CAAA,GACI,IACAE,IAAA,CAAKC,GAAA,CACH,GACAD,IAAA,CAAKI,GAAA,CACH9F,CAAA,GAAaqE,CAAA,GAAemB,CAAA,EAC5BtF,CAAA,CAAMK,WAAA,GAAcS,CAAA,GAAQwE,CAAA,GAASlB,CAAA,IAK/CM,CAAA,IAAezE,CAAA,GAAYiE,CAAA,EAC3BS,CAAA,IAAgB7E,CAAA,GAAaqE,CAC/B;MAAA;MAEaS,CAAA,CAAApC,IAAA,CAAK;QAAEqD,EAAA,EAAI7F,CAAA;QAAOwD,GAAA,EAAKU,CAAA;QAAaJ,IAAA,EAAMK;MAAA,EACzD;IAAA;IAEO,OAAAS,CAAA;EAAA;AAAA,SACT9D,CAAA,IAAAgF,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}