{"ast": null, "code": "'use strict';\n\nmodule.exports = makefile;\nmakefile.displayName = 'makefile';\nmakefile.aliases = [];\nfunction makefile(Prism) {\n  Prism.languages.makefile = {\n    comment: {\n      pattern: /(^|[^\\\\])#(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n])*/,\n      lookbehind: true\n    },\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    'builtin-target': {\n      pattern: /\\.[A-Z][^:#=\\s]+(?=\\s*:(?!=))/,\n      alias: 'builtin'\n    },\n    target: {\n      pattern: /^(?:[^:=\\s]|[ \\t]+(?![\\s:]))+(?=\\s*:(?!=))/m,\n      alias: 'symbol',\n      inside: {\n        variable: /\\$+(?:(?!\\$)[^(){}:#=\\s]+|(?=[({]))/\n      }\n    },\n    variable: /\\$+(?:(?!\\$)[^(){}:#=\\s]+|\\([@*%<^+?][DF]\\)|(?=[({]))/,\n    // Directives\n    keyword: /-include\\b|\\b(?:define|else|endef|endif|export|ifn?def|ifn?eq|include|override|private|sinclude|undefine|unexport|vpath)\\b/,\n    function: {\n      pattern: /(\\()(?:abspath|addsuffix|and|basename|call|dir|error|eval|file|filter(?:-out)?|findstring|firstword|flavor|foreach|guile|if|info|join|lastword|load|notdir|or|origin|patsubst|realpath|shell|sort|strip|subst|suffix|value|warning|wildcard|word(?:list|s)?)(?=[ \\t])/,\n      lookbehind: true\n    },\n    operator: /(?:::|[?:+!])?=|[|@]/,\n    punctuation: /[:;(){}]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "makefile", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "string", "greedy", "alias", "target", "inside", "variable", "keyword", "function", "operator", "punctuation"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/makefile.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = makefile\nmakefile.displayName = 'makefile'\nmakefile.aliases = []\nfunction makefile(Prism) {\n  Prism.languages.makefile = {\n    comment: {\n      pattern: /(^|[^\\\\])#(?:\\\\(?:\\r\\n|[\\s\\S])|[^\\\\\\r\\n])*/,\n      lookbehind: true\n    },\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    'builtin-target': {\n      pattern: /\\.[A-Z][^:#=\\s]+(?=\\s*:(?!=))/,\n      alias: 'builtin'\n    },\n    target: {\n      pattern: /^(?:[^:=\\s]|[ \\t]+(?![\\s:]))+(?=\\s*:(?!=))/m,\n      alias: 'symbol',\n      inside: {\n        variable: /\\$+(?:(?!\\$)[^(){}:#=\\s]+|(?=[({]))/\n      }\n    },\n    variable: /\\$+(?:(?!\\$)[^(){}:#=\\s]+|\\([@*%<^+?][DF]\\)|(?=[({]))/,\n    // Directives\n    keyword:\n      /-include\\b|\\b(?:define|else|endef|endif|export|ifn?def|ifn?eq|include|override|private|sinclude|undefine|unexport|vpath)\\b/,\n    function: {\n      pattern:\n        /(\\()(?:abspath|addsuffix|and|basename|call|dir|error|eval|file|filter(?:-out)?|findstring|firstword|flavor|foreach|guile|if|info|join|lastword|load|notdir|or|origin|patsubst|realpath|shell|sort|strip|subst|suffix|value|warning|wildcard|word(?:list|s)?)(?=[ \\t])/,\n      lookbehind: true\n    },\n    operator: /(?:::|[?:+!])?=|[|@]/,\n    punctuation: /[:;(){}]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,QAAQ;AACzBA,QAAQ,CAACC,WAAW,GAAG,UAAU;AACjCD,QAAQ,CAACE,OAAO,GAAG,EAAE;AACrB,SAASF,QAAQA,CAACG,KAAK,EAAE;EACvBA,KAAK,CAACC,SAAS,CAACJ,QAAQ,GAAG;IACzBK,OAAO,EAAE;MACPC,OAAO,EAAE,4CAA4C;MACrDC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,OAAO,EAAE,gDAAgD;MACzDG,MAAM,EAAE;IACV,CAAC;IACD,gBAAgB,EAAE;MAChBH,OAAO,EAAE,+BAA+B;MACxCI,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE;MACNL,OAAO,EAAE,6CAA6C;MACtDI,KAAK,EAAE,QAAQ;MACfE,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ;IACF,CAAC;IACDA,QAAQ,EAAE,uDAAuD;IACjE;IACAC,OAAO,EACL,4HAA4H;IAC9HC,QAAQ,EAAE;MACRT,OAAO,EACL,uQAAuQ;MACzQC,UAAU,EAAE;IACd,CAAC;IACDS,QAAQ,EAAE,sBAAsB;IAChCC,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}