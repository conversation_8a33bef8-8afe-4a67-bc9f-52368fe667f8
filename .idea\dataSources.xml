<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="fin_data" uuid="6afa4374-3fb5-4564-8481-9e91773e4f2f">
      <driver-ref>sqlite.xerial</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.sqlite.JDBC</jdbc-driver>
      <jdbc-url>jdbc:sqlite:C:\Users\<USER>\PycharmProjects\智能数据分析系统\fin_data.db</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
      <libraries>
        <library>
          <url>file://$APPLICATION_CONFIG_DIR$/jdbc-drivers/Xerial SQLiteJDBC/3.45.1/org/xerial/sqlite-jdbc/3.45.1.0/sqlite-jdbc-3.45.1.0.jar</url>
        </library>
        <library>
          <url>file://$APPLICATION_CONFIG_DIR$/jdbc-drivers/Xerial SQLiteJDBC/3.45.1/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar</url>
        </library>
      </libraries>
    </data-source>
    <data-source source="LOCAL" name="resource" uuid="f0c87252-b59d-4722-85d1-4c4635f39dba">
      <driver-ref>sqlite.xerial</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.sqlite.JDBC</jdbc-driver>
      <jdbc-url>jdbc:sqlite:C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
      <libraries>
        <library>
          <url>file://$APPLICATION_CONFIG_DIR$/jdbc-drivers/Xerial SQLiteJDBC/3.45.1/org/xerial/sqlite-jdbc/3.45.1.0/sqlite-jdbc-3.45.1.0.jar</url>
        </library>
        <library>
          <url>file://$APPLICATION_CONFIG_DIR$/jdbc-drivers/Xerial SQLiteJDBC/3.45.1/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar</url>
        </library>
      </libraries>
    </data-source>
  </component>
</project>