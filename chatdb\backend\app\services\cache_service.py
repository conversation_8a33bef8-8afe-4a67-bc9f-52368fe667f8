"""
缓存服务 - 提供多层缓存机制优化系统性能
"""
import hashlib
import json
import time
from typing import Any, Dict, Optional, Union, List
from functools import wraps
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class CacheService:
    """
    多层缓存服务
    - 内存缓存：快速访问频繁使用的数据
    - 元数据缓存：缓存数据库结构信息
    - 查询结果缓存：缓存SQL查询结果
    """
    
    def __init__(self):
        # 内存缓存存储
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        
        # 缓存配置
        self.default_ttl = 3600  # 默认1小时过期
        self.metadata_ttl = 7200  # 元数据2小时过期
        self.query_result_ttl = 1800  # 查询结果30分钟过期
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'evictions': 0
        }
    
    def _generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        # 创建唯一标识符
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"{prefix}:{key_hash}"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self._memory_cache:
            self.stats['misses'] += 1
            return None
        
        cache_entry = self._memory_cache[key]
        
        # 检查是否过期
        if cache_entry['expires_at'] < time.time():
            del self._memory_cache[key]
            self.stats['misses'] += 1
            self.stats['evictions'] += 1
            return None
        
        self.stats['hits'] += 1
        return cache_entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        expires_at = time.time() + ttl
        self._memory_cache[key] = {
            'value': value,
            'expires_at': expires_at,
            'created_at': time.time()
        }
        self.stats['sets'] += 1
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        if key in self._memory_cache:
            del self._memory_cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._memory_cache.clear()
        logger.info("缓存已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.stats,
            'hit_rate': round(hit_rate, 2),
            'cache_size': len(self._memory_cache),
            'total_requests': total_requests
        }
    
    def cleanup_expired(self) -> int:
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._memory_cache.items()
            if entry['expires_at'] < current_time
        ]
        
        for key in expired_keys:
            del self._memory_cache[key]
        
        self.stats['evictions'] += len(expired_keys)
        return len(expired_keys)

# 全局缓存实例
cache_service = CacheService()

def cached(prefix: str, ttl: Optional[int] = None):
    """
    缓存装饰器
    
    Args:
        prefix: 缓存键前缀
        ttl: 过期时间（秒）
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = cache_service._generate_cache_key(prefix, *args, **kwargs)
            
            # 尝试从缓存获取
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_service.set(cache_key, result, ttl)
            logger.debug(f"缓存设置: {cache_key}")
            
            return result
        return wrapper
    return decorator

def cache_metadata(ttl: Optional[int] = None):
    """元数据缓存装饰器"""
    return cached("metadata", ttl or cache_service.metadata_ttl)

def cache_query_result(ttl: Optional[int] = None):
    """查询结果缓存装饰器"""
    return cached("query_result", ttl or cache_service.query_result_ttl)

def cache_schema(ttl: Optional[int] = None):
    """模式缓存装饰器"""
    return cached("schema", ttl or cache_service.metadata_ttl)

# 缓存管理函数
def invalidate_cache_by_pattern(pattern: str) -> int:
    """根据模式删除缓存"""
    deleted_count = 0
    keys_to_delete = [
        key for key in cache_service._memory_cache.keys()
        if pattern in key
    ]
    
    for key in keys_to_delete:
        cache_service.delete(key)
        deleted_count += 1
    
    logger.info(f"根据模式 '{pattern}' 删除了 {deleted_count} 个缓存项")
    return deleted_count

def warm_up_cache():
    """缓存预热"""
    logger.info("开始缓存预热...")
    # 这里可以预加载常用的元数据和配置
    # 例如：预加载数据库连接信息、常用的表结构等
    pass

# 定期清理任务
import threading
import time

def periodic_cleanup():
    """定期清理过期缓存"""
    while True:
        try:
            time.sleep(300)  # 每5分钟清理一次
            expired_count = cache_service.cleanup_expired()
            if expired_count > 0:
                logger.info(f"清理了 {expired_count} 个过期缓存项")
        except Exception as e:
            logger.error(f"缓存清理出错: {e}")

# 启动后台清理线程
cleanup_thread = threading.Thread(target=periodic_cleanup, daemon=True)
cleanup_thread.start()
