{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport XOutlinedSvg from \"@ant-design/icons-svg/es/asn/XOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar XOutlined = function XOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: XOutlinedSvg\n  }));\n};\n\n/**![x](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNOTIxIDkxMkw2MDEuMTEgNDQ1Ljc1bC41NS40M0w4OTAuMDggMTEySDc5My43TDU1OC43NCAzODQgMzcyLjE1IDExMkgxMTkuMzdsMjk4LjY1IDQzNS4zMS0uMDQtLjA0TDEwMyA5MTJoOTYuMzlMNDYwLjYgNjA5LjM4IDY2OC4yIDkxMnpNMzMzLjk2IDE4NC43M2w0NDguODMgNjU0LjU0SDcwNi40TDI1Ny4yIDE4NC43M3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(XOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'XOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "XOutlinedSvg", "AntdIcon", "XOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/XOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport XOutlinedSvg from \"@ant-design/icons-svg/es/asn/XOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar XOutlined = function XOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: XOutlinedSvg\n  }));\n};\n\n/**![x](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNOTIxIDkxMkw2MDEuMTEgNDQ1Ljc1bC41NS40M0w4OTAuMDggMTEySDc5My43TDU1OC43NCAzODQgMzcyLjE1IDExMkgxMTkuMzdsMjk4LjY1IDQzNS4zMS0uMDQtLjA0TDEwMyA5MTJoOTYuMzlMNDYwLjYgNjA5LjM4IDY2OC4yIDkxMnpNMzMzLjk2IDE4NC43M2w0NDguODMgNjU0LjU0SDcwNi40TDI1Ny4yIDE4NC43M3oiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(XOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'XOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7C,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,SAAS,CAAC;AACtD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,WAAW;AACnC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}