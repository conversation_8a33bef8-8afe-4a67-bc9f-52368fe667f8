# 智能数据分析系统项目结构优化脚本
# 重新组织项目文件结构，提高可维护性

param(
    [switch]$DryRun = $false
)

$OptimizeLog = "structure_optimize_log_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"

function Write-OptimizeLog {
    param($Message, $Color = "White")
    $LogEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss'): $Message"
    Write-Host $LogEntry -ForegroundColor $Color
    $LogEntry | Out-File -FilePath $OptimizeLog -Append -Encoding UTF8
}

function Move-FileToDirectory {
    param($SourcePath, $TargetDir, $Reason)
    
    if (Test-Path $SourcePath) {
        if ($DryRun) {
            Write-OptimizeLog "DRY RUN: 将移动 $SourcePath 到 $TargetDir - $Reason" "Yellow"
        } else {
            try {
                if (-not (Test-Path $TargetDir)) {
                    New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
                }
                
                $FileName = Split-Path $SourcePath -Leaf
                $TargetPath = Join-Path $TargetDir $FileName
                
                Move-Item $SourcePath $TargetPath -Force
                Write-OptimizeLog "已移动: $SourcePath -> $TargetPath - $Reason" "Green"
            } catch {
                Write-OptimizeLog "移动失败: $SourcePath - $($_.Exception.Message)" "Red"
            }
        }
    }
}

Write-OptimizeLog "开始项目结构优化..." "Cyan"

# 创建新的目录结构
$NewDirectories = @(
    "docs",           # 文档目录
    "scripts",        # 脚本目录
    "scripts\database",   # 数据库脚本
    "scripts\testing",    # 测试脚本
    "scripts\migration",  # 迁移脚本
    "backup",         # 备份目录
    "tools"           # 工具目录
)

Write-OptimizeLog "创建新的目录结构..." "Cyan"
foreach ($dir in $NewDirectories) {
    if ($DryRun) {
        Write-OptimizeLog "DRY RUN: 将创建目录 $dir" "Yellow"
    } else {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-OptimizeLog "已创建目录: $dir" "Green"
        }
    }
}

# 移动文档文件
Write-OptimizeLog "整理文档文件..." "Cyan"
$DocsToMove = @(
    "financial_data_column_guide.md",
    "METADATA_SYSTEM_DESIGN_PHILOSOPHY.md",
    "METADATA_SYSTEM_SUMMARY.md", 
    "TEXT2SQL_DETAILED_ANALYSIS.md",
    "TEXT2SQL_INTEGRATION_CHECKLIST.md",
    "FINAL_TECHNICAL_RECOMMENDATIONS.md",
    "DEPLOYMENT_CHECKLIST.md"
)

foreach ($doc in $DocsToMove) {
    Move-FileToDirectory $doc "docs" "重要项目文档"
}

# 移动数据库相关脚本
Write-OptimizeLog "整理数据库脚本..." "Cyan"
$DbScriptsToMove = @(
    "financial_data_metadata_queries.sql",
    "suggested_queries.sql"
)

foreach ($script in $DbScriptsToMove) {
    Move-FileToDirectory $script "scripts\database" "数据库查询脚本"
}

# 移动工具脚本（如果保留的话）
Write-OptimizeLog "整理工具脚本..." "Cyan"
$ToolScriptsToMove = @(
    "view_financial_data_descriptions.py",
    "ai_metadata_usage_examples.py"
)

foreach ($script in $ToolScriptsToMove) {
    Move-FileToDirectory $script "tools" "实用工具脚本"
}

# 移动备份文件
Write-OptimizeLog "整理备份文件..." "Cyan"
Get-ChildItem -Path "." -File | Where-Object { $_.Name -match "\.backup_" -or $_.Name -match "backup_\d+" } | ForEach-Object {
    Move-FileToDirectory $_.Name "backup" "数据库备份文件"
}

# 创建项目README
Write-OptimizeLog "创建项目README..." "Cyan"
$ReadmeContent = @"
# 智能数据分析系统

## 项目结构

```
智能数据分析系统/
├── chatdb/                 # 主应用程序
│   ├── backend/           # Python FastAPI后端
│   ├── frontend/          # React前端
│   └── docker-compose.yml # Docker配置
├── docs/                  # 项目文档
├── scripts/               # 脚本文件
│   ├── database/         # 数据库相关脚本
│   ├── testing/          # 测试脚本
│   └── migration/        # 迁移脚本
├── tools/                 # 实用工具
├── backup/                # 备份文件
├── fin_data.db           # 主数据库
├── resource.db           # 资源数据库
└── README.md             # 项目说明
```

## 快速开始

### 后端启动
```bash
cd chatdb/backend
pip install -r requirements.txt
python main.py
```

### 前端启动
```bash
cd chatdb/frontend
npm install
npm start
```

### Docker启动
```bash
cd chatdb
docker-compose up
```

## 主要功能

- 自然语言转SQL查询 (Text2SQL)
- 数据库连接管理
- 智能查询处理
- 数据可视化
- 元数据增强

## 文档

- [系统设计理念](docs/METADATA_SYSTEM_DESIGN_PHILOSOPHY.md)
- [Text2SQL详细分析](docs/TEXT2SQL_DETAILED_ANALYSIS.md)
- [部署检查清单](docs/DEPLOYMENT_CHECKLIST.md)
- [财务数据列指南](docs/financial_data_column_guide.md)

## 数据库

- `fin_data.db`: 主要财务数据
- `resource.db`: 系统资源和元数据

## 工具脚本

- `tools/view_financial_data_descriptions.py`: 查看财务数据描述
- `tools/ai_metadata_usage_examples.py`: AI元数据使用示例

## 备份

数据库备份文件存储在 `backup/` 目录中。

## 技术栈

- **后端**: Python, FastAPI, SQLAlchemy
- **前端**: React, TypeScript, Ant Design
- **数据库**: SQLite, MySQL (可选), Neo4j (可选)
- **AI**: 多LLM支持 (DeepSeek, OpenAI, 阿里云)

## 许可证

[添加许可证信息]
"@

if ($DryRun) {
    Write-OptimizeLog "DRY RUN: 将创建 README.md" "Yellow"
} else {
    $ReadmeContent | Out-File -FilePath "README.md" -Encoding UTF8
    Write-OptimizeLog "已创建: README.md" "Green"
}

# 创建.gitignore文件
Write-OptimizeLog "创建.gitignore文件..." "Cyan"
$GitignoreContent = @"
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.venv/
venv/
ENV/
env/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.db-journal
*.db-wal

# Backup files
backup/
*.backup
*.backup_*

# Logs
*.log
logs/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*~

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
"@

if ($DryRun) {
    Write-OptimizeLog "DRY RUN: 将创建 .gitignore" "Yellow"
} else {
    $GitignoreContent | Out-File -FilePath ".gitignore" -Encoding UTF8
    Write-OptimizeLog "已创建: .gitignore" "Green"
}

# 显示优化后的结构
Write-OptimizeLog "项目结构优化完成！" "Green"
Write-OptimizeLog "优化后的项目结构:" "Cyan"

if (-not $DryRun) {
    Get-ChildItem -Path "." -Directory | Select-Object Name, @{Name="Files";Expression={(Get-ChildItem $_.FullName -File -ErrorAction SilentlyContinue | Measure-Object).Count}} | Format-Table -AutoSize | Out-String | Write-OptimizeLog
}

Write-OptimizeLog "结构优化日志: $OptimizeLog" "Green"
