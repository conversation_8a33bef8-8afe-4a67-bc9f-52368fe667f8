{"ast": null, "code": "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n/**\n * @typedef {boolean | 'skip'} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<Ancestor>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   Tree type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {Visitor<import('./complex-types.js').Matches<import('./complex-types.js').InclusiveDescendant<Tree>, Check>, Extract<import('./complex-types.js').InclusiveDescendant<Tree>, Parent>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n */\n\nimport { convert } from 'unist-util-is';\nimport { color } from './color.js';\n\n/**\n * Continue traversing as normal.\n */\nexport const CONTINUE = true;\n\n/**\n * Stop traversing immediately.\n */\nexport const EXIT = false;\n\n/**\n * Do not traverse this node’s children.\n */\nexport const SKIP = 'skip';\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nexport const visitParents =\n/**\n * @type {(\n *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n * )}\n */\n\n/**\n * @param {Node} tree\n * @param {Test} test\n * @param {Visitor<Node>} visitor\n * @param {boolean | null | undefined} [reverse]\n * @returns {void}\n */\nfunction (tree, test, visitor, reverse) {\n  if (typeof test === 'function' && typeof visitor !== 'function') {\n    reverse = visitor;\n    // @ts-expect-error no visitor given, so `visitor` is test.\n    visitor = test;\n    test = null;\n  }\n  const is = convert(test);\n  const step = reverse ? -1 : 1;\n  factory(tree, undefined, [])();\n\n  /**\n   * @param {Node} node\n   * @param {number | undefined} index\n   * @param {Array<Parent>} parents\n   */\n  function factory(node, index, parents) {\n    /** @type {Record<string, unknown>} */\n    // @ts-expect-error: hush\n    const value = node && typeof node === 'object' ? node : {};\n    if (typeof value.type === 'string') {\n      const name =\n      // `hast`\n      typeof value.tagName === 'string' ? value.tagName :\n      // `xast`\n      typeof value.name === 'string' ? value.name : undefined;\n      Object.defineProperty(visit, 'name', {\n        value: 'node (' + color(node.type + (name ? '<' + name + '>' : '')) + ')'\n      });\n    }\n    return visit;\n    function visit() {\n      /** @type {ActionTuple} */\n      let result = [];\n      /** @type {ActionTuple} */\n      let subresult;\n      /** @type {number} */\n      let offset;\n      /** @type {Array<Parent>} */\n      let grandparents;\n      if (!test || is(node, index, parents[parents.length - 1] || null)) {\n        result = toResult(visitor(node, parents));\n        if (result[0] === EXIT) {\n          return result;\n        }\n      }\n\n      // @ts-expect-error looks like a parent.\n      if (node.children && result[0] !== SKIP) {\n        // @ts-expect-error looks like a parent.\n        offset = (reverse ? node.children.length : -1) + step;\n        // @ts-expect-error looks like a parent.\n        grandparents = parents.concat(node);\n\n        // @ts-expect-error looks like a parent.\n        while (offset > -1 && offset < node.children.length) {\n          // @ts-expect-error looks like a parent.\n          subresult = factory(node.children[offset], offset, grandparents)();\n          if (subresult[0] === EXIT) {\n            return subresult;\n          }\n          offset = typeof subresult[1] === 'number' ? subresult[1] : offset + step;\n        }\n      }\n      return result;\n    }\n  }\n};\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {ActionTuple}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  if (typeof value === 'number') {\n    return [CONTINUE, value];\n  }\n  return [value];\n}", "map": {"version": 3, "names": ["convert", "color", "CONTINUE", "EXIT", "SKIP", "visitParents", "tree", "test", "visitor", "reverse", "is", "step", "factory", "undefined", "node", "index", "parents", "value", "type", "name", "tagName", "Object", "defineProperty", "visit", "result", "subresult", "offset", "grandparents", "length", "toResult", "children", "concat", "Array", "isArray"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/unist-util-visit-parents/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n * @typedef {import('unist-util-is').Test} Test\n */\n\n/**\n * @typedef {boolean | 'skip'} Action\n *   Union of the action types.\n *\n * @typedef {number} Index\n *   Move to the sibling at `index` next (after node itself is completely\n *   traversed).\n *\n *   Useful if mutating the tree, such as removing the node the visitor is\n *   currently on, or any of its previous siblings.\n *   Results less than 0 or greater than or equal to `children.length` stop\n *   traversing the parent.\n *\n * @typedef {[(Action | null | undefined | void)?, (Index | null | undefined)?]} ActionTuple\n *   List with one or two values, the first an action, the second an index.\n *\n * @typedef {Action | ActionTuple | Index | null | undefined | void} VisitorResult\n *   Any value that can be returned from a visitor.\n */\n\n/**\n * @template {Node} [Visited=Node]\n *   Visited node type.\n * @template {Parent} [Ancestor=Parent]\n *   Ancestor type.\n * @callback Visitor\n *   Handle a node (matching `test`, if given).\n *\n *   Visitors are free to transform `node`.\n *   They can also transform the parent of node (the last of `ancestors`).\n *\n *   Replacing `node` itself, if `SKIP` is not returned, still causes its\n *   descendants to be walked (which is a bug).\n *\n *   When adding or removing previous siblings of `node` (or next siblings, in\n *   case of reverse), the `Visitor` should return a new `Index` to specify the\n *   sibling to traverse after `node` is traversed.\n *   Adding or removing next siblings of `node` (or previous siblings, in case\n *   of reverse) is handled as expected without needing to return a new `Index`.\n *\n *   Removing the children property of an ancestor still results in them being\n *   traversed.\n * @param {Visited} node\n *   Found node.\n * @param {Array<Ancestor>} ancestors\n *   Ancestors of `node`.\n * @returns {VisitorResult}\n *   What to do next.\n *\n *   An `Index` is treated as a tuple of `[CONTINUE, Index]`.\n *   An `Action` is treated as a tuple of `[Action]`.\n *\n *   Passing a tuple back only makes sense if the `Action` is `SKIP`.\n *   When the `Action` is `EXIT`, that action can be returned.\n *   When the `Action` is `CONTINUE`, `Index` can be returned.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   Tree type.\n * @template {Test} [Check=string]\n *   Test type.\n * @typedef {Visitor<import('./complex-types.js').Matches<import('./complex-types.js').InclusiveDescendant<Tree>, Check>, Extract<import('./complex-types.js').InclusiveDescendant<Tree>, Parent>>} BuildVisitor\n *   Build a typed `Visitor` function from a tree and a test.\n *\n *   It will infer which values are passed as `node` and which as `parents`.\n */\n\nimport {convert} from 'unist-util-is'\nimport {color} from './color.js'\n\n/**\n * Continue traversing as normal.\n */\nexport const CONTINUE = true\n\n/**\n * Stop traversing immediately.\n */\nexport const EXIT = false\n\n/**\n * Do not traverse this node’s children.\n */\nexport const SKIP = 'skip'\n\n/**\n * Visit nodes, with ancestral information.\n *\n * This algorithm performs *depth-first* *tree traversal* in *preorder*\n * (**NLR**) or if `reverse` is given, in *reverse preorder* (**NRL**).\n *\n * You can choose for which nodes `visitor` is called by passing a `test`.\n * For complex tests, you should test yourself in `visitor`, as it will be\n * faster and will have improved type information.\n *\n * Walking the tree is an intensive task.\n * Make use of the return values of the visitor when possible.\n * Instead of walking a tree multiple times, walk it once, use `unist-util-is`\n * to check if a node matches, and then perform different operations.\n *\n * You can change the tree.\n * See `Visitor` for more info.\n *\n * @param tree\n *   Tree to traverse.\n * @param test\n *   `unist-util-is`-compatible test\n * @param visitor\n *   Handle each node.\n * @param reverse\n *   Traverse in reverse preorder (NRL) instead of the default preorder (NLR).\n * @returns\n *   Nothing.\n */\nexport const visitParents =\n  /**\n   * @type {(\n   *   (<Tree extends Node, Check extends Test>(tree: Tree, test: Check, visitor: BuildVisitor<Tree, Check>, reverse?: boolean | null | undefined) => void) &\n   *   (<Tree extends Node>(tree: Tree, visitor: BuildVisitor<Tree>, reverse?: boolean | null | undefined) => void)\n   * )}\n   */\n  (\n    /**\n     * @param {Node} tree\n     * @param {Test} test\n     * @param {Visitor<Node>} visitor\n     * @param {boolean | null | undefined} [reverse]\n     * @returns {void}\n     */\n    function (tree, test, visitor, reverse) {\n      if (typeof test === 'function' && typeof visitor !== 'function') {\n        reverse = visitor\n        // @ts-expect-error no visitor given, so `visitor` is test.\n        visitor = test\n        test = null\n      }\n\n      const is = convert(test)\n      const step = reverse ? -1 : 1\n\n      factory(tree, undefined, [])()\n\n      /**\n       * @param {Node} node\n       * @param {number | undefined} index\n       * @param {Array<Parent>} parents\n       */\n      function factory(node, index, parents) {\n        /** @type {Record<string, unknown>} */\n        // @ts-expect-error: hush\n        const value = node && typeof node === 'object' ? node : {}\n\n        if (typeof value.type === 'string') {\n          const name =\n            // `hast`\n            typeof value.tagName === 'string'\n              ? value.tagName\n              : // `xast`\n              typeof value.name === 'string'\n              ? value.name\n              : undefined\n\n          Object.defineProperty(visit, 'name', {\n            value:\n              'node (' + color(node.type + (name ? '<' + name + '>' : '')) + ')'\n          })\n        }\n\n        return visit\n\n        function visit() {\n          /** @type {ActionTuple} */\n          let result = []\n          /** @type {ActionTuple} */\n          let subresult\n          /** @type {number} */\n          let offset\n          /** @type {Array<Parent>} */\n          let grandparents\n\n          if (!test || is(node, index, parents[parents.length - 1] || null)) {\n            result = toResult(visitor(node, parents))\n\n            if (result[0] === EXIT) {\n              return result\n            }\n          }\n\n          // @ts-expect-error looks like a parent.\n          if (node.children && result[0] !== SKIP) {\n            // @ts-expect-error looks like a parent.\n            offset = (reverse ? node.children.length : -1) + step\n            // @ts-expect-error looks like a parent.\n            grandparents = parents.concat(node)\n\n            // @ts-expect-error looks like a parent.\n            while (offset > -1 && offset < node.children.length) {\n              // @ts-expect-error looks like a parent.\n              subresult = factory(node.children[offset], offset, grandparents)()\n\n              if (subresult[0] === EXIT) {\n                return subresult\n              }\n\n              offset =\n                typeof subresult[1] === 'number' ? subresult[1] : offset + step\n            }\n          }\n\n          return result\n        }\n      }\n    }\n  )\n\n/**\n * Turn a return value into a clean result.\n *\n * @param {VisitorResult} value\n *   Valid return values from visitors.\n * @returns {ActionTuple}\n *   Clean result.\n */\nfunction toResult(value) {\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  if (typeof value === 'number') {\n    return [CONTINUE, value]\n  }\n\n  return [value]\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,OAAO,QAAO,eAAe;AACrC,SAAQC,KAAK,QAAO,YAAY;;AAEhC;AACA;AACA;AACA,OAAO,MAAMC,QAAQ,GAAG,IAAI;;AAE5B;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG,KAAK;;AAEzB;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG,MAAM;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY;AACvB;AACF;AACA;AACA;AACA;AACA;;AAEI;AACJ;AACA;AACA;AACA;AACA;AACA;AACI,SAAAA,CAAUC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACtC,IAAI,OAAOF,IAAI,KAAK,UAAU,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;IAC/DC,OAAO,GAAGD,OAAO;IACjB;IACAA,OAAO,GAAGD,IAAI;IACdA,IAAI,GAAG,IAAI;EACb;EAEA,MAAMG,EAAE,GAAGV,OAAO,CAACO,IAAI,CAAC;EACxB,MAAMI,IAAI,GAAGF,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;EAE7BG,OAAO,CAACN,IAAI,EAAEO,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;;EAE9B;AACN;AACA;AACA;AACA;EACM,SAASD,OAAOA,CAACE,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACrC;IACA;IACA,MAAMC,KAAK,GAAGH,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAG,CAAC,CAAC;IAE1D,IAAI,OAAOG,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;MAClC,MAAMC,IAAI;MACR;MACA,OAAOF,KAAK,CAACG,OAAO,KAAK,QAAQ,GAC7BH,KAAK,CAACG,OAAO;MACb;MACF,OAAOH,KAAK,CAACE,IAAI,KAAK,QAAQ,GAC5BF,KAAK,CAACE,IAAI,GACVN,SAAS;MAEfQ,MAAM,CAACC,cAAc,CAACC,KAAK,EAAE,MAAM,EAAE;QACnCN,KAAK,EACH,QAAQ,GAAGhB,KAAK,CAACa,IAAI,CAACI,IAAI,IAAIC,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,GAAG;MACnE,CAAC,CAAC;IACJ;IAEA,OAAOI,KAAK;IAEZ,SAASA,KAAKA,CAAA,EAAG;MACf;MACA,IAAIC,MAAM,GAAG,EAAE;MACf;MACA,IAAIC,SAAS;MACb;MACA,IAAIC,MAAM;MACV;MACA,IAAIC,YAAY;MAEhB,IAAI,CAACpB,IAAI,IAAIG,EAAE,CAACI,IAAI,EAAEC,KAAK,EAAEC,OAAO,CAACA,OAAO,CAACY,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;QACjEJ,MAAM,GAAGK,QAAQ,CAACrB,OAAO,CAACM,IAAI,EAAEE,OAAO,CAAC,CAAC;QAEzC,IAAIQ,MAAM,CAAC,CAAC,CAAC,KAAKrB,IAAI,EAAE;UACtB,OAAOqB,MAAM;QACf;MACF;;MAEA;MACA,IAAIV,IAAI,CAACgB,QAAQ,IAAIN,MAAM,CAAC,CAAC,CAAC,KAAKpB,IAAI,EAAE;QACvC;QACAsB,MAAM,GAAG,CAACjB,OAAO,GAAGK,IAAI,CAACgB,QAAQ,CAACF,MAAM,GAAG,CAAC,CAAC,IAAIjB,IAAI;QACrD;QACAgB,YAAY,GAAGX,OAAO,CAACe,MAAM,CAACjB,IAAI,CAAC;;QAEnC;QACA,OAAOY,MAAM,GAAG,CAAC,CAAC,IAAIA,MAAM,GAAGZ,IAAI,CAACgB,QAAQ,CAACF,MAAM,EAAE;UACnD;UACAH,SAAS,GAAGb,OAAO,CAACE,IAAI,CAACgB,QAAQ,CAACJ,MAAM,CAAC,EAAEA,MAAM,EAAEC,YAAY,CAAC,CAAC,CAAC;UAElE,IAAIF,SAAS,CAAC,CAAC,CAAC,KAAKtB,IAAI,EAAE;YACzB,OAAOsB,SAAS;UAClB;UAEAC,MAAM,GACJ,OAAOD,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAGC,MAAM,GAAGf,IAAI;QACnE;MACF;MAEA,OAAOa,MAAM;IACf;EACF;AACF,CACD;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,QAAQA,CAACZ,KAAK,EAAE;EACvB,IAAIe,KAAK,CAACC,OAAO,CAAChB,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK;EACd;EAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,CAACf,QAAQ,EAAEe,KAAK,CAAC;EAC1B;EAEA,OAAO,CAACA,KAAK,CAAC;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}