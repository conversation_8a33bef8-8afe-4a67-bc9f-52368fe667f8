# 智能数据分析系统安全清理脚本
# 删除可以安全移除的冗余文件

param(
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

$CleanupLog = "cleanup_log_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$TotalSaved = 0

function Write-CleanupLog {
    param($Message, $Color = "White")
    $LogEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss'): $Message"
    Write-Host $LogEntry -ForegroundColor $Color
    $LogEntry | Out-File -FilePath $CleanupLog -Append -Encoding UTF8
}

function Remove-SafeFile {
    param($FilePath, $Reason)
    
    if (Test-Path $FilePath) {
        $FileSize = (Get-Item $FilePath).Length
        
        if ($DryRun) {
            Write-CleanupLog "DRY RUN: 将删除 $FilePath ($([math]::Round($FileSize/1MB, 2)) MB) - $Reason" "Yellow"
        } else {
            try {
                Remove-Item $FilePath -Force
                Write-CleanupLog "已删除: $FilePath ($([math]::Round($FileSize/1MB, 2)) MB) - $Reason" "Green"
                $script:TotalSaved += $FileSize
            } catch {
                Write-CleanupLog "删除失败: $FilePath - $($_.Exception.Message)" "Red"
            }
        }
    }
}

function Remove-SafeDirectory {
    param($DirPath, $Reason)
    
    if (Test-Path $DirPath) {
        $DirSize = (Get-ChildItem $DirPath -Recurse -File | Measure-Object -Property Length -Sum).Sum
        
        if ($DryRun) {
            Write-CleanupLog "DRY RUN: 将删除目录 $DirPath ($([math]::Round($DirSize/1MB, 2)) MB) - $Reason" "Yellow"
        } else {
            try {
                Remove-Item $DirPath -Recurse -Force
                Write-CleanupLog "已删除目录: $DirPath ($([math]::Round($DirSize/1MB, 2)) MB) - $Reason" "Green"
                $script:TotalSaved += $DirSize
            } catch {
                Write-CleanupLog "删除目录失败: $DirPath - $($_.Exception.Message)" "Red"
            }
        }
    }
}

Write-CleanupLog "开始智能数据分析系统项目清理..." "Cyan"
if ($DryRun) {
    Write-CleanupLog "运行模式: 预览模式 (不会实际删除文件)" "Yellow"
} else {
    Write-CleanupLog "运行模式: 实际删除模式" "Red"
}

# 1. 清理数据库备份文件（保留最新的一个）
Write-CleanupLog "清理数据库备份文件..." "Cyan"

$BackupFiles = @(
    "fin_data.db.backup_20250626_073153",
    "fin_data.db.backup_20250627_085551", 
    "fin_data.db.before_restore_20250626_074020",
    "resource.db.backup_20250627_085551"
)

foreach ($backup in $BackupFiles) {
    Remove-SafeFile $backup "旧的数据库备份文件"
}

# 2. 清理Python缓存文件
Write-CleanupLog "清理Python缓存文件..." "Cyan"

Get-ChildItem -Path "." -Recurse -Directory -Name "__pycache__" | ForEach-Object {
    Remove-SafeDirectory $_ "Python字节码缓存"
}

# 3. 清理重复的Node.js依赖
Write-CleanupLog "清理重复的Node.js依赖..." "Cyan"

if (Test-Path "node_modules" -and (Test-Path "chatdb\frontend\node_modules")) {
    Remove-SafeDirectory "node_modules" "重复的Node.js依赖（前端已有）"
}

Remove-SafeFile "package.json" "重复的package.json（前端已有）"
Remove-SafeFile "package-lock.json" "重复的package-lock.json（前端已有）"

# 4. 清理临时文件
Write-CleanupLog "清理临时文件..." "Cyan"

$TempFiles = @(
    "*.tmp",
    "*.temp",
    "*~"
)

foreach ($pattern in $TempFiles) {
    Get-ChildItem -Path "." -File -Name $pattern | ForEach-Object {
        Remove-SafeFile $_ "临时文件"
    }
}

# 5. 清理CSV报告文件
Write-CleanupLog "清理CSV报告文件..." "Cyan"
Remove-SafeFile "management_expense_report_20250626_122140.csv" "临时生成的报告文件"

# 6. 清理过时的数据库文件
Write-CleanupLog "检查过时的数据库文件..." "Cyan"
Remove-SafeFile "fin_data.db.cleanup_backup_20250627_091002" "清理过程的备份文件"
Remove-SafeFile "resource.db.cleanup_backup_20250627_091002" "清理过程的备份文件"

# 总结
Write-CleanupLog "清理完成！" "Green"
Write-CleanupLog "总共节省空间: $([math]::Round($TotalSaved/1MB, 2)) MB" "Green"

if ($DryRun) {
    Write-CleanupLog "这是预览模式，没有实际删除文件。" "Yellow"
    Write-CleanupLog "要执行实际清理，请运行: .\project_cleanup_safe.ps1" "Yellow"
} else {
    Write-CleanupLog "实际清理已完成。日志文件: $CleanupLog" "Green"
}

# 显示清理后的项目结构
Write-CleanupLog "当前项目结构:" "Cyan"
Get-ChildItem -Path "." -Directory | Select-Object Name, @{Name="Size(MB)";Expression={(Get-ChildItem $_.FullName -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum / 1MB}} | Format-Table -AutoSize | Out-String | Write-CleanupLog
