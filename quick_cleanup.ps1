# 快速项目清理脚本
param([switch]$Execute = $false)

$TotalSaved = 0

function Show-FileInfo {
    param($FilePath, $Action)
    if (Test-Path $FilePath) {
        $Size = if (Test-Path $FilePath -PathType Container) {
            (Get-ChildItem $FilePath -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
        } else {
            (Get-Item $FilePath).Length
        }
        Write-Host "$Action`: $FilePath ($([math]::Round($Size/1MB, 2)) MB)" -ForegroundColor $(if($Execute){"Green"}else{"Yellow"})
        return $Size
    }
    return 0
}

function Remove-SafeItem {
    param($Path, $Reason)
    if (Test-Path $Path) {
        $Size = Show-FileInfo $Path $Reason
        if ($Execute) {
            try {
                if (Test-Path $Path -PathType Container) {
                    Remove-Item $Path -Recurse -Force
                } else {
                    Remove-Item $Path -Force
                }
                $script:TotalSaved += $Size
                Write-Host "✓ 已删除: $Path" -ForegroundColor Green
            } catch {
                Write-Host "✗ 删除失败: $Path - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

Write-Host "=== 智能数据分析系统项目清理 ===" -ForegroundColor Cyan
if ($Execute) {
    Write-Host "执行模式: 实际删除文件" -ForegroundColor Red
} else {
    Write-Host "预览模式: 显示将要删除的文件" -ForegroundColor Yellow
    Write-Host "要执行实际删除，请运行: .\quick_cleanup.ps1 -Execute" -ForegroundColor Yellow
}

Write-Host "`n1. 清理数据库备份文件..." -ForegroundColor Cyan
Remove-SafeItem "fin_data.db.backup_20250626_073153" "旧数据库备份"
Remove-SafeItem "fin_data.db.backup_20250627_085551" "旧数据库备份"
Remove-SafeItem "fin_data.db.before_restore_20250626_074020" "旧数据库备份"
Remove-SafeItem "fin_data.db.cleanup_backup_20250627_091002" "清理备份"
Remove-SafeItem "resource.db.backup_20250627_085551" "旧数据库备份"
Remove-SafeItem "resource.db.cleanup_backup_20250627_091002" "清理备份"

Write-Host "`n2. 清理Python缓存..." -ForegroundColor Cyan
Get-ChildItem -Path "." -Recurse -Directory -Name "__pycache__" -ErrorAction SilentlyContinue | ForEach-Object {
    Remove-SafeItem $_ "Python缓存"
}

Write-Host "`n3. 清理重复的Node.js依赖..." -ForegroundColor Cyan
if (Test-Path "node_modules" -and (Test-Path "chatdb\frontend\node_modules")) {
    Remove-SafeItem "node_modules" "重复的Node依赖"
}
Remove-SafeItem "package.json" "重复的package.json"
Remove-SafeItem "package-lock.json" "重复的package-lock.json"

Write-Host "`n4. 清理临时文件..." -ForegroundColor Cyan
Remove-SafeItem "management_expense_report_20250626_122140.csv" "临时报告文件"

Write-Host "`n=== 清理总结 ===" -ForegroundColor Cyan
if ($Execute) {
    Write-Host "实际清理完成！总共节省: $([math]::Round($TotalSaved/1MB, 2)) MB" -ForegroundColor Green
} else {
    Write-Host "预览完成。要执行实际清理，请运行:" -ForegroundColor Yellow
    Write-Host ".\quick_cleanup.ps1 -Execute" -ForegroundColor Yellow
}

Write-Host "`n当前项目大小:" -ForegroundColor Cyan
$ProjectSize = (Get-ChildItem -Path "." -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
Write-Host "总大小: $([math]::Round($ProjectSize/1GB, 2)) GB" -ForegroundColor White
