{"ast": null, "code": "// JSX Structure Syntactic Sugar. Never reach the render code.\n/* istanbul ignore next */\nconst DescriptionsItem = _ref => {\n  let {\n    children\n  } = _ref;\n  return children;\n};\nexport default DescriptionsItem;", "map": {"version": 3, "names": ["DescriptionsItem", "_ref", "children"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/descriptions/Item.js"], "sourcesContent": ["// JSX Structure Syntactic Sugar. Never reach the render code.\n/* istanbul ignore next */\nconst DescriptionsItem = _ref => {\n  let {\n    children\n  } = _ref;\n  return children;\n};\nexport default DescriptionsItem;"], "mappings": "AAAA;AACA;AACA,MAAMA,gBAAgB,GAAGC,IAAI,IAAI;EAC/B,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR,OAAOC,QAAQ;AACjB,CAAC;AACD,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}