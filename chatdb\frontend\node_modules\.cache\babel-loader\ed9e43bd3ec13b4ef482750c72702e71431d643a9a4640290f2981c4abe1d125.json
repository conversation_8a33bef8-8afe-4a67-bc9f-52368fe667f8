{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { initMotion } from './motion';\nexport const fadeIn = new Keyframes('antFadeIn', {\n  '0%': {\n    opacity: 0\n  },\n  '100%': {\n    opacity: 1\n  }\n});\nexport const fadeOut = new Keyframes('antFadeOut', {\n  '0%': {\n    opacity: 1\n  },\n  '100%': {\n    opacity: 0\n  }\n});\nexport const initFadeMotion = function (token) {\n  let sameLevel = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-fade`;\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return [initMotion(motionCls, fadeIn, fadeOut, token.motionDurationMid, sameLevel), {\n    [`\n        ${sameLevelPrefix}${motionCls}-enter,\n        ${sameLevelPrefix}${motionCls}-appear\n      `]: {\n      opacity: 0,\n      animationTimingFunction: 'linear'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave`]: {\n      animationTimingFunction: 'linear'\n    }\n  }];\n};", "map": {"version": 3, "names": ["Keyframes", "initMotion", "fadeIn", "opacity", "fadeOut", "initFadeMotion", "token", "sameLevel", "arguments", "length", "undefined", "antCls", "motionCls", "sameLevelPrefix", "motionDurationMid", "animationTimingFunction"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/style/motion/fade.js"], "sourcesContent": ["import { Keyframes } from '@ant-design/cssinjs';\nimport { initMotion } from './motion';\nexport const fadeIn = new Keyframes('antFadeIn', {\n  '0%': {\n    opacity: 0\n  },\n  '100%': {\n    opacity: 1\n  }\n});\nexport const fadeOut = new Keyframes('antFadeOut', {\n  '0%': {\n    opacity: 1\n  },\n  '100%': {\n    opacity: 0\n  }\n});\nexport const initFadeMotion = function (token) {\n  let sameLevel = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  const {\n    antCls\n  } = token;\n  const motionCls = `${antCls}-fade`;\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return [initMotion(motionCls, fadeIn, fadeOut, token.motionDurationMid, sameLevel), {\n    [`\n        ${sameLevelPrefix}${motionCls}-enter,\n        ${sameLevelPrefix}${motionCls}-appear\n      `]: {\n      opacity: 0,\n      animationTimingFunction: 'linear'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave`]: {\n      animationTimingFunction: 'linear'\n    }\n  }];\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAO,MAAMC,MAAM,GAAG,IAAIF,SAAS,CAAC,WAAW,EAAE;EAC/C,IAAI,EAAE;IACJG,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACNA,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,OAAO,GAAG,IAAIJ,SAAS,CAAC,YAAY,EAAE;EACjD,IAAI,EAAE;IACJG,OAAO,EAAE;EACX,CAAC;EACD,MAAM,EAAE;IACNA,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,OAAO,MAAME,cAAc,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAC7C,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,MAAM;IACJG;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,SAAS,GAAG,GAAGD,MAAM,OAAO;EAClC,MAAME,eAAe,GAAGN,SAAS,GAAG,GAAG,GAAG,EAAE;EAC5C,OAAO,CAACN,UAAU,CAACW,SAAS,EAAEV,MAAM,EAAEE,OAAO,EAAEE,KAAK,CAACQ,iBAAiB,EAAEP,SAAS,CAAC,EAAE;IAClF,CAAC;AACL,UAAUM,eAAe,GAAGD,SAAS;AACrC,UAAUC,eAAe,GAAGD,SAAS;AACrC,OAAO,GAAG;MACJT,OAAO,EAAE,CAAC;MACVY,uBAAuB,EAAE;IAC3B,CAAC;IACD,CAAC,GAAGF,eAAe,GAAGD,SAAS,QAAQ,GAAG;MACxCG,uBAAuB,EAAE;IAC3B;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}