{"ast": null, "code": "import React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\n/**\n * Callback will only execute last one for each raf\n */\nexport default function useRafDebounce(callback) {\n  const executeRef = React.useRef(false);\n  const rafRef = React.useRef(null);\n  const wrapperCallback = useEvent(callback);\n  return () => {\n    if (executeRef.current) {\n      return;\n    }\n    executeRef.current = true;\n    wrapperCallback();\n    rafRef.current = raf(() => {\n      executeRef.current = false;\n    });\n  };\n}", "map": {"version": 3, "names": ["React", "useEvent", "raf", "useRafDebounce", "callback", "executeRef", "useRef", "rafRef", "wrapperCallback", "current"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/watermark/useRafDebounce.js"], "sourcesContent": ["import React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\n/**\n * Callback will only execute last one for each raf\n */\nexport default function useRafDebounce(callback) {\n  const executeRef = React.useRef(false);\n  const rafRef = React.useRef(null);\n  const wrapperCallback = useEvent(callback);\n  return () => {\n    if (executeRef.current) {\n      return;\n    }\n    executeRef.current = true;\n    wrapperCallback();\n    rafRef.current = raf(() => {\n      executeRef.current = false;\n    });\n  };\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,GAAG,MAAM,gBAAgB;AAChC;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAC/C,MAAMC,UAAU,GAAGL,KAAK,CAACM,MAAM,CAAC,KAAK,CAAC;EACtC,MAAMC,MAAM,GAAGP,KAAK,CAACM,MAAM,CAAC,IAAI,CAAC;EACjC,MAAME,eAAe,GAAGP,QAAQ,CAACG,QAAQ,CAAC;EAC1C,OAAO,MAAM;IACX,IAAIC,UAAU,CAACI,OAAO,EAAE;MACtB;IACF;IACAJ,UAAU,CAACI,OAAO,GAAG,IAAI;IACzBD,eAAe,CAAC,CAAC;IACjBD,MAAM,CAACE,OAAO,GAAGP,GAAG,CAAC,MAAM;MACzBG,UAAU,CAACI,OAAO,GAAG,KAAK;IAC5B,CAAC,CAAC;EACJ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}