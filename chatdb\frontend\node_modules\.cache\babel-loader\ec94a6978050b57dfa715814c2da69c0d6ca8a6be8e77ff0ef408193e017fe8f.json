{"ast": null, "code": "import { FastColor } from '@ant-design/fast-color';\nvar hueStep = 2; // 色相阶梯\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nvar lightColorCount = 5; // 浅色数量，主色上\nvar darkColorCount = 4; // 深色数量，主色下\n\n// 暗色主题颜色映射关系表\nvar darkColorMap = [{\n  index: 7,\n  amount: 15\n}, {\n  index: 6,\n  amount: 25\n}, {\n  index: 5,\n  amount: 30\n}, {\n  index: 5,\n  amount: 45\n}, {\n  index: 5,\n  amount: 65\n}, {\n  index: 5,\n  amount: 85\n}, {\n  index: 4,\n  amount: 90\n}, {\n  index: 3,\n  amount: 95\n}, {\n  index: 2,\n  amount: 97\n}, {\n  index: 1,\n  amount: 98\n}];\nfunction getHue(hsv, i, light) {\n  var hue;\n  // 根据色相不同，色相转向不同\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  var saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  }\n  // 边界值修正\n  if (saturation > 1) {\n    saturation = 1;\n  }\n  // 第一格的 s 限制在 0.06-0.1 之间\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Math.round(saturation * 100) / 100;\n}\nfunction getValue(hsv, i, light) {\n  var value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  // Clamp value between 0 and 1\n  value = Math.max(0, Math.min(1, value));\n  return Math.round(value * 100) / 100;\n}\nexport default function generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = new FastColor(color);\n  var hsv = pColor.toHsv();\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var c = new FastColor({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    });\n    patterns.push(c);\n  }\n  patterns.push(pColor);\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _c = new FastColor({\n      h: getHue(hsv, _i),\n      s: getSaturation(hsv, _i),\n      v: getValue(hsv, _i)\n    });\n    patterns.push(_c);\n  }\n\n  // dark theme patterns\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref) {\n      var index = _ref.index,\n        amount = _ref.amount;\n      return new FastColor(opts.backgroundColor || '#141414').mix(patterns[index], amount).toHexString();\n    });\n  }\n  return patterns.map(function (c) {\n    return c.toHexString();\n  });\n}", "map": {"version": 3, "names": ["FastColor", "hueStep", "saturationStep", "saturationStep2", "brightnessStep1", "brightnessStep2", "lightColorCount", "darkColorCount", "darkColorMap", "index", "amount", "getHue", "hsv", "i", "light", "hue", "Math", "round", "h", "getSaturation", "s", "saturation", "getValue", "value", "v", "max", "min", "generate", "color", "opts", "arguments", "length", "undefined", "patterns", "pColor", "toHsv", "c", "push", "_i", "_c", "theme", "map", "_ref", "backgroundColor", "mix", "toHexString"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/colors/es/generate.js"], "sourcesContent": ["import { FastColor } from '@ant-design/fast-color';\nvar hueStep = 2; // 色相阶梯\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nvar lightColorCount = 5; // 浅色数量，主色上\nvar darkColorCount = 4; // 深色数量，主色下\n\n// 暗色主题颜色映射关系表\nvar darkColorMap = [{\n  index: 7,\n  amount: 15\n}, {\n  index: 6,\n  amount: 25\n}, {\n  index: 5,\n  amount: 30\n}, {\n  index: 5,\n  amount: 45\n}, {\n  index: 5,\n  amount: 65\n}, {\n  index: 5,\n  amount: 85\n}, {\n  index: 4,\n  amount: 90\n}, {\n  index: 3,\n  amount: 95\n}, {\n  index: 2,\n  amount: 97\n}, {\n  index: 1,\n  amount: 98\n}];\nfunction getHue(hsv, i, light) {\n  var hue;\n  // 根据色相不同，色相转向不同\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  var saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  }\n  // 边界值修正\n  if (saturation > 1) {\n    saturation = 1;\n  }\n  // 第一格的 s 限制在 0.06-0.1 之间\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Math.round(saturation * 100) / 100;\n}\nfunction getValue(hsv, i, light) {\n  var value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  // Clamp value between 0 and 1\n  value = Math.max(0, Math.min(1, value));\n  return Math.round(value * 100) / 100;\n}\nexport default function generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = new FastColor(color);\n  var hsv = pColor.toHsv();\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var c = new FastColor({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    });\n    patterns.push(c);\n  }\n  patterns.push(pColor);\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _c = new FastColor({\n      h: getHue(hsv, _i),\n      s: getSaturation(hsv, _i),\n      v: getValue(hsv, _i)\n    });\n    patterns.push(_c);\n  }\n\n  // dark theme patterns\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref) {\n      var index = _ref.index,\n        amount = _ref.amount;\n      return new FastColor(opts.backgroundColor || '#141414').mix(patterns[index], amount).toHexString();\n    });\n  }\n  return patterns.map(function (c) {\n    return c.toHexString();\n  });\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,IAAIC,OAAO,GAAG,CAAC,CAAC,CAAC;AACjB,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAC;AAC3B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,eAAe,GAAG,CAAC,CAAC,CAAC;AACzB,IAAIC,cAAc,GAAG,CAAC,CAAC,CAAC;;AAExB;AACA,IAAIC,YAAY,GAAG,CAAC;EAClBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE;AACV,CAAC,CAAC;AACF,SAASC,MAAMA,CAACC,GAAG,EAAEC,CAAC,EAAEC,KAAK,EAAE;EAC7B,IAAIC,GAAG;EACP;EACA,IAAIC,IAAI,CAACC,KAAK,CAACL,GAAG,CAACM,CAAC,CAAC,IAAI,EAAE,IAAIF,IAAI,CAACC,KAAK,CAACL,GAAG,CAACM,CAAC,CAAC,IAAI,GAAG,EAAE;IACvDH,GAAG,GAAGD,KAAK,GAAGE,IAAI,CAACC,KAAK,CAACL,GAAG,CAACM,CAAC,CAAC,GAAGjB,OAAO,GAAGY,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACL,GAAG,CAACM,CAAC,CAAC,GAAGjB,OAAO,GAAGY,CAAC;EACjF,CAAC,MAAM;IACLE,GAAG,GAAGD,KAAK,GAAGE,IAAI,CAACC,KAAK,CAACL,GAAG,CAACM,CAAC,CAAC,GAAGjB,OAAO,GAAGY,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACL,GAAG,CAACM,CAAC,CAAC,GAAGjB,OAAO,GAAGY,CAAC;EACjF;EACA,IAAIE,GAAG,GAAG,CAAC,EAAE;IACXA,GAAG,IAAI,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,IAAI,GAAG,EAAE;IACrBA,GAAG,IAAI,GAAG;EACZ;EACA,OAAOA,GAAG;AACZ;AACA,SAASI,aAAaA,CAACP,GAAG,EAAEC,CAAC,EAAEC,KAAK,EAAE;EACpC;EACA,IAAIF,GAAG,CAACM,CAAC,KAAK,CAAC,IAAIN,GAAG,CAACQ,CAAC,KAAK,CAAC,EAAE;IAC9B,OAAOR,GAAG,CAACQ,CAAC;EACd;EACA,IAAIC,UAAU;EACd,IAAIP,KAAK,EAAE;IACTO,UAAU,GAAGT,GAAG,CAACQ,CAAC,GAAGlB,cAAc,GAAGW,CAAC;EACzC,CAAC,MAAM,IAAIA,CAAC,KAAKN,cAAc,EAAE;IAC/Bc,UAAU,GAAGT,GAAG,CAACQ,CAAC,GAAGlB,cAAc;EACrC,CAAC,MAAM;IACLmB,UAAU,GAAGT,GAAG,CAACQ,CAAC,GAAGjB,eAAe,GAAGU,CAAC;EAC1C;EACA;EACA,IAAIQ,UAAU,GAAG,CAAC,EAAE;IAClBA,UAAU,GAAG,CAAC;EAChB;EACA;EACA,IAAIP,KAAK,IAAID,CAAC,KAAKP,eAAe,IAAIe,UAAU,GAAG,GAAG,EAAE;IACtDA,UAAU,GAAG,GAAG;EAClB;EACA,IAAIA,UAAU,GAAG,IAAI,EAAE;IACrBA,UAAU,GAAG,IAAI;EACnB;EACA,OAAOL,IAAI,CAACC,KAAK,CAACI,UAAU,GAAG,GAAG,CAAC,GAAG,GAAG;AAC3C;AACA,SAASC,QAAQA,CAACV,GAAG,EAAEC,CAAC,EAAEC,KAAK,EAAE;EAC/B,IAAIS,KAAK;EACT,IAAIT,KAAK,EAAE;IACTS,KAAK,GAAGX,GAAG,CAACY,CAAC,GAAGpB,eAAe,GAAGS,CAAC;EACrC,CAAC,MAAM;IACLU,KAAK,GAAGX,GAAG,CAACY,CAAC,GAAGnB,eAAe,GAAGQ,CAAC;EACrC;EACA;EACAU,KAAK,GAAGP,IAAI,CAACS,GAAG,CAAC,CAAC,EAAET,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEH,KAAK,CAAC,CAAC;EACvC,OAAOP,IAAI,CAACC,KAAK,CAACM,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;AACtC;AACA,eAAe,SAASI,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACjF,IAAIG,QAAQ,GAAG,EAAE;EACjB,IAAIC,MAAM,GAAG,IAAIlC,SAAS,CAAC4B,KAAK,CAAC;EACjC,IAAIhB,GAAG,GAAGsB,MAAM,CAACC,KAAK,CAAC,CAAC;EACxB,KAAK,IAAItB,CAAC,GAAGP,eAAe,EAAEO,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IAC3C,IAAIuB,CAAC,GAAG,IAAIpC,SAAS,CAAC;MACpBkB,CAAC,EAAEP,MAAM,CAACC,GAAG,EAAEC,CAAC,EAAE,IAAI,CAAC;MACvBO,CAAC,EAAED,aAAa,CAACP,GAAG,EAAEC,CAAC,EAAE,IAAI,CAAC;MAC9BW,CAAC,EAAEF,QAAQ,CAACV,GAAG,EAAEC,CAAC,EAAE,IAAI;IAC1B,CAAC,CAAC;IACFoB,QAAQ,CAACI,IAAI,CAACD,CAAC,CAAC;EAClB;EACAH,QAAQ,CAACI,IAAI,CAACH,MAAM,CAAC;EACrB,KAAK,IAAII,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI/B,cAAc,EAAE+B,EAAE,IAAI,CAAC,EAAE;IAC9C,IAAIC,EAAE,GAAG,IAAIvC,SAAS,CAAC;MACrBkB,CAAC,EAAEP,MAAM,CAACC,GAAG,EAAE0B,EAAE,CAAC;MAClBlB,CAAC,EAAED,aAAa,CAACP,GAAG,EAAE0B,EAAE,CAAC;MACzBd,CAAC,EAAEF,QAAQ,CAACV,GAAG,EAAE0B,EAAE;IACrB,CAAC,CAAC;IACFL,QAAQ,CAACI,IAAI,CAACE,EAAE,CAAC;EACnB;;EAEA;EACA,IAAIV,IAAI,CAACW,KAAK,KAAK,MAAM,EAAE;IACzB,OAAOhC,YAAY,CAACiC,GAAG,CAAC,UAAUC,IAAI,EAAE;MACtC,IAAIjC,KAAK,GAAGiC,IAAI,CAACjC,KAAK;QACpBC,MAAM,GAAGgC,IAAI,CAAChC,MAAM;MACtB,OAAO,IAAIV,SAAS,CAAC6B,IAAI,CAACc,eAAe,IAAI,SAAS,CAAC,CAACC,GAAG,CAACX,QAAQ,CAACxB,KAAK,CAAC,EAAEC,MAAM,CAAC,CAACmC,WAAW,CAAC,CAAC;IACpG,CAAC,CAAC;EACJ;EACA,OAAOZ,QAAQ,CAACQ,GAAG,CAAC,UAAUL,CAAC,EAAE;IAC/B,OAAOA,CAAC,CAACS,WAAW,CAAC,CAAC;EACxB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}