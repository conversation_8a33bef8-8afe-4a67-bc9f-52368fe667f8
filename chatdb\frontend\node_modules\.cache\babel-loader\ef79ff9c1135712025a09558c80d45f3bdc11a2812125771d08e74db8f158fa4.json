{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GifOutlinedSvg from \"@ant-design/icons-svg/es/asn/GifOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GifOutlined = function GifOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GifOutlinedSvg\n  }));\n};\n\n/**![gif](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05NDQgMjk5SDY5MmMtNC40IDAtOCAzLjYtOCA4djQwNmMwIDQuNCAzLjYgOCA4IDhoNTkuMmM0LjQgMCA4LTMuNiA4LThWNTQ5LjloMTY4LjJjNC40IDAgOC0zLjYgOC04VjQ5NWMwLTQuNC0zLjYtOC04LThINzU5LjJWMzY0LjJIOTQ0YzQuNCAwIDgtMy42IDgtOFYzMDdjMC00LjQtMy42LTgtOC04em0tMzU2IDFoLTU2Yy00LjQgMC04IDMuNi04IDh2NDA2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LThWMzA4YzAtNC40LTMuNi04LTgtOHpNNDUyIDUwMC45SDI5MC41Yy00LjQgMC04IDMuNi04IDh2NDMuN2MwIDQuNCAzLjYgOCA4IDhoOTQuOWwtLjMgOC45Yy0xLjIgNTguOC00NS42IDk4LjUtMTEwLjkgOTguNS03Ni4yIDAtMTIzLjktNTkuNy0xMjMuOS0xNTYuNyAwLTk1LjggNDYuOC0xNTUuMiAxMjEuNS0xNTUuMiA1NC44IDAgOTMuMSAyNi45IDEwOC41IDc1LjRoNzYuMmMtMTMuNi04Ny4yLTg2LTE0My40LTE4NC43LTE0My40QzE1MCAyODggNzIgMzc1LjIgNzIgNTExLjkgNzIgNjUwLjIgMTQ5LjEgNzM2IDI3MyA3MzZjMTE0LjEgMCAxODctNzAuNyAxODctMTgxLjZ2LTQ1LjVjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GifOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GifOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "GifOutlinedSvg", "AntdIcon", "GifOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/GifOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GifOutlinedSvg from \"@ant-design/icons-svg/es/asn/GifOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GifOutlined = function GifOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GifOutlinedSvg\n  }));\n};\n\n/**![gif](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05NDQgMjk5SDY5MmMtNC40IDAtOCAzLjYtOCA4djQwNmMwIDQuNCAzLjYgOCA4IDhoNTkuMmM0LjQgMCA4LTMuNiA4LThWNTQ5LjloMTY4LjJjNC40IDAgOC0zLjYgOC04VjQ5NWMwLTQuNC0zLjYtOC04LThINzU5LjJWMzY0LjJIOTQ0YzQuNCAwIDgtMy42IDgtOFYzMDdjMC00LjQtMy42LTgtOC04em0tMzU2IDFoLTU2Yy00LjQgMC04IDMuNi04IDh2NDA2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LThWMzA4YzAtNC40LTMuNi04LTgtOHpNNDUyIDUwMC45SDI5MC41Yy00LjQgMC04IDMuNi04IDh2NDMuN2MwIDQuNCAzLjYgOCA4IDhoOTQuOWwtLjMgOC45Yy0xLjIgNTguOC00NS42IDk4LjUtMTEwLjkgOTguNS03Ni4yIDAtMTIzLjktNTkuNy0xMjMuOS0xNTYuNyAwLTk1LjggNDYuOC0xNTUuMiAxMjEuNS0xNTUuMiA1NC44IDAgOTMuMSAyNi45IDEwOC41IDc1LjRoNzYuMmMtMTMuNi04Ny4yLTg2LTE0My40LTE4NC43LTE0My40QzE1MCAyODggNzIgMzc1LjIgNzIgNTExLjkgNzIgNjUwLjIgMTQ5LjEgNzM2IDI3MyA3MzZjMTE0LjEgMCAxODctNzAuNyAxODctMTgxLjZ2LTQ1LjVjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GifOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GifOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}