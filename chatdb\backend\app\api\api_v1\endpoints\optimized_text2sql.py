"""
优化版本的Text2SQL API端点
集成性能监控、缓存、错误处理和健康检查功能
"""
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
import json
import asyncio
import time
import logging
from typing import AsyncGenerator, Dict, Any
from datetime import datetime

from app.api import deps
from app.schemas.query import QueryRequest, QueryResponse
from app.services.text2sql_service import process_text2sql_query
from app.crud.crud_db_connection import db_connection
from app.services.monitoring_service import monitor_api_call, monitoring_service
from app.services.performance_optimizer import performance_optimizer
from app.services.cache_service import cache_service
from app.utils.sqlite_utils import analyze_query_performance

router = APIRouter()
logger = logging.getLogger(__name__)

# 性能统计
api_stats = {
    'total_requests': 0,
    'successful_requests': 0,
    'failed_requests': 0,
    'avg_response_time': 0,
    'cache_hits': 0,
}

@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "optimized-text2sql",
        "timestamp": datetime.now().isoformat(),
        "stats": api_stats,
        "cache_stats": cache_service.get_stats(),
        "performance_stats": performance_optimizer.get_performance_stats()
    }

@router.get("/performance-report")
async def get_performance_report():
    """获取详细的性能报告"""
    return {
        "api_performance": api_stats,
        "cache_performance": cache_service.get_stats(),
        "system_performance": performance_optimizer.get_performance_stats(),
        "health_status": performance_optimizer.check_performance_health(),
        "timestamp": datetime.now().isoformat()
    }

@router.post("/query", response_model=QueryResponse)
@monitor_api_call("text2sql_query")
async def execute_text2sql_query(
    request: QueryRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db)
):
    """
    执行Text2SQL查询（优化版本）
    集成缓存、性能监控和错误处理
    """
    start_time = time.time()
    api_stats['total_requests'] += 1
    
    try:
        # 1. 验证请求参数
        if not request.query or not request.query.strip():
            raise HTTPException(status_code=400, detail="查询内容不能为空")
        
        if not request.connection_id:
            raise HTTPException(status_code=400, detail="连接ID不能为空")
        
        # 2. 获取数据库连接信息
        connection = db_connection.get(db, id=request.connection_id)
        if not connection:
            raise HTTPException(status_code=404, detail="数据库连接不存在")
        
        # 3. 检查缓存
        cache_key = f"text2sql:{request.connection_id}:{hash(request.query)}"
        cached_result = cache_service.get(cache_key)
        
        if cached_result:
            api_stats['cache_hits'] += 1
            logger.info(f"缓存命中: {request.query[:50]}...")
            
            # 更新统计信息
            response_time = time.time() - start_time
            _update_stats(response_time, True)
            
            return QueryResponse(**cached_result)
        
        # 4. 执行查询
        logger.info(f"执行Text2SQL查询: {request.query[:100]}...")
        
        # 使用性能监控装饰器
        @performance_optimizer.monitor_query_performance
        def execute_query():
            return process_text2sql_query(db, connection, request.query)
        
        result = execute_query()
        
        # 5. 分析查询性能
        if result.sql:
            query_analysis = analyze_query_performance(result.sql)
            result.context = result.context or {}
            result.context['performance_analysis'] = query_analysis
        
        # 6. 缓存结果
        if result.sql and not result.error:
            cache_service.set(cache_key, result.dict(), ttl=1800)  # 缓存30分钟
        
        # 7. 后台任务：记录查询历史
        background_tasks.add_task(
            _record_query_history,
            request.query,
            result.sql,
            request.connection_id,
            time.time() - start_time
        )
        
        # 8. 更新统计信息
        response_time = time.time() - start_time
        _update_stats(response_time, True)
        
        api_stats['successful_requests'] += 1
        logger.info(f"查询执行成功，耗时: {response_time:.3f}秒")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        # 记录错误
        response_time = time.time() - start_time
        _update_stats(response_time, False)
        
        api_stats['failed_requests'] += 1
        monitoring_service.record_error(e, {
            'query': request.query[:200],
            'connection_id': request.connection_id,
            'response_time': response_time
        })
        
        logger.error(f"查询执行失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询执行失败: {str(e)}")

@router.post("/query-stream")
@monitor_api_call("text2sql_query_stream")
async def execute_text2sql_query_stream(
    request: QueryRequest,
    db: Session = Depends(deps.get_db)
):
    """
    流式执行Text2SQL查询
    提供实时进度反馈
    """
    async def generate_response() -> AsyncGenerator[str, None]:
        try:
            # 发送开始事件
            yield f"data: {json.dumps({'type': 'start', 'message': '开始处理查询...'})}\n\n"
            
            # 获取连接信息
            connection = db_connection.get(db, id=request.connection_id)
            if not connection:
                yield f"data: {json.dumps({'type': 'error', 'message': '数据库连接不存在'})}\n\n"
                return
            
            # 发送进度事件
            yield f"data: {json.dumps({'type': 'progress', 'message': '正在分析查询...', 'progress': 20})}\n\n"
            
            # 执行查询
            result = process_text2sql_query(db, connection, request.query)
            
            # 发送SQL生成事件
            if result.sql:
                yield f"data: {json.dumps({'type': 'sql_generated', 'sql': result.sql, 'progress': 60})}\n\n"
            
            # 发送结果事件
            if result.error:
                yield f"data: {json.dumps({'type': 'error', 'message': result.error})}\n\n"
            else:
                yield f"data: {json.dumps({'type': 'progress', 'message': '正在执行查询...', 'progress': 80})}\n\n"
                yield f"data: {json.dumps({'type': 'results', 'data': result.results, 'progress': 100})}\n\n"
            
            # 发送完成事件
            yield f"data: {json.dumps({'type': 'complete', 'message': '查询完成'})}\n\n"
            
        except Exception as e:
            logger.error(f"流式查询失败: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'message': f'查询失败: {str(e)}'})}\n\n"
    
    return StreamingResponse(
        generate_response(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@router.post("/analyze-query")
async def analyze_query(
    request: Dict[str, str],
    db: Session = Depends(deps.get_db)
):
    """
    分析SQL查询性能
    """
    sql = request.get('sql', '')
    if not sql:
        raise HTTPException(status_code=400, detail="SQL查询不能为空")
    
    try:
        analysis = analyze_query_performance(sql)
        return {
            "sql": sql,
            "analysis": analysis,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"查询分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询分析失败: {str(e)}")

@router.post("/clear-cache")
async def clear_cache():
    """清空缓存"""
    try:
        cache_service.clear()
        return {
            "message": "缓存已清空",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")

@router.get("/cache-stats")
async def get_cache_stats():
    """获取缓存统计信息"""
    return cache_service.get_stats()

def _update_stats(response_time: float, success: bool):
    """更新API统计信息"""
    # 更新平均响应时间
    total_requests = api_stats['total_requests']
    current_avg = api_stats['avg_response_time']
    api_stats['avg_response_time'] = (current_avg * (total_requests - 1) + response_time) / total_requests

async def _record_query_history(query: str, sql: str, connection_id: int, response_time: float):
    """记录查询历史（后台任务）"""
    try:
        # 这里可以添加查询历史记录逻辑
        logger.info(f"记录查询历史: {query[:50]}... (耗时: {response_time:.3f}秒)")
    except Exception as e:
        logger.error(f"记录查询历史失败: {str(e)}")

# 启动时的初始化
@router.on_event("startup")
async def startup_event():
    """API启动时的初始化"""
    logger.info("优化版Text2SQL API启动")
    
    # 预热缓存
    try:
        # 这里可以添加缓存预热逻辑
        pass
    except Exception as e:
        logger.error(f"缓存预热失败: {str(e)}")

@router.on_event("shutdown")
async def shutdown_event():
    """API关闭时的清理"""
    logger.info("优化版Text2SQL API关闭")
    
    # 清理资源
    try:
        # 这里可以添加资源清理逻辑
        pass
    except Exception as e:
        logger.error(f"资源清理失败: {str(e)}")
