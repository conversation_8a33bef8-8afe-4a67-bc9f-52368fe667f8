{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\pages\\\\ValueMappingsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Card, Select, Button, Table, Spin, message, Typography, Form, Input, Modal, Popconfirm, Space } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport * as api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  Title\n} = Typography;\nconst ValueMappingsPage = () => {\n  _s();\n  const [connections, setConnections] = useState([]);\n  const [selectedConnection, setSelectedConnection] = useState(null);\n  const [tables, setTables] = useState([]);\n  const [selectedTable, setSelectedTable] = useState(null);\n  const [columns, setColumns] = useState([]);\n  const [selectedColumn, setSelectedColumn] = useState(null);\n  const [valueMappings, setValueMappings] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingMapping, setEditingMapping] = useState(null);\n  const [form] = Form.useForm();\n\n  // Define fetch functions with useCallback\n  const fetchConnections = async () => {\n    try {\n      const response = await api.getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取连接失败');\n      console.error(error);\n    }\n  };\n  const fetchTables = async connectionId => {\n    setLoading(true);\n    try {\n      const response = await api.getSchemaMetadata(connectionId);\n      setTables(response.data);\n      setSelectedTable(null);\n      setColumns([]);\n      setSelectedColumn(null);\n      setValueMappings([]);\n    } catch (error) {\n      message.error('获取表失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchColumns = useCallback(async tableId => {\n    setLoading(true);\n    try {\n      const selectedTableData = tables.find(t => t.id === tableId);\n      if (!selectedTableData) return;\n\n      // Get columns for this table from the schema metadata\n      const response = await api.getSchemaMetadata(selectedConnection);\n      const tableData = response.data.find(t => t.id === tableId);\n      if (tableData && tableData.columns) {\n        const columnsWithTableInfo = tableData.columns.map(col => ({\n          ...col,\n          table_id: tableId,\n          table_name: selectedTableData.table_name\n        }));\n        setColumns(columnsWithTableInfo);\n      } else {\n        setColumns([]);\n      }\n      setSelectedColumn(null);\n      setValueMappings([]);\n    } catch (error) {\n      message.error('获取列失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedConnection, tables]);\n  const fetchValueMappings = async columnId => {\n    setLoading(true);\n    try {\n      const response = await api.getValueMappings(columnId);\n      setValueMappings(response.data);\n    } catch (error) {\n      message.error('获取值映射失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Setup effects\n  useEffect(() => {\n    fetchConnections();\n  }, []);\n  useEffect(() => {\n    if (selectedConnection) {\n      fetchTables(selectedConnection);\n    }\n  }, [selectedConnection]);\n  useEffect(() => {\n    if (selectedTable) {\n      fetchColumns(selectedTable);\n    }\n  }, [selectedTable, fetchColumns]);\n  useEffect(() => {\n    if (selectedColumn) {\n      fetchValueMappings(selectedColumn);\n    }\n  }, [selectedColumn]);\n  const handleConnectionChange = value => {\n    setSelectedConnection(value);\n  };\n  const handleTableChange = value => {\n    setSelectedTable(value);\n  };\n  const handleColumnChange = value => {\n    setSelectedColumn(value);\n  };\n  const showModal = mapping => {\n    setEditingMapping(mapping || null);\n    form.resetFields();\n    if (mapping) {\n      form.setFieldsValue({\n        nl_term: mapping.nl_term,\n        db_value: mapping.db_value\n      });\n    }\n    setModalVisible(true);\n  };\n  const handleCancel = () => {\n    setModalVisible(false);\n  };\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingMapping) {\n        await api.updateValueMapping(editingMapping.id, values);\n        message.success('值映射更新成功');\n      } else {\n        await api.createValueMapping({\n          ...values,\n          column_id: selectedColumn\n        });\n        message.success('值映射创建成功');\n      }\n      setModalVisible(false);\n      fetchValueMappings(selectedColumn);\n    } catch (error) {\n      message.error('保存值映射失败');\n      console.error(error);\n    }\n  };\n  const handleDelete = async id => {\n    try {\n      await api.deleteValueMapping(id);\n      message.success('值映射删除成功');\n      fetchValueMappings(selectedColumn);\n    } catch (error) {\n      message.error('删除值映射失败');\n      console.error(error);\n    }\n  };\n  const columns_table = [{\n    title: '自然语言术语',\n    dataIndex: 'nl_term',\n    key: 'nl_term'\n  }, {\n    title: '数据库值',\n    dataIndex: 'db_value',\n    key: 'db_value'\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 19\n        }, this),\n        onClick: () => showModal(record),\n        size: \"small\",\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u6620\\u5C04\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u662F\",\n        cancelText: \"\\u5426\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u503C\\u6620\\u5C04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\",\n            style: {\n              width: 200,\n              marginRight: 8\n            },\n            onChange: handleConnectionChange,\n            value: selectedConnection || undefined,\n            children: connections.map(conn => /*#__PURE__*/_jsxDEV(Option, {\n              value: conn.id,\n              children: conn.name\n            }, conn.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u8868\",\n            style: {\n              width: 200,\n              marginRight: 8\n            },\n            onChange: handleTableChange,\n            value: selectedTable || undefined,\n            disabled: !selectedConnection,\n            children: tables.map(table => /*#__PURE__*/_jsxDEV(Option, {\n              value: table.id,\n              children: table.table_name\n            }, table.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u5217\",\n            style: {\n              width: 200\n            },\n            onChange: handleColumnChange,\n            value: selectedColumn || undefined,\n            disabled: !selectedTable,\n            children: columns.map(column => /*#__PURE__*/_jsxDEV(Option, {\n              value: column.id,\n              children: column.column_name\n            }, column.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), selectedColumn && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 21\n          }, this),\n          onClick: () => showModal(),\n          children: \"\\u6DFB\\u52A0\\u503C\\u6620\\u5C04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Spin, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 11\n      }, this) : selectedColumn ? /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns_table,\n        dataSource: valueMappings,\n        rowKey: \"id\",\n        pagination: {\n          pageSize: 10\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u8BF7\\u9009\\u62E9\\u8FDE\\u63A5\\u3001\\u8868\\u548C\\u5217\\u6765\\u7BA1\\u7406\\u503C\\u6620\\u5C04\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingMapping ? '编辑值映射' : '添加值映射',\n      open: modalVisible,\n      onOk: handleSubmit,\n      onCancel: handleCancel,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"nl_term\",\n          label: \"\\u81EA\\u7136\\u8BED\\u8A00\\u672F\\u8BED\",\n          rules: [{\n            required: true,\n            message: '请输入自然语言术语'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"e.g., \\u4E2D\\u77F3\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"db_value\",\n          label: \"\\u6570\\u636E\\u5E93\\u503C\",\n          rules: [{\n            required: true,\n            message: '请输入数据库值'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"e.g., \\u4E2D\\u56FD\\u77F3\\u5316\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(ValueMappingsPage, \"lWdibE0nUkDT0B96fQsq75fyOys=\", false, function () {\n  return [Form.useForm];\n});\n_c = ValueMappingsPage;\nexport default ValueMappingsPage;\nvar _c;\n$RefreshReg$(_c, \"ValueMappingsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Card", "Select", "<PERSON><PERSON>", "Table", "Spin", "message", "Typography", "Form", "Input", "Modal", "Popconfirm", "Space", "PlusOutlined", "EditOutlined", "DeleteOutlined", "api", "jsxDEV", "_jsxDEV", "Option", "Title", "ValueMappingsPage", "_s", "connections", "setConnections", "selectedConnection", "setSelectedConnection", "tables", "setTables", "selectedTable", "setSelectedTable", "columns", "setColumns", "selectedColumn", "setSelectedColumn", "valueMappings", "setValueMappings", "loading", "setLoading", "modalVisible", "setModalVisible", "editingMapping", "setEditingMapping", "form", "useForm", "fetchConnections", "response", "getConnections", "data", "error", "console", "fetchTables", "connectionId", "getSchemaMetadata", "fetchColumns", "tableId", "selectedTableData", "find", "t", "id", "tableData", "columnsWithTableInfo", "map", "col", "table_id", "table_name", "fetchValueMappings", "columnId", "getValueMappings", "handleConnectionChange", "value", "handleTableChange", "handleColumnChange", "showModal", "mapping", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nl_term", "db_value", "handleCancel", "handleSubmit", "values", "validateFields", "updateValueMapping", "success", "createValueMapping", "column_id", "handleDelete", "deleteValueMapping", "columns_table", "title", "dataIndex", "key", "render", "_", "record", "size", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onConfirm", "okText", "cancelText", "danger", "style", "display", "justifyContent", "marginBottom", "level", "placeholder", "width", "marginRight", "onChange", "undefined", "conn", "name", "disabled", "table", "column", "column_name", "type", "textAlign", "padding", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "open", "onOk", "onCancel", "layout", "<PERSON><PERSON>", "label", "rules", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/pages/ValueMappingsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Card, Select, Button, Table, Spin, message,\n  Typography, Form, Input, Modal, Popconfirm, Space\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport * as api from '../services/api';\n\nconst { Option } = Select;\nconst { Title } = Typography;\n\ninterface DBConnection {\n  id: number;\n  name: string;\n}\n\ninterface SchemaTable {\n  id: number;\n  table_name: string;\n  description?: string;\n}\n\ninterface SchemaColumn {\n  id: number;\n  column_name: string;\n  data_type: string;\n  description?: string;\n  table_id: number;\n  table_name: string;\n}\n\ninterface ValueMapping {\n  id: number;\n  column_id: number;\n  nl_term: string;\n  db_value: string;\n}\n\nconst ValueMappingsPage: React.FC = () => {\n  const [connections, setConnections] = useState<DBConnection[]>([]);\n  const [selectedConnection, setSelectedConnection] = useState<number | null>(null);\n  const [tables, setTables] = useState<SchemaTable[]>([]);\n  const [selectedTable, setSelectedTable] = useState<number | null>(null);\n  const [columns, setColumns] = useState<SchemaColumn[]>([]);\n  const [selectedColumn, setSelectedColumn] = useState<number | null>(null);\n  const [valueMappings, setValueMappings] = useState<ValueMapping[]>([]);\n  const [loading, setLoading] = useState<boolean>(false);\n  const [modalVisible, setModalVisible] = useState<boolean>(false);\n  const [editingMapping, setEditingMapping] = useState<ValueMapping | null>(null);\n  const [form] = Form.useForm();\n\n  // Define fetch functions with useCallback\n  const fetchConnections = async () => {\n    try {\n      const response = await api.getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取连接失败');\n      console.error(error);\n    }\n  };\n\n  const fetchTables = async (connectionId: number) => {\n    setLoading(true);\n    try {\n      const response = await api.getSchemaMetadata(connectionId);\n      setTables(response.data);\n      setSelectedTable(null);\n      setColumns([]);\n      setSelectedColumn(null);\n      setValueMappings([]);\n    } catch (error) {\n      message.error('获取表失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchColumns = useCallback(async (tableId: number) => {\n    setLoading(true);\n    try {\n      const selectedTableData = tables.find(t => t.id === tableId);\n      if (!selectedTableData) return;\n\n      // Get columns for this table from the schema metadata\n      const response = await api.getSchemaMetadata(selectedConnection!);\n      const tableData = response.data.find((t: any) => t.id === tableId);\n\n      if (tableData && tableData.columns) {\n        const columnsWithTableInfo = tableData.columns.map((col: any) => ({\n          ...col,\n          table_id: tableId,\n          table_name: selectedTableData.table_name\n        }));\n        setColumns(columnsWithTableInfo);\n      } else {\n        setColumns([]);\n      }\n\n      setSelectedColumn(null);\n      setValueMappings([]);\n    } catch (error) {\n      message.error('获取列失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedConnection, tables]);\n\n  const fetchValueMappings = async (columnId: number) => {\n    setLoading(true);\n    try {\n      const response = await api.getValueMappings(columnId);\n      setValueMappings(response.data);\n    } catch (error) {\n      message.error('获取值映射失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Setup effects\n  useEffect(() => {\n    fetchConnections();\n  }, []);\n\n  useEffect(() => {\n    if (selectedConnection) {\n      fetchTables(selectedConnection);\n    }\n  }, [selectedConnection]);\n\n  useEffect(() => {\n    if (selectedTable) {\n      fetchColumns(selectedTable);\n    }\n  }, [selectedTable, fetchColumns]);\n\n  useEffect(() => {\n    if (selectedColumn) {\n      fetchValueMappings(selectedColumn);\n    }\n  }, [selectedColumn]);\n\n  const handleConnectionChange = (value: number) => {\n    setSelectedConnection(value);\n  };\n\n  const handleTableChange = (value: number) => {\n    setSelectedTable(value);\n  };\n\n  const handleColumnChange = (value: number) => {\n    setSelectedColumn(value);\n  };\n\n  const showModal = (mapping?: ValueMapping) => {\n    setEditingMapping(mapping || null);\n    form.resetFields();\n    if (mapping) {\n      form.setFieldsValue({\n        nl_term: mapping.nl_term,\n        db_value: mapping.db_value,\n      });\n    }\n    setModalVisible(true);\n  };\n\n  const handleCancel = () => {\n    setModalVisible(false);\n  };\n\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (editingMapping) {\n        await api.updateValueMapping(editingMapping.id, values);\n        message.success('值映射更新成功');\n      } else {\n        await api.createValueMapping({\n          ...values,\n          column_id: selectedColumn\n        });\n        message.success('值映射创建成功');\n      }\n\n      setModalVisible(false);\n      fetchValueMappings(selectedColumn!);\n    } catch (error) {\n      message.error('保存值映射失败');\n      console.error(error);\n    }\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await api.deleteValueMapping(id);\n      message.success('值映射删除成功');\n      fetchValueMappings(selectedColumn!);\n    } catch (error) {\n      message.error('删除值映射失败');\n      console.error(error);\n    }\n  };\n\n  const columns_table = [\n    {\n      title: '自然语言术语',\n      dataIndex: 'nl_term',\n      key: 'nl_term',\n    },\n    {\n      title: '数据库值',\n      dataIndex: 'db_value',\n      key: 'db_value',\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_: any, record: ValueMapping) => (\n        <Space size=\"middle\">\n          <Button\n            icon={<EditOutlined />}\n            onClick={() => showModal(record)}\n            size=\"small\"\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个映射吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"是\"\n            cancelText=\"否\"\n          >\n            <Button\n              danger\n              icon={<DeleteOutlined />}\n              size=\"small\"\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>\n          <Title level={4}>值映射</Title>\n          <div>\n            <Select\n              placeholder=\"选择数据库连接\"\n              style={{ width: 200, marginRight: 8 }}\n              onChange={handleConnectionChange}\n              value={selectedConnection || undefined}\n            >\n              {connections.map(conn => (\n                <Option key={conn.id} value={conn.id}>{conn.name}</Option>\n              ))}\n            </Select>\n\n            <Select\n              placeholder=\"选择表\"\n              style={{ width: 200, marginRight: 8 }}\n              onChange={handleTableChange}\n              value={selectedTable || undefined}\n              disabled={!selectedConnection}\n            >\n              {tables.map(table => (\n                <Option key={table.id} value={table.id}>{table.table_name}</Option>\n              ))}\n            </Select>\n\n            <Select\n              placeholder=\"选择列\"\n              style={{ width: 200 }}\n              onChange={handleColumnChange}\n              value={selectedColumn || undefined}\n              disabled={!selectedTable}\n            >\n              {columns.map(column => (\n                <Option key={column.id} value={column.id}>{column.column_name}</Option>\n              ))}\n            </Select>\n          </div>\n        </div>\n\n        {selectedColumn && (\n          <div style={{ marginBottom: 16 }}>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={() => showModal()}\n            >\n              添加值映射\n            </Button>\n          </div>\n        )}\n\n        {loading ? (\n          <div style={{ textAlign: 'center', padding: '20px' }}>\n            <Spin />\n          </div>\n        ) : selectedColumn ? (\n          <Table\n            columns={columns_table}\n            dataSource={valueMappings}\n            rowKey=\"id\"\n            pagination={{ pageSize: 10 }}\n          />\n        ) : (\n          <div style={{ textAlign: 'center', padding: '20px' }}>\n            <p>请选择连接、表和列来管理值映射</p>\n          </div>\n        )}\n      </Card>\n\n      <Modal\n        title={editingMapping ? '编辑值映射' : '添加值映射'}\n        open={modalVisible}\n        onOk={handleSubmit}\n        onCancel={handleCancel}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"nl_term\"\n            label=\"自然语言术语\"\n            rules={[{ required: true, message: '请输入自然语言术语' }]}\n          >\n            <Input placeholder=\"e.g., 中石化\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"db_value\"\n            label=\"数据库值\"\n            rules={[{ required: true, message: '请输入数据库值' }]}\n          >\n            <Input placeholder=\"e.g., 中国石化\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ValueMappingsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAC1CC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,QAC5C,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAQ,mBAAmB;AAC9E,OAAO,KAAKC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAM;EAAEC;AAAO,CAAC,GAAGjB,MAAM;AACzB,MAAM;EAAEkB;AAAM,CAAC,GAAGb,UAAU;AA6B5B,MAAMc,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAiB,EAAE,CAAC;EAClE,MAAM,CAAC2B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;EACjF,MAAM,CAAC6B,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAAgB,EAAE,CAAC;EACvD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAiB,EAAE,CAAC;EAC1D,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAiB,EAAE,CAAC;EACtE,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAsB,IAAI,CAAC;EAC/E,MAAM,CAAC6C,IAAI,CAAC,GAAGnC,IAAI,CAACoC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM9B,GAAG,CAAC+B,cAAc,CAAC,CAAC;MAC3CvB,cAAc,CAACsB,QAAQ,CAACE,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAME,WAAW,GAAG,MAAOC,YAAoB,IAAK;IAClDd,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAM9B,GAAG,CAACqC,iBAAiB,CAACD,YAAY,CAAC;MAC1DxB,SAAS,CAACkB,QAAQ,CAACE,IAAI,CAAC;MACxBlB,gBAAgB,CAAC,IAAI,CAAC;MACtBE,UAAU,CAAC,EAAE,CAAC;MACdE,iBAAiB,CAAC,IAAI,CAAC;MACvBE,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC,OAAOa,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,OAAO,CAAC;MACtBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,YAAY,GAAGtD,WAAW,CAAC,MAAOuD,OAAe,IAAK;IAC1DjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkB,iBAAiB,GAAG7B,MAAM,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,OAAO,CAAC;MAC5D,IAAI,CAACC,iBAAiB,EAAE;;MAExB;MACA,MAAMV,QAAQ,GAAG,MAAM9B,GAAG,CAACqC,iBAAiB,CAAC5B,kBAAmB,CAAC;MACjE,MAAMmC,SAAS,GAAGd,QAAQ,CAACE,IAAI,CAACS,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACC,EAAE,KAAKJ,OAAO,CAAC;MAElE,IAAIK,SAAS,IAAIA,SAAS,CAAC7B,OAAO,EAAE;QAClC,MAAM8B,oBAAoB,GAAGD,SAAS,CAAC7B,OAAO,CAAC+B,GAAG,CAAEC,GAAQ,KAAM;UAChE,GAAGA,GAAG;UACNC,QAAQ,EAAET,OAAO;UACjBU,UAAU,EAAET,iBAAiB,CAACS;QAChC,CAAC,CAAC,CAAC;QACHjC,UAAU,CAAC6B,oBAAoB,CAAC;MAClC,CAAC,MAAM;QACL7B,UAAU,CAAC,EAAE,CAAC;MAChB;MAEAE,iBAAiB,CAAC,IAAI,CAAC;MACvBE,gBAAgB,CAAC,EAAE,CAAC;IACtB,CAAC,CAAC,OAAOa,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,OAAO,CAAC;MACtBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACb,kBAAkB,EAAEE,MAAM,CAAC,CAAC;EAEhC,MAAMuC,kBAAkB,GAAG,MAAOC,QAAgB,IAAK;IACrD7B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAM9B,GAAG,CAACoD,gBAAgB,CAACD,QAAQ,CAAC;MACrD/B,gBAAgB,CAACU,QAAQ,CAACE,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,SAAS,CAAC;MACxBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAvC,SAAS,CAAC,MAAM;IACd8C,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN9C,SAAS,CAAC,MAAM;IACd,IAAI0B,kBAAkB,EAAE;MACtB0B,WAAW,CAAC1B,kBAAkB,CAAC;IACjC;EACF,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB1B,SAAS,CAAC,MAAM;IACd,IAAI8B,aAAa,EAAE;MACjByB,YAAY,CAACzB,aAAa,CAAC;IAC7B;EACF,CAAC,EAAE,CAACA,aAAa,EAAEyB,YAAY,CAAC,CAAC;EAEjCvD,SAAS,CAAC,MAAM;IACd,IAAIkC,cAAc,EAAE;MAClBiC,kBAAkB,CAACjC,cAAc,CAAC;IACpC;EACF,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,MAAMoC,sBAAsB,GAAIC,KAAa,IAAK;IAChD5C,qBAAqB,CAAC4C,KAAK,CAAC;EAC9B,CAAC;EAED,MAAMC,iBAAiB,GAAID,KAAa,IAAK;IAC3CxC,gBAAgB,CAACwC,KAAK,CAAC;EACzB,CAAC;EAED,MAAME,kBAAkB,GAAIF,KAAa,IAAK;IAC5CpC,iBAAiB,CAACoC,KAAK,CAAC;EAC1B,CAAC;EAED,MAAMG,SAAS,GAAIC,OAAsB,IAAK;IAC5ChC,iBAAiB,CAACgC,OAAO,IAAI,IAAI,CAAC;IAClC/B,IAAI,CAACgC,WAAW,CAAC,CAAC;IAClB,IAAID,OAAO,EAAE;MACX/B,IAAI,CAACiC,cAAc,CAAC;QAClBC,OAAO,EAAEH,OAAO,CAACG,OAAO;QACxBC,QAAQ,EAAEJ,OAAO,CAACI;MACpB,CAAC,CAAC;IACJ;IACAtC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMuC,YAAY,GAAGA,CAAA,KAAM;IACzBvC,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMwC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMtC,IAAI,CAACuC,cAAc,CAAC,CAAC;MAE1C,IAAIzC,cAAc,EAAE;QAClB,MAAMzB,GAAG,CAACmE,kBAAkB,CAAC1C,cAAc,CAACkB,EAAE,EAAEsB,MAAM,CAAC;QACvD3E,OAAO,CAAC8E,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAC,MAAM;QACL,MAAMpE,GAAG,CAACqE,kBAAkB,CAAC;UAC3B,GAAGJ,MAAM;UACTK,SAAS,EAAErD;QACb,CAAC,CAAC;QACF3B,OAAO,CAAC8E,OAAO,CAAC,SAAS,CAAC;MAC5B;MAEA5C,eAAe,CAAC,KAAK,CAAC;MACtB0B,kBAAkB,CAACjC,cAAe,CAAC;IACrC,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,SAAS,CAAC;MACxBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMsC,YAAY,GAAG,MAAO5B,EAAU,IAAK;IACzC,IAAI;MACF,MAAM3C,GAAG,CAACwE,kBAAkB,CAAC7B,EAAE,CAAC;MAChCrD,OAAO,CAAC8E,OAAO,CAAC,SAAS,CAAC;MAC1BlB,kBAAkB,CAACjC,cAAe,CAAC;IACrC,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,SAAS,CAAC;MACxBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMwC,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACC,CAAM,EAAEC,MAAoB,kBACnC7E,OAAA,CAACN,KAAK;MAACoF,IAAI,EAAC,QAAQ;MAAAC,QAAA,gBAClB/E,OAAA,CAACf,MAAM;QACL+F,IAAI,eAAEhF,OAAA,CAACJ,YAAY;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,OAAO,EAAEA,CAAA,KAAM9B,SAAS,CAACsB,MAAM,CAAE;QACjCC,IAAI,EAAC,OAAO;QAAAC,QAAA,EACb;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTpF,OAAA,CAACP,UAAU;QACT+E,KAAK,EAAC,oEAAa;QACnBc,SAAS,EAAEA,CAAA,KAAMjB,YAAY,CAACQ,MAAM,CAACpC,EAAE,CAAE;QACzC8C,MAAM,EAAC,QAAG;QACVC,UAAU,EAAC,QAAG;QAAAT,QAAA,eAEd/E,OAAA,CAACf,MAAM;UACLwG,MAAM;UACNT,IAAI,eAAEhF,OAAA,CAACH,cAAc;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBN,IAAI,EAAC,OAAO;UAAAC,QAAA,EACb;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEpF,OAAA;IAAA+E,QAAA,gBACE/E,OAAA,CAACjB,IAAI;MAAAgG,QAAA,gBACH/E,OAAA;QAAK0F,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAd,QAAA,gBACjF/E,OAAA,CAACE,KAAK;UAAC4F,KAAK,EAAE,CAAE;UAAAf,QAAA,EAAC;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5BpF,OAAA;UAAA+E,QAAA,gBACE/E,OAAA,CAAChB,MAAM;YACL+G,WAAW,EAAC,4CAAS;YACrBL,KAAK,EAAE;cAAEM,KAAK,EAAE,GAAG;cAAEC,WAAW,EAAE;YAAE,CAAE;YACtCC,QAAQ,EAAE/C,sBAAuB;YACjCC,KAAK,EAAE7C,kBAAkB,IAAI4F,SAAU;YAAApB,QAAA,EAEtC1E,WAAW,CAACuC,GAAG,CAACwD,IAAI,iBACnBpG,OAAA,CAACC,MAAM;cAAemD,KAAK,EAAEgD,IAAI,CAAC3D,EAAG;cAAAsC,QAAA,EAAEqB,IAAI,CAACC;YAAI,GAAnCD,IAAI,CAAC3D,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAqC,CAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAETpF,OAAA,CAAChB,MAAM;YACL+G,WAAW,EAAC,oBAAK;YACjBL,KAAK,EAAE;cAAEM,KAAK,EAAE,GAAG;cAAEC,WAAW,EAAE;YAAE,CAAE;YACtCC,QAAQ,EAAE7C,iBAAkB;YAC5BD,KAAK,EAAEzC,aAAa,IAAIwF,SAAU;YAClCG,QAAQ,EAAE,CAAC/F,kBAAmB;YAAAwE,QAAA,EAE7BtE,MAAM,CAACmC,GAAG,CAAC2D,KAAK,iBACfvG,OAAA,CAACC,MAAM;cAAgBmD,KAAK,EAAEmD,KAAK,CAAC9D,EAAG;cAAAsC,QAAA,EAAEwB,KAAK,CAACxD;YAAU,GAA5CwD,KAAK,CAAC9D,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA6C,CACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAETpF,OAAA,CAAChB,MAAM;YACL+G,WAAW,EAAC,oBAAK;YACjBL,KAAK,EAAE;cAAEM,KAAK,EAAE;YAAI,CAAE;YACtBE,QAAQ,EAAE5C,kBAAmB;YAC7BF,KAAK,EAAErC,cAAc,IAAIoF,SAAU;YACnCG,QAAQ,EAAE,CAAC3F,aAAc;YAAAoE,QAAA,EAExBlE,OAAO,CAAC+B,GAAG,CAAC4D,MAAM,iBACjBxG,OAAA,CAACC,MAAM;cAAiBmD,KAAK,EAAEoD,MAAM,CAAC/D,EAAG;cAAAsC,QAAA,EAAEyB,MAAM,CAACC;YAAW,GAAhDD,MAAM,CAAC/D,EAAE;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgD,CACvE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELrE,cAAc,iBACbf,OAAA;QAAK0F,KAAK,EAAE;UAAEG,YAAY,EAAE;QAAG,CAAE;QAAAd,QAAA,eAC/B/E,OAAA,CAACf,MAAM;UACLyH,IAAI,EAAC,SAAS;UACd1B,IAAI,eAAEhF,OAAA,CAACL,YAAY;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEA,CAAA,KAAM9B,SAAS,CAAC,CAAE;UAAAwB,QAAA,EAC5B;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEAjE,OAAO,gBACNnB,OAAA;QAAK0F,KAAK,EAAE;UAAEiB,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAA7B,QAAA,eACnD/E,OAAA,CAACb,IAAI;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,GACJrE,cAAc,gBAChBf,OAAA,CAACd,KAAK;QACJ2B,OAAO,EAAE0D,aAAc;QACvBsC,UAAU,EAAE5F,aAAc;QAC1B6F,MAAM,EAAC,IAAI;QACXC,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,gBAEFpF,OAAA;QAAK0F,KAAK,EAAE;UAAEiB,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAA7B,QAAA,eACnD/E,OAAA;UAAA+E,QAAA,EAAG;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEPpF,OAAA,CAACR,KAAK;MACJgF,KAAK,EAAEjD,cAAc,GAAG,OAAO,GAAG,OAAQ;MAC1C0F,IAAI,EAAE5F,YAAa;MACnB6F,IAAI,EAAEpD,YAAa;MACnBqD,QAAQ,EAAEtD,YAAa;MAAAkB,QAAA,eAEvB/E,OAAA,CAACV,IAAI;QACHmC,IAAI,EAAEA,IAAK;QACX2F,MAAM,EAAC,UAAU;QAAArC,QAAA,gBAEjB/E,OAAA,CAACV,IAAI,CAAC+H,IAAI;UACRhB,IAAI,EAAC,SAAS;UACdiB,KAAK,EAAC,sCAAQ;UACdC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEpI,OAAO,EAAE;UAAY,CAAC,CAAE;UAAA2F,QAAA,eAElD/E,OAAA,CAACT,KAAK;YAACwG,WAAW,EAAC;UAAW;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAEZpF,OAAA,CAACV,IAAI,CAAC+H,IAAI;UACRhB,IAAI,EAAC,UAAU;UACfiB,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEpI,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA2F,QAAA,eAEhD/E,OAAA,CAACT,KAAK;YAACwG,WAAW,EAAC;UAAY;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAChF,EAAA,CA1TID,iBAA2B;EAAA,QAWhBb,IAAI,CAACoC,OAAO;AAAA;AAAA+F,EAAA,GAXvBtH,iBAA2B;AA4TjC,eAAeA,iBAAiB;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}