{"ast": null, "code": "export function isValidWaveColor(color) {\n  return color && color !== '#fff' && color !== '#ffffff' && color !== 'rgb(255, 255, 255)' && color !== 'rgba(255, 255, 255, 1)' && !/rgba\\((?:\\d*, ){3}0\\)/.test(color) &&\n  // any transparent rgba color\n  color !== 'transparent';\n}\nexport function getTargetWaveColor(node) {\n  const {\n    borderTopColor,\n    borderColor,\n    backgroundColor\n  } = getComputedStyle(node);\n  if (isValidWaveColor(borderTopColor)) {\n    return borderTopColor;\n  }\n  if (isValidWaveColor(borderColor)) {\n    return borderColor;\n  }\n  if (isValidWaveColor(backgroundColor)) {\n    return backgroundColor;\n  }\n  return null;\n}", "map": {"version": 3, "names": ["isValidWaveColor", "color", "test", "getTargetWaveColor", "node", "borderTopColor", "borderColor", "backgroundColor", "getComputedStyle"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/_util/wave/util.js"], "sourcesContent": ["export function isValidWaveColor(color) {\n  return color && color !== '#fff' && color !== '#ffffff' && color !== 'rgb(255, 255, 255)' && color !== 'rgba(255, 255, 255, 1)' && !/rgba\\((?:\\d*, ){3}0\\)/.test(color) &&\n  // any transparent rgba color\n  color !== 'transparent';\n}\nexport function getTargetWaveColor(node) {\n  const {\n    borderTopColor,\n    borderColor,\n    backgroundColor\n  } = getComputedStyle(node);\n  if (isValidWaveColor(borderTopColor)) {\n    return borderTopColor;\n  }\n  if (isValidWaveColor(borderColor)) {\n    return borderColor;\n  }\n  if (isValidWaveColor(backgroundColor)) {\n    return backgroundColor;\n  }\n  return null;\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAOA,KAAK,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,oBAAoB,IAAIA,KAAK,KAAK,wBAAwB,IAAI,CAAC,uBAAuB,CAACC,IAAI,CAACD,KAAK,CAAC;EACvK;EACAA,KAAK,KAAK,aAAa;AACzB;AACA,OAAO,SAASE,kBAAkBA,CAACC,IAAI,EAAE;EACvC,MAAM;IACJC,cAAc;IACdC,WAAW;IACXC;EACF,CAAC,GAAGC,gBAAgB,CAACJ,IAAI,CAAC;EAC1B,IAAIJ,gBAAgB,CAACK,cAAc,CAAC,EAAE;IACpC,OAAOA,cAAc;EACvB;EACA,IAAIL,gBAAgB,CAACM,WAAW,CAAC,EAAE;IACjC,OAAOA,WAAW;EACpB;EACA,IAAIN,gBAAgB,CAACO,eAAe,CAAC,EAAE;IACrC,OAAOA,eAAe;EACxB;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}