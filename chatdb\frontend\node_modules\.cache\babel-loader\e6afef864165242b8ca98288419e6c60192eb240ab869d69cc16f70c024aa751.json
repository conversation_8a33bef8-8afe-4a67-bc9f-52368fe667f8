{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { Provider as MotionProvider } from 'rc-motion';\nimport { useToken } from '../theme/internal';\nexport default function MotionWrapper(props) {\n  const {\n    children\n  } = props;\n  const [, token] = useToken();\n  const {\n    motion\n  } = token;\n  const needWrapMotionProviderRef = React.useRef(false);\n  needWrapMotionProviderRef.current = needWrapMotionProviderRef.current || motion === false;\n  if (needWrapMotionProviderRef.current) {\n    return /*#__PURE__*/React.createElement(MotionProvider, {\n      motion: motion\n    }, children);\n  }\n  return children;\n}", "map": {"version": 3, "names": ["React", "Provider", "MotionProvider", "useToken", "MotionWrapper", "props", "children", "token", "motion", "needWrapMotionProviderRef", "useRef", "current", "createElement"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/config-provider/MotionWrapper.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { Provider as MotionProvider } from 'rc-motion';\nimport { useToken } from '../theme/internal';\nexport default function MotionWrapper(props) {\n  const {\n    children\n  } = props;\n  const [, token] = useToken();\n  const {\n    motion\n  } = token;\n  const needWrapMotionProviderRef = React.useRef(false);\n  needWrapMotionProviderRef.current = needWrapMotionProviderRef.current || motion === false;\n  if (needWrapMotionProviderRef.current) {\n    return /*#__PURE__*/React.createElement(MotionProvider, {\n      motion: motion\n    }, children);\n  }\n  return children;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,IAAIC,cAAc,QAAQ,WAAW;AACtD,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAM,GAAGE,KAAK,CAAC,GAAGJ,QAAQ,CAAC,CAAC;EAC5B,MAAM;IACJK;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,yBAAyB,GAAGT,KAAK,CAACU,MAAM,CAAC,KAAK,CAAC;EACrDD,yBAAyB,CAACE,OAAO,GAAGF,yBAAyB,CAACE,OAAO,IAAIH,MAAM,KAAK,KAAK;EACzF,IAAIC,yBAAyB,CAACE,OAAO,EAAE;IACrC,OAAO,aAAaX,KAAK,CAACY,aAAa,CAACV,cAAc,EAAE;MACtDM,MAAM,EAAEA;IACV,CAAC,EAAEF,QAAQ,CAAC;EACd;EACA,OAAOA,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}