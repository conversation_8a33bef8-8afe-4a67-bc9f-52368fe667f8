{"ast": null, "code": "'use strict';\n\nvar refractorTurtle = require('./turtle.js');\nmodule.exports = sparql;\nsparql.displayName = 'sparql';\nsparql.aliases = ['rq'];\nfunction sparql(Prism) {\n  Prism.register(refractorTurtle);\n  Prism.languages.sparql = Prism.languages.extend('turtle', {\n    boolean: /\\b(?:false|true)\\b/i,\n    variable: {\n      pattern: /[?$]\\w+/,\n      greedy: true\n    }\n  });\n  Prism.languages.insertBefore('sparql', 'punctuation', {\n    keyword: [/\\b(?:A|ADD|ALL|AS|ASC|ASK|BNODE|BY|CLEAR|CONSTRUCT|COPY|CREATE|DATA|DEFAULT|DELETE|DESC|DESCRIBE|DISTINCT|DROP|EXISTS|FILTER|FROM|GROUP|HAVING|INSERT|INTO|LIMIT|LOAD|MINUS|MOVE|NAMED|NOT|NOW|OFFSET|OPTIONAL|ORDER|RAND|REDUCED|SELECT|SEPARATOR|SERVICE|SILENT|STRUUID|UNION|USING|UUID|VALUES|WHERE)\\b/i, /\\b(?:ABS|AVG|BIND|BOUND|CEIL|COALESCE|CONCAT|CONTAINS|COUNT|DATATYPE|DAY|ENCODE_FOR_URI|FLOOR|GROUP_CONCAT|HOURS|IF|IRI|isBLANK|isIRI|isLITERAL|isNUMERIC|isURI|LANG|LANGMATCHES|LCASE|MAX|MD5|MIN|MINUTES|MONTH|REGEX|REPLACE|ROUND|sameTerm|SAMPLE|SECONDS|SHA1|SHA256|SHA384|SHA512|STR|STRAFTER|STRBEFORE|STRDT|STRENDS|STRLANG|STRLEN|STRSTARTS|SUBSTR|SUM|TIMEZONE|TZ|UCASE|URI|YEAR)\\b(?=\\s*\\()/i, /\\b(?:BASE|GRAPH|PREFIX)\\b/i]\n  });\n  Prism.languages.rq = Prism.languages.sparql;\n}", "map": {"version": 3, "names": ["refractor<PERSON><PERSON><PERSON>", "require", "module", "exports", "sparql", "displayName", "aliases", "Prism", "register", "languages", "extend", "boolean", "variable", "pattern", "greedy", "insertBefore", "keyword", "rq"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/sparql.js"], "sourcesContent": ["'use strict'\nvar refractorTurtle = require('./turtle.js')\nmodule.exports = sparql\nsparql.displayName = 'sparql'\nsparql.aliases = ['rq']\nfunction sparql(Prism) {\n  Prism.register(refractorTurtle)\n  Prism.languages.sparql = Prism.languages.extend('turtle', {\n    boolean: /\\b(?:false|true)\\b/i,\n    variable: {\n      pattern: /[?$]\\w+/,\n      greedy: true\n    }\n  })\n  Prism.languages.insertBefore('sparql', 'punctuation', {\n    keyword: [\n      /\\b(?:A|ADD|ALL|AS|ASC|ASK|BNODE|BY|CLEAR|CONSTRUCT|COPY|CREATE|DATA|DEFAULT|DELETE|DESC|DESCRIBE|DISTINCT|DROP|EXISTS|FILTER|FROM|GROUP|HAVING|INSERT|INTO|LIMIT|LOAD|MINUS|MOVE|NAMED|NOT|NOW|OFFSET|OPTIONAL|ORDER|RAND|REDUCED|SELECT|SEPARATOR|SERVICE|SILENT|STRUUID|UNION|USING|UUID|VALUES|WHERE)\\b/i,\n      /\\b(?:ABS|AVG|BIND|BOUND|CEIL|COALESCE|CONCAT|CONTAINS|COUNT|DATATYPE|DAY|ENCODE_FOR_URI|FLOOR|GROUP_CONCAT|HOURS|IF|IRI|isBLANK|isIRI|isLITERAL|isNUMERIC|isURI|LANG|LANGMATCHES|LCASE|MAX|MD5|MIN|MINUTES|MONTH|REGEX|REPLACE|ROUND|sameTerm|SAMPLE|SECONDS|SHA1|SHA256|SHA384|SHA512|STR|STRAFTER|STRBEFORE|STRDT|STRENDS|STRLANG|STRLEN|STRSTARTS|SUBSTR|SUM|TIMEZONE|TZ|UCASE|URI|YEAR)\\b(?=\\s*\\()/i,\n      /\\b(?:BASE|GRAPH|PREFIX)\\b/i\n    ]\n  })\n  Prism.languages.rq = Prism.languages.sparql\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,aAAa,CAAC;AAC5CC,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,IAAI,CAAC;AACvB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,QAAQ,CAACR,eAAe,CAAC;EAC/BO,KAAK,CAACE,SAAS,CAACL,MAAM,GAAGG,KAAK,CAACE,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAE;IACxDC,OAAO,EAAE,qBAAqB;IAC9BC,QAAQ,EAAE;MACRC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACFP,KAAK,CAACE,SAAS,CAACM,YAAY,CAAC,QAAQ,EAAE,aAAa,EAAE;IACpDC,OAAO,EAAE,CACP,6SAA6S,EAC7S,yYAAyY,EACzY,4BAA4B;EAEhC,CAAC,CAAC;EACFT,KAAK,CAACE,SAAS,CAACQ,EAAE,GAAGV,KAAK,CAACE,SAAS,CAACL,MAAM;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}