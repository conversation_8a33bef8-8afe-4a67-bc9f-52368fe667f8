{"ast": null, "code": "'use strict';\n\nmodule.exports = julia;\njulia.displayName = 'julia';\njulia.aliases = [];\nfunction julia(Prism) {\n  Prism.languages.julia = {\n    comment: {\n      // support one level of nested comments\n      // https://github.com/JuliaLang/julia/pull/6128\n      pattern: /(^|[^\\\\])(?:#=(?:[^#=]|=(?!#)|#(?!=)|#=(?:[^#=]|=(?!#)|#(?!=))*=#)*=#|#.*)/,\n      lookbehind: true\n    },\n    regex: {\n      // https://docs.julialang.org/en/v1/manual/strings/#Regular-Expressions-1\n      pattern: /r\"(?:\\\\.|[^\"\\\\\\r\\n])*\"[imsx]{0,4}/,\n      greedy: true\n    },\n    string: {\n      // https://docs.julialang.org/en/v1/manual/strings/#String-Basics-1\n      // https://docs.julialang.org/en/v1/manual/strings/#non-standard-string-literals-1\n      // https://docs.julialang.org/en/v1/manual/running-external-programs/#Running-External-Programs-1\n      pattern: /\"\"\"[\\s\\S]+?\"\"\"|(?:\\b\\w+)?\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`(?:[^\\\\`\\r\\n]|\\\\.)*`/,\n      greedy: true\n    },\n    char: {\n      // https://docs.julialang.org/en/v1/manual/strings/#man-characters-1\n      pattern: /(^|[^\\w'])'(?:\\\\[^\\r\\n][^'\\r\\n]*|[^\\\\\\r\\n])'/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword: /\\b(?:abstract|baremodule|begin|bitstype|break|catch|ccall|const|continue|do|else|elseif|end|export|finally|for|function|global|if|immutable|import|importall|in|let|local|macro|module|print|println|quote|return|struct|try|type|typealias|using|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /(?:\\b(?=\\d)|\\B(?=\\.))(?:0[box])?(?:[\\da-f]+(?:_[\\da-f]+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[efp][+-]?\\d+(?:_\\d+)*)?j?/i,\n    // https://docs.julialang.org/en/v1/manual/mathematical-operations/\n    // https://docs.julialang.org/en/v1/manual/mathematical-operations/#Operator-Precedence-and-Associativity-1\n    operator: /&&|\\|\\||[-+*^%÷⊻&$\\\\]=?|\\/[\\/=]?|!=?=?|\\|[=>]?|<(?:<=?|[=:|])?|>(?:=|>>?=?)?|==?=?|[~≠≤≥'√∛]/,\n    punctuation: /::?|[{}[\\]();,.?]/,\n    // https://docs.julialang.org/en/v1/base/numbers/#Base.im\n    constant: /\\b(?:(?:Inf|NaN)(?:16|32|64)?|im|pi)\\b|[πℯ]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "julia", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "regex", "greedy", "string", "char", "keyword", "boolean", "number", "operator", "punctuation", "constant"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/julia.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = julia\njulia.displayName = 'julia'\njulia.aliases = []\nfunction julia(Prism) {\n  Prism.languages.julia = {\n    comment: {\n      // support one level of nested comments\n      // https://github.com/JuliaLang/julia/pull/6128\n      pattern:\n        /(^|[^\\\\])(?:#=(?:[^#=]|=(?!#)|#(?!=)|#=(?:[^#=]|=(?!#)|#(?!=))*=#)*=#|#.*)/,\n      lookbehind: true\n    },\n    regex: {\n      // https://docs.julialang.org/en/v1/manual/strings/#Regular-Expressions-1\n      pattern: /r\"(?:\\\\.|[^\"\\\\\\r\\n])*\"[imsx]{0,4}/,\n      greedy: true\n    },\n    string: {\n      // https://docs.julialang.org/en/v1/manual/strings/#String-Basics-1\n      // https://docs.julialang.org/en/v1/manual/strings/#non-standard-string-literals-1\n      // https://docs.julialang.org/en/v1/manual/running-external-programs/#Running-External-Programs-1\n      pattern:\n        /\"\"\"[\\s\\S]+?\"\"\"|(?:\\b\\w+)?\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`(?:[^\\\\`\\r\\n]|\\\\.)*`/,\n      greedy: true\n    },\n    char: {\n      // https://docs.julialang.org/en/v1/manual/strings/#man-characters-1\n      pattern: /(^|[^\\w'])'(?:\\\\[^\\r\\n][^'\\r\\n]*|[^\\\\\\r\\n])'/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:abstract|baremodule|begin|bitstype|break|catch|ccall|const|continue|do|else|elseif|end|export|finally|for|function|global|if|immutable|import|importall|in|let|local|macro|module|print|println|quote|return|struct|try|type|typealias|using|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    number:\n      /(?:\\b(?=\\d)|\\B(?=\\.))(?:0[box])?(?:[\\da-f]+(?:_[\\da-f]+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\.\\d+(?:_\\d+)*)(?:[efp][+-]?\\d+(?:_\\d+)*)?j?/i,\n    // https://docs.julialang.org/en/v1/manual/mathematical-operations/\n    // https://docs.julialang.org/en/v1/manual/mathematical-operations/#Operator-Precedence-and-Associativity-1\n    operator:\n      /&&|\\|\\||[-+*^%÷⊻&$\\\\]=?|\\/[\\/=]?|!=?=?|\\|[=>]?|<(?:<=?|[=:|])?|>(?:=|>>?=?)?|==?=?|[~≠≤≥'√∛]/,\n    punctuation: /::?|[{}[\\]();,.?]/,\n    // https://docs.julialang.org/en/v1/base/numbers/#Base.im\n    constant: /\\b(?:(?:Inf|NaN)(?:16|32|64)?|im|pi)\\b|[πℯ]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,KAAK;AACtBA,KAAK,CAACC,WAAW,GAAG,OAAO;AAC3BD,KAAK,CAACE,OAAO,GAAG,EAAE;AAClB,SAASF,KAAKA,CAACG,KAAK,EAAE;EACpBA,KAAK,CAACC,SAAS,CAACJ,KAAK,GAAG;IACtBK,OAAO,EAAE;MACP;MACA;MACAC,OAAO,EACL,4EAA4E;MAC9EC,UAAU,EAAE;IACd,CAAC;IACDC,KAAK,EAAE;MACL;MACAF,OAAO,EAAE,mCAAmC;MAC5CG,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACN;MACA;MACA;MACAJ,OAAO,EACL,sEAAsE;MACxEG,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJ;MACAL,OAAO,EAAE,8CAA8C;MACvDC,UAAU,EAAE,IAAI;MAChBE,MAAM,EAAE;IACV,CAAC;IACDG,OAAO,EACL,4PAA4P;IAC9PC,OAAO,EAAE,oBAAoB;IAC7BC,MAAM,EACJ,iIAAiI;IACnI;IACA;IACAC,QAAQ,EACN,8FAA8F;IAChGC,WAAW,EAAE,mBAAmB;IAChC;IACAC,QAAQ,EAAE;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}