{"ast": null, "code": "/*\nLanguage: Go\nAuthor: <PERSON> aka StepLg <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Google go language (golang). For info about language\nWebsite: http://golang.org/\nCategory: common, system\n*/\n\nfunction go(hljs) {\n  const GO_KEYWORDS = {\n    keyword: 'break default func interface select case map struct chan else goto package switch ' + 'const fallthrough if range type continue for import return var go defer ' + 'bool byte complex64 complex128 float32 float64 int8 int16 int32 int64 string uint8 ' + 'uint16 uint32 uint64 int uint uintptr rune',\n    literal: 'true false iota nil',\n    built_in: 'append cap close complex copy imag len make new panic print println real recover delete'\n  };\n  return {\n    name: 'Go',\n    aliases: ['golang'],\n    keywords: GO_KEYWORDS,\n    illegal: '</',\n    contains: [hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'string',\n      variants: [hljs.QUOTE_STRING_MODE, hljs.APOS_STRING_MODE, {\n        begin: '`',\n        end: '`'\n      }]\n    }, {\n      className: 'number',\n      variants: [{\n        begin: hljs.C_NUMBER_RE + '[i]',\n        relevance: 1\n      }, hljs.C_NUMBER_MODE]\n    }, {\n      begin: /:=/ // relevance booster\n    }, {\n      className: 'function',\n      beginKeywords: 'func',\n      end: '\\\\s*(\\\\{|$)',\n      excludeEnd: true,\n      contains: [hljs.TITLE_MODE, {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: GO_KEYWORDS,\n        illegal: /[\"']/\n      }]\n    }]\n  };\n}\nmodule.exports = go;", "map": {"version": 3, "names": ["go", "hljs", "GO_KEYWORDS", "keyword", "literal", "built_in", "name", "aliases", "keywords", "illegal", "contains", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "className", "variants", "QUOTE_STRING_MODE", "APOS_STRING_MODE", "begin", "end", "C_NUMBER_RE", "relevance", "C_NUMBER_MODE", "beginKeywords", "excludeEnd", "TITLE_MODE", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/go.js"], "sourcesContent": ["/*\nLanguage: Go\nAuthor: <PERSON> aka StepLg <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Google go language (golang). For info about language\nWebsite: http://golang.org/\nCategory: common, system\n*/\n\nfunction go(hljs) {\n  const GO_KEYWORDS = {\n    keyword:\n      'break default func interface select case map struct chan else goto package switch ' +\n      'const fallthrough if range type continue for import return var go defer ' +\n      'bool byte complex64 complex128 float32 float64 int8 int16 int32 int64 string uint8 ' +\n      'uint16 uint32 uint64 int uint uintptr rune',\n    literal:\n       'true false iota nil',\n    built_in:\n      'append cap close complex copy imag len make new panic print println real recover delete'\n  };\n  return {\n    name: 'Go',\n    aliases: ['golang'],\n    keywords: GO_KEYWORDS,\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'string',\n        variants: [\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          {\n            begin: '`',\n            end: '`'\n          }\n        ]\n      },\n      {\n        className: 'number',\n        variants: [\n          {\n            begin: hljs.C_NUMBER_RE + '[i]',\n            relevance: 1\n          },\n          hljs.C_NUMBER_MODE\n        ]\n      },\n      {\n        begin: /:=/ // relevance booster\n      },\n      {\n        className: 'function',\n        beginKeywords: 'func',\n        end: '\\\\s*(\\\\{|$)',\n        excludeEnd: true,\n        contains: [\n          hljs.TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: GO_KEYWORDS,\n            illegal: /[\"']/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = go;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,EAAEA,CAACC,IAAI,EAAE;EAChB,MAAMC,WAAW,GAAG;IAClBC,OAAO,EACL,oFAAoF,GACpF,0EAA0E,GAC1E,qFAAqF,GACrF,4CAA4C;IAC9CC,OAAO,EACJ,qBAAqB;IACxBC,QAAQ,EACN;EACJ,CAAC;EACD,OAAO;IACLC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,CAAC,QAAQ,CAAC;IACnBC,QAAQ,EAAEN,WAAW;IACrBO,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRT,IAAI,CAACU,mBAAmB,EACxBV,IAAI,CAACW,oBAAoB,EACzB;MACEC,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,CACRb,IAAI,CAACc,iBAAiB,EACtBd,IAAI,CAACe,gBAAgB,EACrB;QACEC,KAAK,EAAE,GAAG;QACVC,GAAG,EAAE;MACP,CAAC;IAEL,CAAC,EACD;MACEL,SAAS,EAAE,QAAQ;MACnBC,QAAQ,EAAE,CACR;QACEG,KAAK,EAAEhB,IAAI,CAACkB,WAAW,GAAG,KAAK;QAC/BC,SAAS,EAAE;MACb,CAAC,EACDnB,IAAI,CAACoB,aAAa;IAEtB,CAAC,EACD;MACEJ,KAAK,EAAE,IAAI,CAAC;IACd,CAAC,EACD;MACEJ,SAAS,EAAE,UAAU;MACrBS,aAAa,EAAE,MAAM;MACrBJ,GAAG,EAAE,aAAa;MAClBK,UAAU,EAAE,IAAI;MAChBb,QAAQ,EAAE,CACRT,IAAI,CAACuB,UAAU,EACf;QACEX,SAAS,EAAE,QAAQ;QACnBI,KAAK,EAAE,IAAI;QACXC,GAAG,EAAE,IAAI;QACTV,QAAQ,EAAEN,WAAW;QACrBO,OAAO,EAAE;MACX,CAAC;IAEL,CAAC;EAEL,CAAC;AACH;AAEAgB,MAAM,CAACC,OAAO,GAAG1B,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}