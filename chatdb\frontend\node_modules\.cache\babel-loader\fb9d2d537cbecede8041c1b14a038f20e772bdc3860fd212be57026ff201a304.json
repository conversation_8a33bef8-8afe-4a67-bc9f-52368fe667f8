{"ast": null, "code": "import formatDistance from \"../en-US/_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"../en-US/_lib/formatRelative/index.js\";\nimport localize from \"../en-US/_lib/localize/index.js\";\nimport match from \"../en-US/_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary English locale (Australia).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@Julien<PERSON>alige]{@link https://github.com/JulienMalige}\n */\nvar locale = {\n  code: 'en-AU',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "locale", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/date-fns/esm/locale/en-AU/index.js"], "sourcesContent": ["import formatDistance from \"../en-US/_lib/formatDistance/index.js\";\nimport formatLong from \"./_lib/formatLong/index.js\";\nimport formatRelative from \"../en-US/_lib/formatRelative/index.js\";\nimport localize from \"../en-US/_lib/localize/index.js\";\nimport match from \"../en-US/_lib/match/index.js\";\n/**\n * @type {Locale}\n * @category Locales\n * @summary English locale (Australia).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@Julien<PERSON>alige]{@link https://github.com/JulienMalige}\n */\nvar locale = {\n  code: 'en-AU',\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\nexport default locale;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,uCAAuC;AAClE,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,QAAQ,MAAM,iCAAiC;AACtD,OAAOC,KAAK,MAAM,8BAA8B;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;AACD,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}