{"ast": null, "code": "\"use client\";\n\nimport generateRangePicker from './generateRangePicker';\nimport generateSinglePicker from './generateSinglePicker';\nconst generatePicker = generateConfig => {\n  // =========================== Picker ===========================\n  const {\n    DatePicker,\n    WeekPicker,\n    MonthPicker,\n    YearPicker,\n    TimePicker,\n    QuarterPicker\n  } = generateSinglePicker(generateConfig);\n  // ======================== Range Picker ========================\n  const RangePicker = generateRangePicker(generateConfig);\n  const MergedDatePicker = DatePicker;\n  MergedDatePicker.WeekPicker = WeekPicker;\n  MergedDatePicker.MonthPicker = MonthPicker;\n  MergedDatePicker.YearPicker = YearPicker;\n  MergedDatePicker.RangePicker = RangePicker;\n  MergedDatePicker.TimePicker = TimePicker;\n  MergedDatePicker.QuarterPicker = QuarterPicker;\n  if (process.env.NODE_ENV !== 'production') {\n    MergedDatePicker.displayName = 'DatePicker';\n  }\n  return MergedDatePicker;\n};\nexport default generatePicker;", "map": {"version": 3, "names": ["generateRangePicker", "generateSinglePicker", "generatePicker", "generateConfig", "DatePicker", "WeekPicker", "MonthPicker", "YearPicker", "TimePicker", "QuarterPicker", "RangePicker", "MergedDatePicker", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/date-picker/generatePicker/index.js"], "sourcesContent": ["\"use client\";\n\nimport generateRangePicker from './generateRangePicker';\nimport generateSinglePicker from './generateSinglePicker';\nconst generatePicker = generateConfig => {\n  // =========================== Picker ===========================\n  const {\n    DatePicker,\n    WeekPicker,\n    MonthPicker,\n    YearPicker,\n    TimePicker,\n    QuarterPicker\n  } = generateSinglePicker(generateConfig);\n  // ======================== Range Picker ========================\n  const RangePicker = generateRangePicker(generateConfig);\n  const MergedDatePicker = DatePicker;\n  MergedDatePicker.WeekPicker = WeekPicker;\n  MergedDatePicker.MonthPicker = MonthPicker;\n  MergedDatePicker.YearPicker = YearPicker;\n  MergedDatePicker.RangePicker = RangePicker;\n  MergedDatePicker.TimePicker = TimePicker;\n  MergedDatePicker.QuarterPicker = QuarterPicker;\n  if (process.env.NODE_ENV !== 'production') {\n    MergedDatePicker.displayName = 'DatePicker';\n  }\n  return MergedDatePicker;\n};\nexport default generatePicker;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,MAAMC,cAAc,GAAGC,cAAc,IAAI;EACvC;EACA,MAAM;IACJC,UAAU;IACVC,UAAU;IACVC,WAAW;IACXC,UAAU;IACVC,UAAU;IACVC;EACF,CAAC,GAAGR,oBAAoB,CAACE,cAAc,CAAC;EACxC;EACA,MAAMO,WAAW,GAAGV,mBAAmB,CAACG,cAAc,CAAC;EACvD,MAAMQ,gBAAgB,GAAGP,UAAU;EACnCO,gBAAgB,CAACN,UAAU,GAAGA,UAAU;EACxCM,gBAAgB,CAACL,WAAW,GAAGA,WAAW;EAC1CK,gBAAgB,CAACJ,UAAU,GAAGA,UAAU;EACxCI,gBAAgB,CAACD,WAAW,GAAGA,WAAW;EAC1CC,gBAAgB,CAACH,UAAU,GAAGA,UAAU;EACxCG,gBAAgB,CAACF,aAAa,GAAGA,aAAa;EAC9C,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCH,gBAAgB,CAACI,WAAW,GAAG,YAAY;EAC7C;EACA,OAAOJ,gBAAgB;AACzB,CAAC;AACD,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}