{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst getRTLStyle = _ref => {\n  let {\n    componentCls,\n    menuArrowOffset,\n    calc\n  } = _ref;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-submenu-rtl`]: {\n      transformOrigin: '100% 0'\n    },\n    // Vertical Arrow\n    [`${componentCls}-rtl${componentCls}-vertical,\n    ${componentCls}-submenu-rtl ${componentCls}-vertical`]: {\n      [`${componentCls}-submenu-arrow`]: {\n        '&::before': {\n          transform: `rotate(-45deg) translateY(${unit(calc(menuArrowOffset).mul(-1).equal())})`\n        },\n        '&::after': {\n          transform: `rotate(45deg) translateY(${unit(menuArrowOffset)})`\n        }\n      }\n    }\n  };\n};\nexport default getRTLStyle;", "map": {"version": 3, "names": ["unit", "getRTLStyle", "_ref", "componentCls", "menuArrowOffset", "calc", "direction", "transform<PERSON><PERSON>in", "transform", "mul", "equal"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/menu/style/rtl.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst getRTLStyle = _ref => {\n  let {\n    componentCls,\n    menuArrowOffset,\n    calc\n  } = _ref;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-submenu-rtl`]: {\n      transformOrigin: '100% 0'\n    },\n    // Vertical Arrow\n    [`${componentCls}-rtl${componentCls}-vertical,\n    ${componentCls}-submenu-rtl ${componentCls}-vertical`]: {\n      [`${componentCls}-submenu-arrow`]: {\n        '&::before': {\n          transform: `rotate(-45deg) translateY(${unit(calc(menuArrowOffset).mul(-1).equal())})`\n        },\n        '&::after': {\n          transform: `rotate(45deg) translateY(${unit(menuArrowOffset)})`\n        }\n      }\n    }\n  };\n};\nexport default getRTLStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,WAAW,GAAGC,IAAI,IAAI;EAC1B,IAAI;IACFC,YAAY;IACZC,eAAe;IACfC;EACF,CAAC,GAAGH,IAAI;EACR,OAAO;IACL,CAAC,GAAGC,YAAY,MAAM,GAAG;MACvBG,SAAS,EAAE;IACb,CAAC;IACD,CAAC,GAAGH,YAAY,cAAc,GAAG;MAC/BI,eAAe,EAAE;IACnB,CAAC;IACD;IACA,CAAC,GAAGJ,YAAY,OAAOA,YAAY;AACvC,MAAMA,YAAY,gBAAgBA,YAAY,WAAW,GAAG;MACtD,CAAC,GAAGA,YAAY,gBAAgB,GAAG;QACjC,WAAW,EAAE;UACXK,SAAS,EAAE,6BAA6BR,IAAI,CAACK,IAAI,CAACD,eAAe,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;QACrF,CAAC;QACD,UAAU,EAAE;UACVF,SAAS,EAAE,4BAA4BR,IAAI,CAACI,eAAe,CAAC;QAC9D;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}