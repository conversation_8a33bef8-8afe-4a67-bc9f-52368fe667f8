{"ast": null, "code": "const getMoveTranslate = direction => {\n  const value = '100%';\n  return {\n    left: `translateX(-${value})`,\n    right: `translateX(${value})`,\n    top: `translateY(-${value})`,\n    bottom: `translateY(${value})`\n  }[direction];\n};\nconst getEnterLeaveStyle = (startStyle, endStyle) => ({\n  '&-enter, &-appear': Object.assign(Object.assign({}, startStyle), {\n    '&-active': endStyle\n  }),\n  '&-leave': Object.assign(Object.assign({}, endStyle), {\n    '&-active': startStyle\n  })\n});\nconst getFadeStyle = (from, duration) => Object.assign({\n  '&-enter, &-appear, &-leave': {\n    '&-start': {\n      transition: 'none'\n    },\n    '&-active': {\n      transition: `all ${duration}`\n    }\n  }\n}, getEnterLeaveStyle({\n  opacity: from\n}, {\n  opacity: 1\n}));\nconst getPanelMotionStyles = (direction, duration) => [getFadeStyle(0.7, duration), getEnterLeaveStyle({\n  transform: getMoveTranslate(direction)\n}, {\n  transform: 'none'\n})];\nconst genMotionStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow\n  } = token;\n  return {\n    [componentCls]: {\n      // ======================== Mask ========================\n      [`${componentCls}-mask-motion`]: getFadeStyle(0, motionDurationSlow),\n      // ======================= Panel ========================\n      [`${componentCls}-panel-motion`]: ['left', 'right', 'top', 'bottom'].reduce((obj, direction) => Object.assign(Object.assign({}, obj), {\n        [`&-${direction}`]: getPanelMotionStyles(direction, motionDurationSlow)\n      }), {})\n    }\n  };\n};\nexport default genMotionStyle;", "map": {"version": 3, "names": ["getMoveTranslate", "direction", "value", "left", "right", "top", "bottom", "getEnterLeaveStyle", "startStyle", "endStyle", "Object", "assign", "getFadeStyle", "from", "duration", "transition", "opacity", "getPanelMotionStyles", "transform", "genMotionStyle", "token", "componentCls", "motionDurationSlow", "reduce", "obj"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/drawer/style/motion.js"], "sourcesContent": ["const getMoveTranslate = direction => {\n  const value = '100%';\n  return {\n    left: `translateX(-${value})`,\n    right: `translateX(${value})`,\n    top: `translateY(-${value})`,\n    bottom: `translateY(${value})`\n  }[direction];\n};\nconst getEnterLeaveStyle = (startStyle, endStyle) => ({\n  '&-enter, &-appear': Object.assign(Object.assign({}, startStyle), {\n    '&-active': endStyle\n  }),\n  '&-leave': Object.assign(Object.assign({}, endStyle), {\n    '&-active': startStyle\n  })\n});\nconst getFadeStyle = (from, duration) => Object.assign({\n  '&-enter, &-appear, &-leave': {\n    '&-start': {\n      transition: 'none'\n    },\n    '&-active': {\n      transition: `all ${duration}`\n    }\n  }\n}, getEnterLeaveStyle({\n  opacity: from\n}, {\n  opacity: 1\n}));\nconst getPanelMotionStyles = (direction, duration) => [getFadeStyle(0.7, duration), getEnterLeaveStyle({\n  transform: getMoveTranslate(direction)\n}, {\n  transform: 'none'\n})];\nconst genMotionStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow\n  } = token;\n  return {\n    [componentCls]: {\n      // ======================== Mask ========================\n      [`${componentCls}-mask-motion`]: getFadeStyle(0, motionDurationSlow),\n      // ======================= Panel ========================\n      [`${componentCls}-panel-motion`]: ['left', 'right', 'top', 'bottom'].reduce((obj, direction) => Object.assign(Object.assign({}, obj), {\n        [`&-${direction}`]: getPanelMotionStyles(direction, motionDurationSlow)\n      }), {})\n    }\n  };\n};\nexport default genMotionStyle;"], "mappings": "AAAA,MAAMA,gBAAgB,GAAGC,SAAS,IAAI;EACpC,MAAMC,KAAK,GAAG,MAAM;EACpB,OAAO;IACLC,IAAI,EAAE,eAAeD,KAAK,GAAG;IAC7BE,KAAK,EAAE,cAAcF,KAAK,GAAG;IAC7BG,GAAG,EAAE,eAAeH,KAAK,GAAG;IAC5BI,MAAM,EAAE,cAAcJ,KAAK;EAC7B,CAAC,CAACD,SAAS,CAAC;AACd,CAAC;AACD,MAAMM,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,QAAQ,MAAM;EACpD,mBAAmB,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,UAAU,CAAC,EAAE;IAChE,UAAU,EAAEC;EACd,CAAC,CAAC;EACF,SAAS,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,QAAQ,CAAC,EAAE;IACpD,UAAU,EAAED;EACd,CAAC;AACH,CAAC,CAAC;AACF,MAAMI,YAAY,GAAGA,CAACC,IAAI,EAAEC,QAAQ,KAAKJ,MAAM,CAACC,MAAM,CAAC;EACrD,4BAA4B,EAAE;IAC5B,SAAS,EAAE;MACTI,UAAU,EAAE;IACd,CAAC;IACD,UAAU,EAAE;MACVA,UAAU,EAAE,OAAOD,QAAQ;IAC7B;EACF;AACF,CAAC,EAAEP,kBAAkB,CAAC;EACpBS,OAAO,EAAEH;AACX,CAAC,EAAE;EACDG,OAAO,EAAE;AACX,CAAC,CAAC,CAAC;AACH,MAAMC,oBAAoB,GAAGA,CAAChB,SAAS,EAAEa,QAAQ,KAAK,CAACF,YAAY,CAAC,GAAG,EAAEE,QAAQ,CAAC,EAAEP,kBAAkB,CAAC;EACrGW,SAAS,EAAElB,gBAAgB,CAACC,SAAS;AACvC,CAAC,EAAE;EACDiB,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AACH,MAAMC,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACd;MACA,CAAC,GAAGA,YAAY,cAAc,GAAGT,YAAY,CAAC,CAAC,EAAEU,kBAAkB,CAAC;MACpE;MACA,CAAC,GAAGD,YAAY,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEvB,SAAS,KAAKS,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEa,GAAG,CAAC,EAAE;QACpI,CAAC,KAAKvB,SAAS,EAAE,GAAGgB,oBAAoB,CAAChB,SAAS,EAAEqB,kBAAkB;MACxE,CAAC,CAAC,EAAE,CAAC,CAAC;IACR;EACF,CAAC;AACH,CAAC;AACD,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}