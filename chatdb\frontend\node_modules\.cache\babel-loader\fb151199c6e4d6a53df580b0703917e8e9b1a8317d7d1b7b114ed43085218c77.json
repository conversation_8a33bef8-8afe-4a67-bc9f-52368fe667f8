{"ast": null, "code": "/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Reference} Reference\n * @typedef {import('mdast').Root} Root\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Reference>} References\n */\n\n// To do: next major: always return array.\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {References} node\n *   Reference node (image, link).\n * @returns {ElementContent | Array<ElementContent>}\n *   hast content.\n */\nexport function revert(state, node) {\n  const subtype = node.referenceType;\n  let suffix = ']';\n  if (subtype === 'collapsed') {\n    suffix += '[]';\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']';\n  }\n  if (node.type === 'imageReference') {\n    return {\n      type: 'text',\n      value: '![' + node.alt + suffix\n    };\n  }\n  const contents = state.all(node);\n  const head = contents[0];\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value;\n  } else {\n    contents.unshift({\n      type: 'text',\n      value: '['\n    });\n  }\n  const tail = contents[contents.length - 1];\n  if (tail && tail.type === 'text') {\n    tail.value += suffix;\n  } else {\n    contents.push({\n      type: 'text',\n      value: suffix\n    });\n  }\n  return contents;\n}", "map": {"version": 3, "names": ["revert", "state", "node", "subtype", "referenceType", "suffix", "label", "identifier", "type", "value", "alt", "contents", "all", "head", "unshift", "tail", "length", "push"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/mdast-util-to-hast/lib/revert.js"], "sourcesContent": ["/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Reference} Reference\n * @typedef {import('mdast').Root} Root\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @typedef {Root | Content} Nodes\n * @typedef {Extract<Nodes, Reference>} References\n */\n\n// To do: next major: always return array.\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {References} node\n *   Reference node (image, link).\n * @returns {ElementContent | Array<ElementContent>}\n *   hast content.\n */\nexport function revert(state, node) {\n  const subtype = node.referenceType\n  let suffix = ']'\n\n  if (subtype === 'collapsed') {\n    suffix += '[]'\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']'\n  }\n\n  if (node.type === 'imageReference') {\n    return {type: 'text', value: '![' + node.alt + suffix}\n  }\n\n  const contents = state.all(node)\n  const head = contents[0]\n\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value\n  } else {\n    contents.unshift({type: 'text', value: '['})\n  }\n\n  const tail = contents[contents.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += suffix\n  } else {\n    contents.push({type: 'text', value: suffix})\n  }\n\n  return contents\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,MAAMA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAClC,MAAMC,OAAO,GAAGD,IAAI,CAACE,aAAa;EAClC,IAAIC,MAAM,GAAG,GAAG;EAEhB,IAAIF,OAAO,KAAK,WAAW,EAAE;IAC3BE,MAAM,IAAI,IAAI;EAChB,CAAC,MAAM,IAAIF,OAAO,KAAK,MAAM,EAAE;IAC7BE,MAAM,IAAI,GAAG,IAAIH,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACK,UAAU,CAAC,GAAG,GAAG;EACvD;EAEA,IAAIL,IAAI,CAACM,IAAI,KAAK,gBAAgB,EAAE;IAClC,OAAO;MAACA,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,IAAI,GAAGP,IAAI,CAACQ,GAAG,GAAGL;IAAM,CAAC;EACxD;EAEA,MAAMM,QAAQ,GAAGV,KAAK,CAACW,GAAG,CAACV,IAAI,CAAC;EAChC,MAAMW,IAAI,GAAGF,QAAQ,CAAC,CAAC,CAAC;EAExB,IAAIE,IAAI,IAAIA,IAAI,CAACL,IAAI,KAAK,MAAM,EAAE;IAChCK,IAAI,CAACJ,KAAK,GAAG,GAAG,GAAGI,IAAI,CAACJ,KAAK;EAC/B,CAAC,MAAM;IACLE,QAAQ,CAACG,OAAO,CAAC;MAACN,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;EAC9C;EAEA,MAAMM,IAAI,GAAGJ,QAAQ,CAACA,QAAQ,CAACK,MAAM,GAAG,CAAC,CAAC;EAE1C,IAAID,IAAI,IAAIA,IAAI,CAACP,IAAI,KAAK,MAAM,EAAE;IAChCO,IAAI,CAACN,KAAK,IAAIJ,MAAM;EACtB,CAAC,MAAM;IACLM,QAAQ,CAACM,IAAI,CAAC;MAACT,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAEJ;IAAM,CAAC,CAAC;EAC9C;EAEA,OAAOM,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}