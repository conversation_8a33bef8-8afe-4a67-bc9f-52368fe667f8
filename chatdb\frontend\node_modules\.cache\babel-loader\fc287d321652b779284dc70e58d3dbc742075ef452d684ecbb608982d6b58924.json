{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\n/**\n * @private Internal usage only\n * see: https://developer.mozilla.org/en-US/docs/Web/CSS/gradient/conic-gradient#checkerboard\n */\nexport const getTransBg = (size, colorFill) => ({\n  backgroundImage: `conic-gradient(${colorFill} 25%, transparent 25% 50%, ${colorFill} 50% 75%, transparent 75% 100%)`,\n  backgroundSize: `${size} ${size}`\n});\nconst genColorBlockStyle = (token, size) => {\n  const {\n    componentCls,\n    borderRadiusSM,\n    colorPickerInsetShadow,\n    lineWidth,\n    colorFillSecondary\n  } = token;\n  return {\n    [`${componentCls}-color-block`]: Object.assign(Object.assign({\n      position: 'relative',\n      borderRadius: borderRadiusSM,\n      width: size,\n      height: size,\n      boxShadow: colorPickerInsetShadow,\n      flex: 'none'\n    }, getTransBg('50%', token.colorFillSecondary)), {\n      [`${componentCls}-color-block-inner`]: {\n        width: '100%',\n        height: '100%',\n        boxShadow: `inset 0 0 0 ${unit(lineWidth)} ${colorFillSecondary}`,\n        borderRadius: 'inherit'\n      }\n    })\n  };\n};\nexport default genColorBlockStyle;", "map": {"version": 3, "names": ["unit", "getTransBg", "size", "colorFill", "backgroundImage", "backgroundSize", "genColorBlockStyle", "token", "componentCls", "borderRadiusSM", "colorPickerInsetShadow", "lineWidth", "colorFillSecondary", "Object", "assign", "position", "borderRadius", "width", "height", "boxShadow", "flex"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/color-picker/style/color-block.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\n/**\n * @private Internal usage only\n * see: https://developer.mozilla.org/en-US/docs/Web/CSS/gradient/conic-gradient#checkerboard\n */\nexport const getTransBg = (size, colorFill) => ({\n  backgroundImage: `conic-gradient(${colorFill} 25%, transparent 25% 50%, ${colorFill} 50% 75%, transparent 75% 100%)`,\n  backgroundSize: `${size} ${size}`\n});\nconst genColorBlockStyle = (token, size) => {\n  const {\n    componentCls,\n    borderRadiusSM,\n    colorPickerInsetShadow,\n    lineWidth,\n    colorFillSecondary\n  } = token;\n  return {\n    [`${componentCls}-color-block`]: Object.assign(Object.assign({\n      position: 'relative',\n      borderRadius: borderRadiusSM,\n      width: size,\n      height: size,\n      boxShadow: colorPickerInsetShadow,\n      flex: 'none'\n    }, getTransBg('50%', token.colorFillSecondary)), {\n      [`${componentCls}-color-block-inner`]: {\n        width: '100%',\n        height: '100%',\n        boxShadow: `inset 0 0 0 ${unit(lineWidth)} ${colorFillSecondary}`,\n        borderRadius: 'inherit'\n      }\n    })\n  };\n};\nexport default genColorBlockStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAGA,CAACC,IAAI,EAAEC,SAAS,MAAM;EAC9CC,eAAe,EAAE,kBAAkBD,SAAS,8BAA8BA,SAAS,iCAAiC;EACpHE,cAAc,EAAE,GAAGH,IAAI,IAAIA,IAAI;AACjC,CAAC,CAAC;AACF,MAAMI,kBAAkB,GAAGA,CAACC,KAAK,EAAEL,IAAI,KAAK;EAC1C,MAAM;IACJM,YAAY;IACZC,cAAc;IACdC,sBAAsB;IACtBC,SAAS;IACTC;EACF,CAAC,GAAGL,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,cAAc,GAAGK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAC3DC,QAAQ,EAAE,UAAU;MACpBC,YAAY,EAAEP,cAAc;MAC5BQ,KAAK,EAAEf,IAAI;MACXgB,MAAM,EAAEhB,IAAI;MACZiB,SAAS,EAAET,sBAAsB;MACjCU,IAAI,EAAE;IACR,CAAC,EAAEnB,UAAU,CAAC,KAAK,EAAEM,KAAK,CAACK,kBAAkB,CAAC,CAAC,EAAE;MAC/C,CAAC,GAAGJ,YAAY,oBAAoB,GAAG;QACrCS,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,eAAenB,IAAI,CAACW,SAAS,CAAC,IAAIC,kBAAkB,EAAE;QACjEI,YAAY,EAAE;MAChB;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,eAAeV,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}