{"ast": null, "code": "import * as React from 'react';\nvar SliderContext = /*#__PURE__*/React.createContext({\n  min: 0,\n  max: 0,\n  direction: 'ltr',\n  step: 1,\n  includedStart: 0,\n  includedEnd: 0,\n  tabIndex: 0,\n  keyboard: true,\n  styles: {},\n  classNames: {}\n});\nexport default SliderContext;\n/** @private NOT PROMISE AVAILABLE. DO NOT USE IN PRODUCTION. */\nexport var UnstableContext = /*#__PURE__*/React.createContext({});", "map": {"version": 3, "names": ["React", "SliderContext", "createContext", "min", "max", "direction", "step", "includedStart", "includedEnd", "tabIndex", "keyboard", "styles", "classNames", "UnstableContext"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/rc-slider/es/context.js"], "sourcesContent": ["import * as React from 'react';\nvar SliderContext = /*#__PURE__*/React.createContext({\n  min: 0,\n  max: 0,\n  direction: 'ltr',\n  step: 1,\n  includedStart: 0,\n  includedEnd: 0,\n  tabIndex: 0,\n  keyboard: true,\n  styles: {},\n  classNames: {}\n});\nexport default SliderContext;\n/** @private NOT PROMISE AVAILABLE. DO NOT USE IN PRODUCTION. */\nexport var UnstableContext = /*#__PURE__*/React.createContext({});"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC;EACnDC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,SAAS,EAAE,KAAK;EAChBC,IAAI,EAAE,CAAC;EACPC,aAAa,EAAE,CAAC;EAChBC,WAAW,EAAE,CAAC;EACdC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,CAAC,CAAC;EACVC,UAAU,EAAE,CAAC;AACf,CAAC,CAAC;AACF,eAAeX,aAAa;AAC5B;AACA,OAAO,IAAIY,eAAe,GAAG,aAAab,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}