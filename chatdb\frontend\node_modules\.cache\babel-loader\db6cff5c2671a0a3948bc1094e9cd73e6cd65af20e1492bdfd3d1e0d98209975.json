{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { DEPRECATED_TOKENS, prepareComponentToken } from '.';\nimport { genStyleHooks } from '../../theme/internal';\nconst genSiderStyle = token => {\n  const {\n    componentCls,\n    siderBg,\n    motionDurationMid,\n    motionDurationSlow,\n    antCls,\n    triggerHeight,\n    triggerColor,\n    triggerBg,\n    headerHeight,\n    zeroTriggerWidth,\n    zeroTriggerHeight,\n    borderRadiusLG,\n    lightSiderBg,\n    lightTriggerColor,\n    lightTriggerBg,\n    bodyBg\n  } = token;\n  return {\n    [componentCls]: {\n      position: 'relative',\n      // fix firefox can't set width smaller than content on flex item\n      minWidth: 0,\n      background: siderBg,\n      transition: `all ${motionDurationMid}, background 0s`,\n      '&-has-trigger': {\n        paddingBottom: triggerHeight\n      },\n      '&-right': {\n        order: 1\n      },\n      [`${componentCls}-children`]: {\n        height: '100%',\n        // Hack for fixing margin collapse bug\n        // https://github.com/ant-design/ant-design/issues/7967\n        // solution from https://stackoverflow.com/a/33132624/3040605\n        marginTop: -0.1,\n        paddingTop: 0.1,\n        [`${antCls}-menu${antCls}-menu-inline-collapsed`]: {\n          width: 'auto'\n        }\n      },\n      [`&-zero-width ${componentCls}-children`]: {\n        overflow: 'hidden'\n      },\n      [`${componentCls}-trigger`]: {\n        position: 'fixed',\n        bottom: 0,\n        zIndex: 1,\n        height: triggerHeight,\n        color: triggerColor,\n        lineHeight: unit(triggerHeight),\n        textAlign: 'center',\n        background: triggerBg,\n        cursor: 'pointer',\n        transition: `all ${motionDurationMid}`\n      },\n      [`${componentCls}-zero-width-trigger`]: {\n        position: 'absolute',\n        top: headerHeight,\n        insetInlineEnd: token.calc(zeroTriggerWidth).mul(-1).equal(),\n        zIndex: 1,\n        width: zeroTriggerWidth,\n        height: zeroTriggerHeight,\n        color: triggerColor,\n        fontSize: token.fontSizeXL,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        background: siderBg,\n        borderRadius: `0 ${unit(borderRadiusLG)} ${unit(borderRadiusLG)} 0`,\n        cursor: 'pointer',\n        transition: `background ${motionDurationSlow} ease`,\n        '&::after': {\n          position: 'absolute',\n          inset: 0,\n          background: 'transparent',\n          transition: `all ${motionDurationSlow}`,\n          content: '\"\"'\n        },\n        '&:hover::after': {\n          background: `rgba(255, 255, 255, 0.2)`\n        },\n        '&-right': {\n          insetInlineStart: token.calc(zeroTriggerWidth).mul(-1).equal(),\n          borderRadius: `${unit(borderRadiusLG)} 0 0 ${unit(borderRadiusLG)}`\n        }\n      },\n      // Light\n      '&-light': {\n        background: lightSiderBg,\n        [`${componentCls}-trigger`]: {\n          color: lightTriggerColor,\n          background: lightTriggerBg\n        },\n        [`${componentCls}-zero-width-trigger`]: {\n          color: lightTriggerColor,\n          background: lightTriggerBg,\n          border: `1px solid ${bodyBg}`,\n          // Safe to modify to any other color\n          borderInlineStart: 0\n        }\n      }\n    }\n  };\n};\nexport default genStyleHooks(['Layout', 'Sider'], token => [genSiderStyle(token)], prepareComponentToken, {\n  deprecatedTokens: DEPRECATED_TOKENS\n});", "map": {"version": 3, "names": ["unit", "DEPRECATED_TOKENS", "prepareComponentToken", "genStyleHooks", "genSiderStyle", "token", "componentCls", "siderBg", "motionDurationMid", "motionDurationSlow", "antCls", "triggerHeight", "triggerColor", "triggerBg", "headerHeight", "zeroTriggerWidth", "zeroTriggerHeight", "borderRadiusLG", "lightSiderBg", "lightTriggerColor", "lightTriggerBg", "bodyBg", "position", "min<PERSON><PERSON><PERSON>", "background", "transition", "paddingBottom", "order", "height", "marginTop", "paddingTop", "width", "overflow", "bottom", "zIndex", "color", "lineHeight", "textAlign", "cursor", "top", "insetInlineEnd", "calc", "mul", "equal", "fontSize", "fontSizeXL", "display", "alignItems", "justifyContent", "borderRadius", "inset", "content", "insetInlineStart", "border", "borderInlineStart", "deprecatedTokens"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/layout/style/sider.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { DEPRECATED_TOKENS, prepareComponentToken } from '.';\nimport { genStyleHooks } from '../../theme/internal';\nconst genSiderStyle = token => {\n  const {\n    componentCls,\n    siderBg,\n    motionDurationMid,\n    motionDurationSlow,\n    antCls,\n    triggerHeight,\n    triggerColor,\n    triggerBg,\n    headerHeight,\n    zeroTriggerWidth,\n    zeroTriggerHeight,\n    borderRadiusLG,\n    lightSiderBg,\n    lightTriggerColor,\n    lightTriggerBg,\n    bodyBg\n  } = token;\n  return {\n    [componentCls]: {\n      position: 'relative',\n      // fix firefox can't set width smaller than content on flex item\n      minWidth: 0,\n      background: siderBg,\n      transition: `all ${motionDurationMid}, background 0s`,\n      '&-has-trigger': {\n        paddingBottom: triggerHeight\n      },\n      '&-right': {\n        order: 1\n      },\n      [`${componentCls}-children`]: {\n        height: '100%',\n        // Hack for fixing margin collapse bug\n        // https://github.com/ant-design/ant-design/issues/7967\n        // solution from https://stackoverflow.com/a/33132624/3040605\n        marginTop: -0.1,\n        paddingTop: 0.1,\n        [`${antCls}-menu${antCls}-menu-inline-collapsed`]: {\n          width: 'auto'\n        }\n      },\n      [`&-zero-width ${componentCls}-children`]: {\n        overflow: 'hidden'\n      },\n      [`${componentCls}-trigger`]: {\n        position: 'fixed',\n        bottom: 0,\n        zIndex: 1,\n        height: triggerHeight,\n        color: triggerColor,\n        lineHeight: unit(triggerHeight),\n        textAlign: 'center',\n        background: triggerBg,\n        cursor: 'pointer',\n        transition: `all ${motionDurationMid}`\n      },\n      [`${componentCls}-zero-width-trigger`]: {\n        position: 'absolute',\n        top: headerHeight,\n        insetInlineEnd: token.calc(zeroTriggerWidth).mul(-1).equal(),\n        zIndex: 1,\n        width: zeroTriggerWidth,\n        height: zeroTriggerHeight,\n        color: triggerColor,\n        fontSize: token.fontSizeXL,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        background: siderBg,\n        borderRadius: `0 ${unit(borderRadiusLG)} ${unit(borderRadiusLG)} 0`,\n        cursor: 'pointer',\n        transition: `background ${motionDurationSlow} ease`,\n        '&::after': {\n          position: 'absolute',\n          inset: 0,\n          background: 'transparent',\n          transition: `all ${motionDurationSlow}`,\n          content: '\"\"'\n        },\n        '&:hover::after': {\n          background: `rgba(255, 255, 255, 0.2)`\n        },\n        '&-right': {\n          insetInlineStart: token.calc(zeroTriggerWidth).mul(-1).equal(),\n          borderRadius: `${unit(borderRadiusLG)} 0 0 ${unit(borderRadiusLG)}`\n        }\n      },\n      // Light\n      '&-light': {\n        background: lightSiderBg,\n        [`${componentCls}-trigger`]: {\n          color: lightTriggerColor,\n          background: lightTriggerBg\n        },\n        [`${componentCls}-zero-width-trigger`]: {\n          color: lightTriggerColor,\n          background: lightTriggerBg,\n          border: `1px solid ${bodyBg}`,\n          // Safe to modify to any other color\n          borderInlineStart: 0\n        }\n      }\n    }\n  };\n};\nexport default genStyleHooks(['Layout', 'Sider'], token => [genSiderStyle(token)], prepareComponentToken, {\n  deprecatedTokens: DEPRECATED_TOKENS\n});"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,iBAAiB,EAAEC,qBAAqB,QAAQ,GAAG;AAC5D,SAASC,aAAa,QAAQ,sBAAsB;AACpD,MAAMC,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,YAAY;IACZC,OAAO;IACPC,iBAAiB;IACjBC,kBAAkB;IAClBC,MAAM;IACNC,aAAa;IACbC,YAAY;IACZC,SAAS;IACTC,YAAY;IACZC,gBAAgB;IAChBC,iBAAiB;IACjBC,cAAc;IACdC,YAAY;IACZC,iBAAiB;IACjBC,cAAc;IACdC;EACF,CAAC,GAAGhB,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACdgB,QAAQ,EAAE,UAAU;MACpB;MACAC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAEjB,OAAO;MACnBkB,UAAU,EAAE,OAAOjB,iBAAiB,iBAAiB;MACrD,eAAe,EAAE;QACfkB,aAAa,EAAEf;MACjB,CAAC;MACD,SAAS,EAAE;QACTgB,KAAK,EAAE;MACT,CAAC;MACD,CAAC,GAAGrB,YAAY,WAAW,GAAG;QAC5BsB,MAAM,EAAE,MAAM;QACd;QACA;QACA;QACAC,SAAS,EAAE,CAAC,GAAG;QACfC,UAAU,EAAE,GAAG;QACf,CAAC,GAAGpB,MAAM,QAAQA,MAAM,wBAAwB,GAAG;UACjDqB,KAAK,EAAE;QACT;MACF,CAAC;MACD,CAAC,gBAAgBzB,YAAY,WAAW,GAAG;QACzC0B,QAAQ,EAAE;MACZ,CAAC;MACD,CAAC,GAAG1B,YAAY,UAAU,GAAG;QAC3BgB,QAAQ,EAAE,OAAO;QACjBW,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACTN,MAAM,EAAEjB,aAAa;QACrBwB,KAAK,EAAEvB,YAAY;QACnBwB,UAAU,EAAEpC,IAAI,CAACW,aAAa,CAAC;QAC/B0B,SAAS,EAAE,QAAQ;QACnBb,UAAU,EAAEX,SAAS;QACrByB,MAAM,EAAE,SAAS;QACjBb,UAAU,EAAE,OAAOjB,iBAAiB;MACtC,CAAC;MACD,CAAC,GAAGF,YAAY,qBAAqB,GAAG;QACtCgB,QAAQ,EAAE,UAAU;QACpBiB,GAAG,EAAEzB,YAAY;QACjB0B,cAAc,EAAEnC,KAAK,CAACoC,IAAI,CAAC1B,gBAAgB,CAAC,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QAC5DT,MAAM,EAAE,CAAC;QACTH,KAAK,EAAEhB,gBAAgB;QACvBa,MAAM,EAAEZ,iBAAiB;QACzBmB,KAAK,EAAEvB,YAAY;QACnBgC,QAAQ,EAAEvC,KAAK,CAACwC,UAAU;QAC1BC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBxB,UAAU,EAAEjB,OAAO;QACnB0C,YAAY,EAAE,KAAKjD,IAAI,CAACiB,cAAc,CAAC,IAAIjB,IAAI,CAACiB,cAAc,CAAC,IAAI;QACnEqB,MAAM,EAAE,SAAS;QACjBb,UAAU,EAAE,cAAchB,kBAAkB,OAAO;QACnD,UAAU,EAAE;UACVa,QAAQ,EAAE,UAAU;UACpB4B,KAAK,EAAE,CAAC;UACR1B,UAAU,EAAE,aAAa;UACzBC,UAAU,EAAE,OAAOhB,kBAAkB,EAAE;UACvC0C,OAAO,EAAE;QACX,CAAC;QACD,gBAAgB,EAAE;UAChB3B,UAAU,EAAE;QACd,CAAC;QACD,SAAS,EAAE;UACT4B,gBAAgB,EAAE/C,KAAK,CAACoC,IAAI,CAAC1B,gBAAgB,CAAC,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;UAC9DM,YAAY,EAAE,GAAGjD,IAAI,CAACiB,cAAc,CAAC,QAAQjB,IAAI,CAACiB,cAAc,CAAC;QACnE;MACF,CAAC;MACD;MACA,SAAS,EAAE;QACTO,UAAU,EAAEN,YAAY;QACxB,CAAC,GAAGZ,YAAY,UAAU,GAAG;UAC3B6B,KAAK,EAAEhB,iBAAiB;UACxBK,UAAU,EAAEJ;QACd,CAAC;QACD,CAAC,GAAGd,YAAY,qBAAqB,GAAG;UACtC6B,KAAK,EAAEhB,iBAAiB;UACxBK,UAAU,EAAEJ,cAAc;UAC1BiC,MAAM,EAAE,aAAahC,MAAM,EAAE;UAC7B;UACAiC,iBAAiB,EAAE;QACrB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAenD,aAAa,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAEE,KAAK,IAAI,CAACD,aAAa,CAACC,KAAK,CAAC,CAAC,EAAEH,qBAAqB,EAAE;EACxGqD,gBAAgB,EAAEtD;AACpB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}