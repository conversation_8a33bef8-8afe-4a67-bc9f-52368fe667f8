{"ast": null, "code": "'use strict';\n\nmodule.exports = nim;\nnim.displayName = 'nim';\nnim.aliases = [];\nfunction nim(Prism) {\n  Prism.languages.nim = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    string: {\n      // Double-quoted strings can be prefixed by an identifier (Generalized raw string literals)\n      pattern: /(?:\\b(?!\\d)(?:\\w|\\\\x[89a-fA-F][0-9a-fA-F])+)?(?:\"\"\"[\\s\\S]*?\"\"\"(?!\")|\"(?:\\\\[\\s\\S]|\"\"|[^\"\\\\])*\")/,\n      greedy: true\n    },\n    char: {\n      // Character literals are handled specifically to prevent issues with numeric type suffixes\n      pattern: /'(?:\\\\(?:\\d+|x[\\da-fA-F]{0,2}|.)|[^'])'/,\n      greedy: true\n    },\n    function: {\n      pattern: /(?:(?!\\d)(?:\\w|\\\\x[89a-fA-F][0-9a-fA-F])+|`[^`\\r\\n]+`)\\*?(?:\\[[^\\]]+\\])?(?=\\s*\\()/,\n      greedy: true,\n      inside: {\n        operator: /\\*$/\n      }\n    },\n    // We don't want to highlight operators (and anything really) inside backticks\n    identifier: {\n      pattern: /`[^`\\r\\n]+`/,\n      greedy: true,\n      inside: {\n        punctuation: /`/\n      }\n    },\n    // The negative look ahead prevents wrong highlighting of the .. operator\n    number: /\\b(?:0[xXoObB][\\da-fA-F_]+|\\d[\\d_]*(?:(?!\\.\\.)\\.[\\d_]*)?(?:[eE][+-]?\\d[\\d_]*)?)(?:'?[iuf]\\d*)?/,\n    keyword: /\\b(?:addr|as|asm|atomic|bind|block|break|case|cast|concept|const|continue|converter|defer|discard|distinct|do|elif|else|end|enum|except|export|finally|for|from|func|generic|if|import|include|interface|iterator|let|macro|method|mixin|nil|object|out|proc|ptr|raise|ref|return|static|template|try|tuple|type|using|var|when|while|with|without|yield)\\b/,\n    operator: {\n      // Look behind and look ahead prevent wrong highlighting of punctuations [. .] {. .} (. .)\n      // but allow the slice operator .. to take precedence over them\n      // One can define his own operators in Nim so all combination of operators might be an operator.\n      pattern: /(^|[({\\[](?=\\.\\.)|(?![({\\[]\\.).)(?:(?:[=+\\-*\\/<>@$~&%|!?^:\\\\]|\\.\\.|\\.(?![)}\\]]))+|\\b(?:and|div|in|is|isnot|mod|not|notin|of|or|shl|shr|xor)\\b)/m,\n      lookbehind: true\n    },\n    punctuation: /[({\\[]\\.|\\.[)}\\]]|[`(){}\\[\\],:]/\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "nim", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "greedy", "string", "char", "function", "inside", "operator", "identifier", "punctuation", "number", "keyword", "lookbehind"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/nim.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nim\nnim.displayName = 'nim'\nnim.aliases = []\nfunction nim(Prism) {\n  Prism.languages.nim = {\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    string: {\n      // Double-quoted strings can be prefixed by an identifier (Generalized raw string literals)\n      pattern:\n        /(?:\\b(?!\\d)(?:\\w|\\\\x[89a-fA-F][0-9a-fA-F])+)?(?:\"\"\"[\\s\\S]*?\"\"\"(?!\")|\"(?:\\\\[\\s\\S]|\"\"|[^\"\\\\])*\")/,\n      greedy: true\n    },\n    char: {\n      // Character literals are handled specifically to prevent issues with numeric type suffixes\n      pattern: /'(?:\\\\(?:\\d+|x[\\da-fA-F]{0,2}|.)|[^'])'/,\n      greedy: true\n    },\n    function: {\n      pattern:\n        /(?:(?!\\d)(?:\\w|\\\\x[89a-fA-F][0-9a-fA-F])+|`[^`\\r\\n]+`)\\*?(?:\\[[^\\]]+\\])?(?=\\s*\\()/,\n      greedy: true,\n      inside: {\n        operator: /\\*$/\n      }\n    },\n    // We don't want to highlight operators (and anything really) inside backticks\n    identifier: {\n      pattern: /`[^`\\r\\n]+`/,\n      greedy: true,\n      inside: {\n        punctuation: /`/\n      }\n    },\n    // The negative look ahead prevents wrong highlighting of the .. operator\n    number:\n      /\\b(?:0[xXoObB][\\da-fA-F_]+|\\d[\\d_]*(?:(?!\\.\\.)\\.[\\d_]*)?(?:[eE][+-]?\\d[\\d_]*)?)(?:'?[iuf]\\d*)?/,\n    keyword:\n      /\\b(?:addr|as|asm|atomic|bind|block|break|case|cast|concept|const|continue|converter|defer|discard|distinct|do|elif|else|end|enum|except|export|finally|for|from|func|generic|if|import|include|interface|iterator|let|macro|method|mixin|nil|object|out|proc|ptr|raise|ref|return|static|template|try|tuple|type|using|var|when|while|with|without|yield)\\b/,\n    operator: {\n      // Look behind and look ahead prevent wrong highlighting of punctuations [. .] {. .} (. .)\n      // but allow the slice operator .. to take precedence over them\n      // One can define his own operators in Nim so all combination of operators might be an operator.\n      pattern:\n        /(^|[({\\[](?=\\.\\.)|(?![({\\[]\\.).)(?:(?:[=+\\-*\\/<>@$~&%|!?^:\\\\]|\\.\\.|\\.(?![)}\\]]))+|\\b(?:and|div|in|is|isnot|mod|not|notin|of|or|shl|shr|xor)\\b)/m,\n      lookbehind: true\n    },\n    punctuation: /[({\\[]\\.|\\.[)}\\]]|[`(){}\\[\\],:]/\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClBA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,OAAO,EAAE;MACPC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;IACV,CAAC;IACDC,MAAM,EAAE;MACN;MACAF,OAAO,EACL,gGAAgG;MAClGC,MAAM,EAAE;IACV,CAAC;IACDE,IAAI,EAAE;MACJ;MACAH,OAAO,EAAE,yCAAyC;MAClDC,MAAM,EAAE;IACV,CAAC;IACDG,QAAQ,EAAE;MACRJ,OAAO,EACL,mFAAmF;MACrFC,MAAM,EAAE,IAAI;MACZI,MAAM,EAAE;QACNC,QAAQ,EAAE;MACZ;IACF,CAAC;IACD;IACAC,UAAU,EAAE;MACVP,OAAO,EAAE,aAAa;MACtBC,MAAM,EAAE,IAAI;MACZI,MAAM,EAAE;QACNG,WAAW,EAAE;MACf;IACF,CAAC;IACD;IACAC,MAAM,EACJ,gGAAgG;IAClGC,OAAO,EACL,6VAA6V;IAC/VJ,QAAQ,EAAE;MACR;MACA;MACA;MACAN,OAAO,EACL,iJAAiJ;MACnJW,UAAU,EAAE;IACd,CAAC;IACDH,WAAW,EAAE;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}