{"ast": null, "code": "import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  if (info.parentSelectors.some(function (selector) {\n    var selectors = selector.split(',');\n    return selectors.some(function (item) {\n      return item.split('&').length > 2;\n    });\n  })) {\n    lintWarning('Should not use more than one `&` in a selector.', info);\n  }\n};\nexport default linter;", "map": {"version": 3, "names": ["lintWarning", "linter", "key", "value", "info", "parentSelectors", "some", "selector", "selectors", "split", "item", "length"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/cssinjs/es/linters/parentSelectorLinter.js"], "sourcesContent": ["import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  if (info.parentSelectors.some(function (selector) {\n    var selectors = selector.split(',');\n    return selectors.some(function (item) {\n      return item.split('&').length > 2;\n    });\n  })) {\n    lintWarning('Should not use more than one `&` in a selector.', info);\n  }\n};\nexport default linter;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,SAAS;AACrC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC7C,IAAIA,IAAI,CAACC,eAAe,CAACC,IAAI,CAAC,UAAUC,QAAQ,EAAE;IAChD,IAAIC,SAAS,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;IACnC,OAAOD,SAAS,CAACF,IAAI,CAAC,UAAUI,IAAI,EAAE;MACpC,OAAOA,IAAI,CAACD,KAAK,CAAC,GAAG,CAAC,CAACE,MAAM,GAAG,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC,EAAE;IACFX,WAAW,CAAC,iDAAiD,EAAEI,IAAI,CAAC;EACtE;AACF,CAAC;AACD,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}