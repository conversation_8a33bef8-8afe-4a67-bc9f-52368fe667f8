{"ast": null, "code": "'use strict';\n\nmodule.exports = haxe;\nhaxe.displayName = 'haxe';\nhaxe.aliases = [];\nfunction haxe(Prism) {\n  Prism.languages.haxe = Prism.languages.extend('clike', {\n    string: {\n      // Strings can be multi-line\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"/,\n      greedy: true\n    },\n    'class-name': [{\n      pattern: /(\\b(?:abstract|class|enum|extends|implements|interface|new|typedef)\\s+)[A-Z_]\\w*/,\n      lookbehind: true\n    },\n    // based on naming convention\n    /\\b[A-Z]\\w*/],\n    // The final look-ahead prevents highlighting of keywords if expressions such as \"haxe.macro.Expr\"\n    keyword: /\\bthis\\b|\\b(?:abstract|as|break|case|cast|catch|class|continue|default|do|dynamic|else|enum|extends|extern|final|for|from|function|if|implements|import|in|inline|interface|macro|new|null|operator|overload|override|package|private|public|return|static|super|switch|throw|to|try|typedef|untyped|using|var|while)(?!\\.)\\b/,\n    function: {\n      pattern: /\\b[a-z_]\\w*(?=\\s*(?:<[^<>]*>\\s*)?\\()/i,\n      greedy: true\n    },\n    operator: /\\.{3}|\\+\\+|--|&&|\\|\\||->|=>|(?:<<?|>{1,3}|[-+*/%!=&|^])=?|[?:~]/\n  });\n  Prism.languages.insertBefore('haxe', 'string', {\n    'string-interpolation': {\n      pattern: /'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(^|[^\\\\])\\$(?:\\w+|\\{[^{}]+\\})/,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{?|\\}$/,\n              alias: 'punctuation'\n            },\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.haxe\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  });\n  Prism.languages.insertBefore('haxe', 'class-name', {\n    regex: {\n      pattern: /~\\/(?:[^\\/\\\\\\r\\n]|\\\\.)+\\/[a-z]*/,\n      greedy: true,\n      inside: {\n        'regex-flags': /\\b[a-z]+$/,\n        'regex-source': {\n          pattern: /^(~\\/)[\\s\\S]+(?=\\/$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^~\\/|\\/$/\n      }\n    }\n  });\n  Prism.languages.insertBefore('haxe', 'keyword', {\n    preprocessor: {\n      pattern: /#(?:else|elseif|end|if)\\b.*/,\n      alias: 'property'\n    },\n    metadata: {\n      pattern: /@:?[\\w.]+/,\n      alias: 'symbol'\n    },\n    reification: {\n      pattern: /\\$(?:\\w+|(?=\\{))/,\n      alias: 'important'\n    }\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "haxe", "displayName", "aliases", "Prism", "languages", "extend", "string", "pattern", "greedy", "lookbehind", "keyword", "function", "operator", "insertBefore", "inside", "interpolation", "alias", "expression", "regex", "preprocessor", "metadata", "reification"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/haxe.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = haxe\nhaxe.displayName = 'haxe'\nhaxe.aliases = []\nfunction haxe(Prism) {\n  Prism.languages.haxe = Prism.languages.extend('clike', {\n    string: {\n      // Strings can be multi-line\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"/,\n      greedy: true\n    },\n    'class-name': [\n      {\n        pattern:\n          /(\\b(?:abstract|class|enum|extends|implements|interface|new|typedef)\\s+)[A-Z_]\\w*/,\n        lookbehind: true\n      }, // based on naming convention\n      /\\b[A-Z]\\w*/\n    ],\n    // The final look-ahead prevents highlighting of keywords if expressions such as \"haxe.macro.Expr\"\n    keyword:\n      /\\bthis\\b|\\b(?:abstract|as|break|case|cast|catch|class|continue|default|do|dynamic|else|enum|extends|extern|final|for|from|function|if|implements|import|in|inline|interface|macro|new|null|operator|overload|override|package|private|public|return|static|super|switch|throw|to|try|typedef|untyped|using|var|while)(?!\\.)\\b/,\n    function: {\n      pattern: /\\b[a-z_]\\w*(?=\\s*(?:<[^<>]*>\\s*)?\\()/i,\n      greedy: true\n    },\n    operator: /\\.{3}|\\+\\+|--|&&|\\|\\||->|=>|(?:<<?|>{1,3}|[-+*/%!=&|^])=?|[?:~]/\n  })\n  Prism.languages.insertBefore('haxe', 'string', {\n    'string-interpolation': {\n      pattern: /'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(^|[^\\\\])\\$(?:\\w+|\\{[^{}]+\\})/,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{?|\\}$/,\n              alias: 'punctuation'\n            },\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.haxe\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  Prism.languages.insertBefore('haxe', 'class-name', {\n    regex: {\n      pattern: /~\\/(?:[^\\/\\\\\\r\\n]|\\\\.)+\\/[a-z]*/,\n      greedy: true,\n      inside: {\n        'regex-flags': /\\b[a-z]+$/,\n        'regex-source': {\n          pattern: /^(~\\/)[\\s\\S]+(?=\\/$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^~\\/|\\/$/\n      }\n    }\n  })\n  Prism.languages.insertBefore('haxe', 'keyword', {\n    preprocessor: {\n      pattern: /#(?:else|elseif|end|if)\\b.*/,\n      alias: 'property'\n    },\n    metadata: {\n      pattern: /@:?[\\w.]+/,\n      alias: 'symbol'\n    },\n    reification: {\n      pattern: /\\$(?:\\w+|(?=\\{))/,\n      alias: 'important'\n    }\n  })\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,SAAS,CAACJ,IAAI,GAAGG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE;IACrDC,MAAM,EAAE;MACN;MACAC,OAAO,EAAE,wBAAwB;MACjCC,MAAM,EAAE;IACV,CAAC;IACD,YAAY,EAAE,CACZ;MACED,OAAO,EACL,kFAAkF;MACpFE,UAAU,EAAE;IACd,CAAC;IAAE;IACH,YAAY,CACb;IACD;IACAC,OAAO,EACL,+TAA+T;IACjUC,QAAQ,EAAE;MACRJ,OAAO,EAAE,uCAAuC;MAChDC,MAAM,EAAE;IACV,CAAC;IACDI,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFT,KAAK,CAACC,SAAS,CAACS,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE;IAC7C,sBAAsB,EAAE;MACtBN,OAAO,EAAE,wBAAwB;MACjCC,MAAM,EAAE,IAAI;MACZM,MAAM,EAAE;QACNC,aAAa,EAAE;UACbR,OAAO,EAAE,+BAA+B;UACxCE,UAAU,EAAE,IAAI;UAChBK,MAAM,EAAE;YACN,2BAA2B,EAAE;cAC3BP,OAAO,EAAE,YAAY;cACrBS,KAAK,EAAE;YACT,CAAC;YACDC,UAAU,EAAE;cACVV,OAAO,EAAE,SAAS;cAClBO,MAAM,EAAEX,KAAK,CAACC,SAAS,CAACJ;YAC1B;UACF;QACF,CAAC;QACDM,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;EACFH,KAAK,CAACC,SAAS,CAACS,YAAY,CAAC,MAAM,EAAE,YAAY,EAAE;IACjDK,KAAK,EAAE;MACLX,OAAO,EAAE,iCAAiC;MAC1CC,MAAM,EAAE,IAAI;MACZM,MAAM,EAAE;QACN,aAAa,EAAE,WAAW;QAC1B,cAAc,EAAE;UACdP,OAAO,EAAE,sBAAsB;UAC/BE,UAAU,EAAE,IAAI;UAChBO,KAAK,EAAE,gBAAgB;UACvBF,MAAM,EAAEX,KAAK,CAACC,SAAS,CAACc;QAC1B,CAAC;QACD,iBAAiB,EAAE;MACrB;IACF;EACF,CAAC,CAAC;EACFf,KAAK,CAACC,SAAS,CAACS,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE;IAC9CM,YAAY,EAAE;MACZZ,OAAO,EAAE,6BAA6B;MACtCS,KAAK,EAAE;IACT,CAAC;IACDI,QAAQ,EAAE;MACRb,OAAO,EAAE,WAAW;MACpBS,KAAK,EAAE;IACT,CAAC;IACDK,WAAW,EAAE;MACXd,OAAO,EAAE,kBAAkB;MAC3BS,KAAK,EAAE;IACT;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}