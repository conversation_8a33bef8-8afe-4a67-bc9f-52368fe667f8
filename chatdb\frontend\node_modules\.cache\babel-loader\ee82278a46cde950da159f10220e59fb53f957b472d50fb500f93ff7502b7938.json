{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BulbTwoToneSvg from \"@ant-design/icons-svg/es/asn/BulbTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BulbTwoTone = function BulbTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BulbTwoToneSvg\n  }));\n};\n\n/**![bulb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxMzZjLTE0MS40IDAtMjU2IDExNC42LTI1NiAyNTYgMCA5Mi41IDQ5LjQgMTc2LjMgMTI4LjEgMjIxLjhsMzUuOSAyMC44Vjc1MmgxODRWNjM0LjZsMzUuOS0yMC44QzcxOC42IDU2OC4zIDc2OCA0ODQuNSA3NjggMzkyYzAtMTQxLjQtMTE0LjYtMjU2LTI1Ni0yNTZ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik02MzIgODg4SDM5MmMtNC40IDAtOCAzLjYtOCA4djMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDE5MmMxNy43IDAgMzItMTQuMyAzMi0zMnYtMzJjMC00LjQtMy42LTgtOC04ek01MTIgNjRjLTE4MS4xIDAtMzI4IDE0Ni45LTMyOCAzMjggMCAxMjEuNCA2NiAyMjcuNCAxNjQgMjg0LjFWNzkyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDI2NGMxNy43IDAgMzItMTQuMyAzMi0zMlY2NzYuMWM5OC01Ni43IDE2NC0xNjIuNyAxNjQtMjg0LjEgMC0xODEuMS0xNDYuOS0zMjgtMzI4LTMyOHptMTI3LjkgNTQ5LjhMNjA0IDYzNC42Vjc1Mkg0MjBWNjM0LjZsLTM1LjktMjAuOEMzMDUuNCA1NjguMyAyNTYgNDg0LjUgMjU2IDM5MmMwLTE0MS40IDExNC42LTI1NiAyNTYtMjU2czI1NiAxMTQuNiAyNTYgMjU2YzAgOTIuNS00OS40IDE3Ni4zLTEyOC4xIDIyMS44eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BulbTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BulbTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "BulbTwoToneSvg", "AntdIcon", "BulbTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/BulbTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BulbTwoToneSvg from \"@ant-design/icons-svg/es/asn/BulbTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BulbTwoTone = function BulbTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BulbTwoToneSvg\n  }));\n};\n\n/**![bulb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxMzZjLTE0MS40IDAtMjU2IDExNC42LTI1NiAyNTYgMCA5Mi41IDQ5LjQgMTc2LjMgMTI4LjEgMjIxLjhsMzUuOSAyMC44Vjc1MmgxODRWNjM0LjZsMzUuOS0yMC44QzcxOC42IDU2OC4zIDc2OCA0ODQuNSA3NjggMzkyYzAtMTQxLjQtMTE0LjYtMjU2LTI1Ni0yNTZ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik02MzIgODg4SDM5MmMtNC40IDAtOCAzLjYtOCA4djMyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDE5MmMxNy43IDAgMzItMTQuMyAzMi0zMnYtMzJjMC00LjQtMy42LTgtOC04ek01MTIgNjRjLTE4MS4xIDAtMzI4IDE0Ni45LTMyOCAzMjggMCAxMjEuNCA2NiAyMjcuNCAxNjQgMjg0LjFWNzkyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDI2NGMxNy43IDAgMzItMTQuMyAzMi0zMlY2NzYuMWM5OC01Ni43IDE2NC0xNjIuNyAxNjQtMjg0LjEgMC0xODEuMS0xNDYuOS0zMjgtMzI4LTMyOHptMTI3LjkgNTQ5LjhMNjA0IDYzNC42Vjc1Mkg0MjBWNjM0LjZsLTM1LjktMjAuOEMzMDUuNCA1NjguMyAyNTYgNDg0LjUgMjU2IDM5MmMwLTE0MS40IDExNC42LTI1NiAyNTYtMjU2czI1NiAxMTQuNiAyNTYgMjU2YzAgOTIuNS00OS40IDE3Ni4zLTEyOC4xIDIyMS44eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BulbTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BulbTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}