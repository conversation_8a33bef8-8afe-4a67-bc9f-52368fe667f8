{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport LockTwoToneSvg from \"@ant-design/icons-svg/es/asn/LockTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar LockTwoTone = function LockTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: LockTwoToneSvg\n  }));\n};\n\n/**![lock](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA0NjRoLTY4VjI0MGMwLTcwLjctNTcuMy0xMjgtMTI4LTEyOEgzODhjLTcwLjcgMC0xMjggNTcuMy0xMjggMTI4djIyNGgtNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjM4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWNDk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek0zMzIgMjQwYzAtMzAuOSAyNS4xLTU2IDU2LTU2aDI0OGMzMC45IDAgNTYgMjUuMSA1NiA1NnYyMjRIMzMyVjI0MHptNDYwIDYwMEgyMzJWNTM2aDU2MHYzMDR6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0yMzIgODQwaDU2MFY1MzZIMjMydjMwNHptMjgwLTIyNmE0OC4wMSA0OC4wMSAwIDAxMjggODd2NTNjMCA0LjQtMy42IDgtOCA4aC00MGMtNC40IDAtOC0zLjYtOC04di01M2E0OC4wMSA0OC4wMSAwIDAxMjgtODd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik00ODQgNzAxdjUzYzAgNC40IDMuNiA4IDggOGg0MGM0LjQgMCA4LTMuNiA4LTh2LTUzYTQ4LjAxIDQ4LjAxIDAgMTAtNTYgMHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(LockTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LockTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "LockTwoToneSvg", "AntdIcon", "LockTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/LockTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport LockTwoToneSvg from \"@ant-design/icons-svg/es/asn/LockTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar LockTwoTone = function LockTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: LockTwoToneSvg\n  }));\n};\n\n/**![lock](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA0NjRoLTY4VjI0MGMwLTcwLjctNTcuMy0xMjgtMTI4LTEyOEgzODhjLTcwLjcgMC0xMjggNTcuMy0xMjggMTI4djIyNGgtNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjM4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWNDk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek0zMzIgMjQwYzAtMzAuOSAyNS4xLTU2IDU2LTU2aDI0OGMzMC45IDAgNTYgMjUuMSA1NiA1NnYyMjRIMzMyVjI0MHptNDYwIDYwMEgyMzJWNTM2aDU2MHYzMDR6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0yMzIgODQwaDU2MFY1MzZIMjMydjMwNHptMjgwLTIyNmE0OC4wMSA0OC4wMSAwIDAxMjggODd2NTNjMCA0LjQtMy42IDgtOCA4aC00MGMtNC40IDAtOC0zLjYtOC04di01M2E0OC4wMSA0OC4wMSAwIDAxMjgtODd6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik00ODQgNzAxdjUzYzAgNC40IDMuNiA4IDggOGg0MGM0LjQgMCA4LTMuNiA4LTh2LTUzYTQ4LjAxIDQ4LjAxIDAgMTAtNTYgMHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(LockTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LockTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}