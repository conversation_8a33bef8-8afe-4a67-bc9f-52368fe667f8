{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport { useCacheToken } from '@ant-design/cssinjs';\nimport version from '../version';\nimport { defaultTheme, DesignTokenContext } from './context';\nimport defaultSeedToken from './themes/seed';\nimport formatToken from './util/alias';\nexport const unitless = {\n  lineHeight: true,\n  lineHeightSM: true,\n  lineHeightLG: true,\n  lineHeightHeading1: true,\n  lineHeightHeading2: true,\n  lineHeightHeading3: true,\n  lineHeightHeading4: true,\n  lineHeightHeading5: true,\n  opacityLoading: true,\n  fontWeightStrong: true,\n  zIndexPopupBase: true,\n  zIndexBase: true,\n  opacityImage: true\n};\nexport const ignore = {\n  size: true,\n  sizeSM: true,\n  sizeLG: true,\n  sizeMD: true,\n  sizeXS: true,\n  sizeXXS: true,\n  sizeMS: true,\n  sizeXL: true,\n  sizeXXL: true,\n  sizeUnit: true,\n  sizeStep: true,\n  motionBase: true,\n  motionUnit: true\n};\nconst preserve = {\n  screenXS: true,\n  screenXSMin: true,\n  screenXSMax: true,\n  screenSM: true,\n  screenSMMin: true,\n  screenSMMax: true,\n  screenMD: true,\n  screenMDMin: true,\n  screenMDMax: true,\n  screenLG: true,\n  screenLGMin: true,\n  screenLGMax: true,\n  screenXL: true,\n  screenXLMin: true,\n  screenXLMax: true,\n  screenXXL: true,\n  screenXXLMin: true\n};\nexport const getComputedToken = (originToken, overrideToken, theme) => {\n  const derivativeToken = theme.getDerivativeToken(originToken);\n  const {\n      override\n    } = overrideToken,\n    components = __rest(overrideToken, [\"override\"]);\n  // Merge with override\n  let mergedDerivativeToken = Object.assign(Object.assign({}, derivativeToken), {\n    override\n  });\n  // Format if needed\n  mergedDerivativeToken = formatToken(mergedDerivativeToken);\n  if (components) {\n    Object.entries(components).forEach(_ref => {\n      let [key, value] = _ref;\n      const {\n          theme: componentTheme\n        } = value,\n        componentTokens = __rest(value, [\"theme\"]);\n      let mergedComponentToken = componentTokens;\n      if (componentTheme) {\n        mergedComponentToken = getComputedToken(Object.assign(Object.assign({}, mergedDerivativeToken), componentTokens), {\n          override: componentTokens\n        }, componentTheme);\n      }\n      mergedDerivativeToken[key] = mergedComponentToken;\n    });\n  }\n  return mergedDerivativeToken;\n};\n// ================================== Hook ==================================\nexport default function useToken() {\n  const {\n    token: rootDesignToken,\n    hashed,\n    theme,\n    override,\n    cssVar\n  } = React.useContext(DesignTokenContext);\n  const salt = `${version}-${hashed || ''}`;\n  const mergedTheme = theme || defaultTheme;\n  const [token, hashId, realToken] = useCacheToken(mergedTheme, [defaultSeedToken, rootDesignToken], {\n    salt,\n    override,\n    getComputedToken,\n    // formatToken will not be consumed after 1.15.0 with getComputedToken.\n    // But token will break if @ant-design/cssinjs is under 1.15.0 without it\n    formatToken,\n    cssVar: cssVar && {\n      prefix: cssVar.prefix,\n      key: cssVar.key,\n      unitless,\n      ignore,\n      preserve\n    }\n  });\n  return [mergedTheme, realToken, hashed ? hashId : '', token, cssVar];\n}", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "useCacheToken", "version", "defaultTheme", "DesignTokenContext", "defaultSeedToken", "formatToken", "unitless", "lineHeight", "lineHeightSM", "lineHeightLG", "lineHeightHeading1", "lineHeightHeading2", "lineHeightHeading3", "lineHeightHeading4", "lineHeightHeading5", "opacityLoading", "fontWeightStrong", "zIndexPopupBase", "zIndexBase", "opacityImage", "ignore", "size", "sizeSM", "sizeLG", "sizeMD", "sizeXS", "sizeXXS", "sizeMS", "sizeXL", "sizeXXL", "sizeUnit", "sizeStep", "motionBase", "motionUnit", "preserve", "screenXS", "screenXSMin", "screenXSMax", "screenSM", "screenSMMin", "screenSMMax", "screenMD", "screenMDMin", "screenMDMax", "screenLG", "screenLGMin", "screenLGMax", "screenXL", "screenXLMin", "screenXLMax", "screenXXL", "screenXXLMin", "getComputedToken", "originToken", "overrideToken", "theme", "derivativeToken", "getDerivativeToken", "override", "components", "mergedDerivativeToken", "assign", "entries", "for<PERSON>ach", "_ref", "key", "value", "componentTheme", "componentTokens", "mergedComponentToken", "useToken", "token", "rootDesignToken", "hashed", "cssVar", "useContext", "salt", "mergedTheme", "hashId", "realToken", "prefix"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/theme/useToken.js"], "sourcesContent": ["var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from 'react';\nimport { useCacheToken } from '@ant-design/cssinjs';\nimport version from '../version';\nimport { defaultTheme, DesignTokenContext } from './context';\nimport defaultSeedToken from './themes/seed';\nimport formatToken from './util/alias';\nexport const unitless = {\n  lineHeight: true,\n  lineHeightSM: true,\n  lineHeightLG: true,\n  lineHeightHeading1: true,\n  lineHeightHeading2: true,\n  lineHeightHeading3: true,\n  lineHeightHeading4: true,\n  lineHeightHeading5: true,\n  opacityLoading: true,\n  fontWeightStrong: true,\n  zIndexPopupBase: true,\n  zIndexBase: true,\n  opacityImage: true\n};\nexport const ignore = {\n  size: true,\n  sizeSM: true,\n  sizeLG: true,\n  sizeMD: true,\n  sizeXS: true,\n  sizeXXS: true,\n  sizeMS: true,\n  sizeXL: true,\n  sizeXXL: true,\n  sizeUnit: true,\n  sizeStep: true,\n  motionBase: true,\n  motionUnit: true\n};\nconst preserve = {\n  screenXS: true,\n  screenXSMin: true,\n  screenXSMax: true,\n  screenSM: true,\n  screenSMMin: true,\n  screenSMMax: true,\n  screenMD: true,\n  screenMDMin: true,\n  screenMDMax: true,\n  screenLG: true,\n  screenLGMin: true,\n  screenLGMax: true,\n  screenXL: true,\n  screenXLMin: true,\n  screenXLMax: true,\n  screenXXL: true,\n  screenXXLMin: true\n};\nexport const getComputedToken = (originToken, overrideToken, theme) => {\n  const derivativeToken = theme.getDerivativeToken(originToken);\n  const {\n      override\n    } = overrideToken,\n    components = __rest(overrideToken, [\"override\"]);\n  // Merge with override\n  let mergedDerivativeToken = Object.assign(Object.assign({}, derivativeToken), {\n    override\n  });\n  // Format if needed\n  mergedDerivativeToken = formatToken(mergedDerivativeToken);\n  if (components) {\n    Object.entries(components).forEach(_ref => {\n      let [key, value] = _ref;\n      const {\n          theme: componentTheme\n        } = value,\n        componentTokens = __rest(value, [\"theme\"]);\n      let mergedComponentToken = componentTokens;\n      if (componentTheme) {\n        mergedComponentToken = getComputedToken(Object.assign(Object.assign({}, mergedDerivativeToken), componentTokens), {\n          override: componentTokens\n        }, componentTheme);\n      }\n      mergedDerivativeToken[key] = mergedComponentToken;\n    });\n  }\n  return mergedDerivativeToken;\n};\n// ================================== Hook ==================================\nexport default function useToken() {\n  const {\n    token: rootDesignToken,\n    hashed,\n    theme,\n    override,\n    cssVar\n  } = React.useContext(DesignTokenContext);\n  const salt = `${version}-${hashed || ''}`;\n  const mergedTheme = theme || defaultTheme;\n  const [token, hashId, realToken] = useCacheToken(mergedTheme, [defaultSeedToken, rootDesignToken], {\n    salt,\n    override,\n    getComputedToken,\n    // formatToken will not be consumed after 1.15.0 with getComputedToken.\n    // But token will break if @ant-design/cssinjs is under 1.15.0 without it\n    formatToken,\n    cssVar: cssVar && {\n      prefix: cssVar.prefix,\n      key: cssVar.key,\n      unitless,\n      ignore,\n      preserve\n    }\n  });\n  return [mergedTheme, realToken, hashed ? hashId : '', token, cssVar];\n}"], "mappings": "AAAA,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,WAAW;AAC5D,OAAOC,gBAAgB,MAAM,eAAe;AAC5C,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,MAAMC,QAAQ,GAAG;EACtBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,IAAI;EAClBC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE,IAAI;EACxBC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,eAAe,EAAE,IAAI;EACrBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,MAAM,GAAG;EACpBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE;AACd,CAAC;AACD,MAAMC,QAAQ,GAAG;EACfC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE;AAChB,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,WAAW,EAAEC,aAAa,EAAEC,KAAK,KAAK;EACrE,MAAMC,eAAe,GAAGD,KAAK,CAACE,kBAAkB,CAACJ,WAAW,CAAC;EAC7D,MAAM;MACFK;IACF,CAAC,GAAGJ,aAAa;IACjBK,UAAU,GAAG1E,MAAM,CAACqE,aAAa,EAAE,CAAC,UAAU,CAAC,CAAC;EAClD;EACA,IAAIM,qBAAqB,GAAGtE,MAAM,CAACuE,MAAM,CAACvE,MAAM,CAACuE,MAAM,CAAC,CAAC,CAAC,EAAEL,eAAe,CAAC,EAAE;IAC5EE;EACF,CAAC,CAAC;EACF;EACAE,qBAAqB,GAAGvD,WAAW,CAACuD,qBAAqB,CAAC;EAC1D,IAAID,UAAU,EAAE;IACdrE,MAAM,CAACwE,OAAO,CAACH,UAAU,CAAC,CAACI,OAAO,CAACC,IAAI,IAAI;MACzC,IAAI,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAGF,IAAI;MACvB,MAAM;UACFT,KAAK,EAAEY;QACT,CAAC,GAAGD,KAAK;QACTE,eAAe,GAAGnF,MAAM,CAACiF,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC;MAC5C,IAAIG,oBAAoB,GAAGD,eAAe;MAC1C,IAAID,cAAc,EAAE;QAClBE,oBAAoB,GAAGjB,gBAAgB,CAAC9D,MAAM,CAACuE,MAAM,CAACvE,MAAM,CAACuE,MAAM,CAAC,CAAC,CAAC,EAAED,qBAAqB,CAAC,EAAEQ,eAAe,CAAC,EAAE;UAChHV,QAAQ,EAAEU;QACZ,CAAC,EAAED,cAAc,CAAC;MACpB;MACAP,qBAAqB,CAACK,GAAG,CAAC,GAAGI,oBAAoB;IACnD,CAAC,CAAC;EACJ;EACA,OAAOT,qBAAqB;AAC9B,CAAC;AACD;AACA,eAAe,SAASU,QAAQA,CAAA,EAAG;EACjC,MAAM;IACJC,KAAK,EAAEC,eAAe;IACtBC,MAAM;IACNlB,KAAK;IACLG,QAAQ;IACRgB;EACF,CAAC,GAAG3E,KAAK,CAAC4E,UAAU,CAACxE,kBAAkB,CAAC;EACxC,MAAMyE,IAAI,GAAG,GAAG3E,OAAO,IAAIwE,MAAM,IAAI,EAAE,EAAE;EACzC,MAAMI,WAAW,GAAGtB,KAAK,IAAIrD,YAAY;EACzC,MAAM,CAACqE,KAAK,EAAEO,MAAM,EAAEC,SAAS,CAAC,GAAG/E,aAAa,CAAC6E,WAAW,EAAE,CAACzE,gBAAgB,EAAEoE,eAAe,CAAC,EAAE;IACjGI,IAAI;IACJlB,QAAQ;IACRN,gBAAgB;IAChB;IACA;IACA/C,WAAW;IACXqE,MAAM,EAAEA,MAAM,IAAI;MAChBM,MAAM,EAAEN,MAAM,CAACM,MAAM;MACrBf,GAAG,EAAES,MAAM,CAACT,GAAG;MACf3D,QAAQ;MACRc,MAAM;MACNc;IACF;EACF,CAAC,CAAC;EACF,OAAO,CAAC2C,WAAW,EAAEE,SAAS,EAAEN,MAAM,GAAGK,MAAM,GAAG,EAAE,EAAEP,KAAK,EAAEG,MAAM,CAAC;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}