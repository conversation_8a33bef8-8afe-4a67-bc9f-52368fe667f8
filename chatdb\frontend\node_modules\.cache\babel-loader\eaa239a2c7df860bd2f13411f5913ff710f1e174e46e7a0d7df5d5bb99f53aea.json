{"ast": null, "code": "'use strict';\n\nmodule.exports = python;\npython.displayName = 'python';\npython.aliases = ['py'];\nfunction python(Prism) {\n  Prism.languages.python = {\n    comment: {\n      pattern: /(^|[^\\\\])#.*/,\n      lookbehind: true,\n      greedy: true\n    },\n    'string-interpolation': {\n      pattern: /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,\n      greedy: true,\n      inside: {\n        interpolation: {\n          // \"{\" <expression> <optional \"!s\", \"!r\", or \"!a\"> <optional \":\" format specifier> \"}\"\n          pattern: /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,\n          lookbehind: true,\n          inside: {\n            'format-spec': {\n              pattern: /(:)[^:(){}]+(?=\\}$)/,\n              lookbehind: true\n            },\n            'conversion-option': {\n              pattern: /![sra](?=[:}]$)/,\n              alias: 'punctuation'\n            },\n            rest: null\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    'triple-quoted-string': {\n      pattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,\n      greedy: true,\n      alias: 'string'\n    },\n    string: {\n      pattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,\n      greedy: true\n    },\n    function: {\n      pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,\n      lookbehind: true\n    },\n    'class-name': {\n      pattern: /(\\bclass\\s+)\\w+/i,\n      lookbehind: true\n    },\n    decorator: {\n      pattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,\n      lookbehind: true,\n      alias: ['annotation', 'punctuation'],\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    keyword: /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,\n    builtin: /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,\n    boolean: /\\b(?:False|None|True)\\b/,\n    number: /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,\n    operator: /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n    punctuation: /[{}[\\];(),.:]/\n  };\n  Prism.languages.python['string-interpolation'].inside['interpolation'].inside.rest = Prism.languages.python;\n  Prism.languages.py = Prism.languages.python;\n}", "map": {"version": 3, "names": ["module", "exports", "python", "displayName", "aliases", "Prism", "languages", "comment", "pattern", "lookbehind", "greedy", "inside", "interpolation", "alias", "rest", "string", "function", "decorator", "punctuation", "keyword", "builtin", "boolean", "number", "operator", "py"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/python.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = python\npython.displayName = 'python'\npython.aliases = ['py']\nfunction python(Prism) {\n  Prism.languages.python = {\n    comment: {\n      pattern: /(^|[^\\\\])#.*/,\n      lookbehind: true,\n      greedy: true\n    },\n    'string-interpolation': {\n      pattern:\n        /(?:f|fr|rf)(?:(\"\"\"|''')[\\s\\S]*?\\1|(\"|')(?:\\\\.|(?!\\2)[^\\\\\\r\\n])*\\2)/i,\n      greedy: true,\n      inside: {\n        interpolation: {\n          // \"{\" <expression> <optional \"!s\", \"!r\", or \"!a\"> <optional \":\" format specifier> \"}\"\n          pattern:\n            /((?:^|[^{])(?:\\{\\{)*)\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}]|\\{(?!\\{)(?:[^{}])+\\})+\\})+\\}/,\n          lookbehind: true,\n          inside: {\n            'format-spec': {\n              pattern: /(:)[^:(){}]+(?=\\}$)/,\n              lookbehind: true\n            },\n            'conversion-option': {\n              pattern: /![sra](?=[:}]$)/,\n              alias: 'punctuation'\n            },\n            rest: null\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    },\n    'triple-quoted-string': {\n      pattern: /(?:[rub]|br|rb)?(\"\"\"|''')[\\s\\S]*?\\1/i,\n      greedy: true,\n      alias: 'string'\n    },\n    string: {\n      pattern: /(?:[rub]|br|rb)?(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/i,\n      greedy: true\n    },\n    function: {\n      pattern: /((?:^|\\s)def[ \\t]+)[a-zA-Z_]\\w*(?=\\s*\\()/g,\n      lookbehind: true\n    },\n    'class-name': {\n      pattern: /(\\bclass\\s+)\\w+/i,\n      lookbehind: true\n    },\n    decorator: {\n      pattern: /(^[\\t ]*)@\\w+(?:\\.\\w+)*/m,\n      lookbehind: true,\n      alias: ['annotation', 'punctuation'],\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    keyword:\n      /\\b(?:_(?=\\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\\b/,\n    builtin:\n      /\\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\\b/,\n    boolean: /\\b(?:False|None|True)\\b/,\n    number:\n      /\\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\\b|(?:\\b\\d+(?:_\\d+)*(?:\\.(?:\\d+(?:_\\d+)*)?)?|\\B\\.\\d+(?:_\\d+)*)(?:e[+-]?\\d+(?:_\\d+)*)?j?(?!\\w)/i,\n    operator: /[-+%=]=?|!=|:=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.python['string-interpolation'].inside[\n    'interpolation'\n  ].inside.rest = Prism.languages.python\n  Prism.languages.py = Prism.languages.python\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,CAAC,IAAI,CAAC;AACvB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvBK,OAAO,EAAE;MACPC,OAAO,EAAE,cAAc;MACvBC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,sBAAsB,EAAE;MACtBF,OAAO,EACL,qEAAqE;MACvEE,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNC,aAAa,EAAE;UACb;UACAJ,OAAO,EACL,qFAAqF;UACvFC,UAAU,EAAE,IAAI;UAChBE,MAAM,EAAE;YACN,aAAa,EAAE;cACbH,OAAO,EAAE,qBAAqB;cAC9BC,UAAU,EAAE;YACd,CAAC;YACD,mBAAmB,EAAE;cACnBD,OAAO,EAAE,iBAAiB;cAC1BK,KAAK,EAAE;YACT,CAAC;YACDC,IAAI,EAAE;UACR;QACF,CAAC;QACDC,MAAM,EAAE;MACV;IACF,CAAC;IACD,sBAAsB,EAAE;MACtBP,OAAO,EAAE,sCAAsC;MAC/CE,MAAM,EAAE,IAAI;MACZG,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNP,OAAO,EAAE,kDAAkD;MAC3DE,MAAM,EAAE;IACV,CAAC;IACDM,QAAQ,EAAE;MACRR,OAAO,EAAE,2CAA2C;MACpDC,UAAU,EAAE;IACd,CAAC;IACD,YAAY,EAAE;MACZD,OAAO,EAAE,kBAAkB;MAC3BC,UAAU,EAAE;IACd,CAAC;IACDQ,SAAS,EAAE;MACTT,OAAO,EAAE,0BAA0B;MACnCC,UAAU,EAAE,IAAI;MAChBI,KAAK,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;MACpCF,MAAM,EAAE;QACNO,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EACL,uNAAuN;IACzNC,OAAO,EACL,qhBAAqhB;IACvhBC,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EACJ,kJAAkJ;IACpJC,QAAQ,EAAE,sDAAsD;IAChEL,WAAW,EAAE;EACf,CAAC;EACDb,KAAK,CAACC,SAAS,CAACJ,MAAM,CAAC,sBAAsB,CAAC,CAACS,MAAM,CACnD,eAAe,CAChB,CAACA,MAAM,CAACG,IAAI,GAAGT,KAAK,CAACC,SAAS,CAACJ,MAAM;EACtCG,KAAK,CAACC,SAAS,CAACkB,EAAE,GAAGnB,KAAK,CAACC,SAAS,CAACJ,MAAM;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}