{"ast": null, "code": "/*\nLanguage: Pony\nAuthor: <PERSON> <<EMAIL>>\nDescription: Pony is an open-source, object-oriented, actor-model,\n             capabilities-secure, high performance programming language.\nWebsite: https://www.ponylang.io\n*/\n\nfunction pony(hljs) {\n  const KEYWORDS = {\n    keyword: 'actor addressof and as be break class compile_error compile_intrinsic ' + 'consume continue delegate digestof do else elseif embed end error ' + 'for fun if ifdef in interface is isnt lambda let match new not object ' + 'or primitive recover repeat return struct then trait try type until ' + 'use var where while with xor',\n    meta: 'iso val tag trn box ref',\n    literal: 'this false true'\n  };\n  const TRIPLE_QUOTE_STRING_MODE = {\n    className: 'string',\n    begin: '\"\"\"',\n    end: '\"\"\"',\n    relevance: 10\n  };\n  const QUOTE_STRING_MODE = {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    contains: [hljs.BACKSLASH_ESCAPE]\n  };\n  const SINGLE_QUOTE_CHAR_MODE = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    contains: [hljs.BACKSLASH_ESCAPE],\n    relevance: 0\n  };\n  const TYPE_NAME = {\n    className: 'type',\n    begin: '\\\\b_?[A-Z][\\\\w]*',\n    relevance: 0\n  };\n  const PRIMED_NAME = {\n    begin: hljs.IDENT_RE + '\\'',\n    relevance: 0\n  };\n  const NUMBER_MODE = {\n    className: 'number',\n    begin: '(-?)(\\\\b0[xX][a-fA-F0-9]+|\\\\b0[bB][01]+|(\\\\b\\\\d+(_\\\\d+)?(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)',\n    relevance: 0\n  };\n\n  /**\n   * The `FUNCTION` and `CLASS` modes were intentionally removed to simplify\n   * highlighting and fix cases like\n   * ```\n   * interface Iterator[A: A]\n   *   fun has_next(): Bool\n   *   fun next(): A?\n   * ```\n   * where it is valid to have a function head without a body\n   */\n\n  return {\n    name: 'Pony',\n    keywords: KEYWORDS,\n    contains: [TYPE_NAME, TRIPLE_QUOTE_STRING_MODE, QUOTE_STRING_MODE, SINGLE_QUOTE_CHAR_MODE, PRIMED_NAME, NUMBER_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n  };\n}\nmodule.exports = pony;", "map": {"version": 3, "names": ["pony", "hljs", "KEYWORDS", "keyword", "meta", "literal", "TRIPLE_QUOTE_STRING_MODE", "className", "begin", "end", "relevance", "QUOTE_STRING_MODE", "contains", "BACKSLASH_ESCAPE", "SINGLE_QUOTE_CHAR_MODE", "TYPE_NAME", "PRIMED_NAME", "IDENT_RE", "NUMBER_MODE", "name", "keywords", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/pony.js"], "sourcesContent": ["/*\nLanguage: Pony\nAuthor: <PERSON> <<EMAIL>>\nDescription: Pony is an open-source, object-oriented, actor-model,\n             capabilities-secure, high performance programming language.\nWebsite: https://www.ponylang.io\n*/\n\nfunction pony(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'actor addressof and as be break class compile_error compile_intrinsic ' +\n      'consume continue delegate digestof do else elseif embed end error ' +\n      'for fun if ifdef in interface is isnt lambda let match new not object ' +\n      'or primitive recover repeat return struct then trait try type until ' +\n      'use var where while with xor',\n    meta:\n      'iso val tag trn box ref',\n    literal:\n      'this false true'\n  };\n\n  const TRIPLE_QUOTE_STRING_MODE = {\n    className: 'string',\n    begin: '\"\"\"',\n    end: '\"\"\"',\n    relevance: 10\n  };\n\n  const QUOTE_STRING_MODE = {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n\n  const SINGLE_QUOTE_CHAR_MODE = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    relevance: 0\n  };\n\n  const TYPE_NAME = {\n    className: 'type',\n    begin: '\\\\b_?[A-Z][\\\\w]*',\n    relevance: 0\n  };\n\n  const PRIMED_NAME = {\n    begin: hljs.IDENT_RE + '\\'',\n    relevance: 0\n  };\n\n  const NUMBER_MODE = {\n    className: 'number',\n    begin: '(-?)(\\\\b0[xX][a-fA-F0-9]+|\\\\b0[bB][01]+|(\\\\b\\\\d+(_\\\\d+)?(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)',\n    relevance: 0\n  };\n\n  /**\n   * The `FUNCTION` and `CLASS` modes were intentionally removed to simplify\n   * highlighting and fix cases like\n   * ```\n   * interface Iterator[A: A]\n   *   fun has_next(): Bool\n   *   fun next(): A?\n   * ```\n   * where it is valid to have a function head without a body\n   */\n\n  return {\n    name: 'Pony',\n    keywords: KEYWORDS,\n    contains: [\n      TYPE_NAME,\n      TRIPLE_QUOTE_STRING_MODE,\n      QUOTE_STRING_MODE,\n      SINGLE_QUOTE_CHAR_MODE,\n      PRIMED_NAME,\n      NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = pony;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,MAAMC,QAAQ,GAAG;IACfC,OAAO,EACL,wEAAwE,GACxE,oEAAoE,GACpE,wEAAwE,GACxE,sEAAsE,GACtE,8BAA8B;IAChCC,IAAI,EACF,yBAAyB;IAC3BC,OAAO,EACL;EACJ,CAAC;EAED,MAAMC,wBAAwB,GAAG;IAC/BC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,KAAK;IACZC,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,iBAAiB,GAAG;IACxBJ,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACRG,QAAQ,EAAE,CAAEX,IAAI,CAACY,gBAAgB;EACnC,CAAC;EAED,MAAMC,sBAAsB,GAAG;IAC7BP,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTG,QAAQ,EAAE,CAAEX,IAAI,CAACY,gBAAgB,CAAE;IACnCH,SAAS,EAAE;EACb,CAAC;EAED,MAAMK,SAAS,GAAG;IAChBR,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,kBAAkB;IACzBE,SAAS,EAAE;EACb,CAAC;EAED,MAAMM,WAAW,GAAG;IAClBR,KAAK,EAAEP,IAAI,CAACgB,QAAQ,GAAG,IAAI;IAC3BP,SAAS,EAAE;EACb,CAAC;EAED,MAAMQ,WAAW,GAAG;IAClBX,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE,8FAA8F;IACrGE,SAAS,EAAE;EACb,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE,OAAO;IACLS,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAElB,QAAQ;IAClBU,QAAQ,EAAE,CACRG,SAAS,EACTT,wBAAwB,EACxBK,iBAAiB,EACjBG,sBAAsB,EACtBE,WAAW,EACXE,WAAW,EACXjB,IAAI,CAACoB,mBAAmB,EACxBpB,IAAI,CAACqB,oBAAoB;EAE7B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGxB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}