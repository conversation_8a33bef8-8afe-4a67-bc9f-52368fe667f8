{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"./useEvent\";\nimport { useLayoutUpdateEffect } from \"./useLayoutEffect\";\nimport useState from \"./useState\";\n/** We only think `undefined` is empty */\nfunction hasValue(value) {\n  return value !== undefined;\n}\n\n/**\n * Similar to `useState` but will use props value if provided.\n * Note that internal use rc-util `useState` hook.\n */\nexport default function useMergedState(defaultStateValue, option) {\n  var _ref = option || {},\n    defaultValue = _ref.defaultValue,\n    value = _ref.value,\n    onChange = _ref.onChange,\n    postState = _ref.postState;\n\n  // ======================= Init =======================\n  var _useState = useState(function () {\n      if (hasValue(value)) {\n        return value;\n      } else if (hasValue(defaultValue)) {\n        return typeof defaultValue === 'function' ? defaultValue() : defaultValue;\n      } else {\n        return typeof defaultStateValue === 'function' ? defaultStateValue() : defaultStateValue;\n      }\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    innerValue = _useState2[0],\n    setInnerValue = _useState2[1];\n  var mergedValue = value !== undefined ? value : innerValue;\n  var postMergedValue = postState ? postState(mergedValue) : mergedValue;\n\n  // ====================== Change ======================\n  var onChangeFn = useEvent(onChange);\n  var _useState3 = useState([mergedValue]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    prevValue = _useState4[0],\n    setPrevValue = _useState4[1];\n  useLayoutUpdateEffect(function () {\n    var prev = prevValue[0];\n    if (innerValue !== prev) {\n      onChangeFn(innerValue, prev);\n    }\n  }, [prevValue]);\n\n  // Sync value back to `undefined` when it from control to un-control\n  useLayoutUpdateEffect(function () {\n    if (!hasValue(value)) {\n      setInnerValue(value);\n    }\n  }, [value]);\n\n  // ====================== Update ======================\n  var triggerChange = useEvent(function (updater, ignoreDestroy) {\n    setInnerValue(updater, ignoreDestroy);\n    setPrevValue([mergedValue], ignoreDestroy);\n  });\n  return [postMergedValue, triggerChange];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useEvent", "useLayoutUpdateEffect", "useState", "hasValue", "value", "undefined", "useMergedState", "defaultStateValue", "option", "_ref", "defaultValue", "onChange", "postState", "_useState", "_useState2", "innerValue", "setInnerValue", "mergedValue", "postMergedValue", "onChangeFn", "_useState3", "_useState4", "prevValue", "setPrevValue", "prev", "trigger<PERSON>hange", "updater", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/rc-util/es/hooks/useMergedState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"./useEvent\";\nimport { useLayoutUpdateEffect } from \"./useLayoutEffect\";\nimport useState from \"./useState\";\n/** We only think `undefined` is empty */\nfunction hasValue(value) {\n  return value !== undefined;\n}\n\n/**\n * Similar to `useState` but will use props value if provided.\n * Note that internal use rc-util `useState` hook.\n */\nexport default function useMergedState(defaultStateValue, option) {\n  var _ref = option || {},\n    defaultValue = _ref.defaultValue,\n    value = _ref.value,\n    onChange = _ref.onChange,\n    postState = _ref.postState;\n\n  // ======================= Init =======================\n  var _useState = useState(function () {\n      if (hasValue(value)) {\n        return value;\n      } else if (hasValue(defaultValue)) {\n        return typeof defaultValue === 'function' ? defaultValue() : defaultValue;\n      } else {\n        return typeof defaultStateValue === 'function' ? defaultStateValue() : defaultStateValue;\n      }\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    innerValue = _useState2[0],\n    setInnerValue = _useState2[1];\n  var mergedValue = value !== undefined ? value : innerValue;\n  var postMergedValue = postState ? postState(mergedValue) : mergedValue;\n\n  // ====================== Change ======================\n  var onChangeFn = useEvent(onChange);\n  var _useState3 = useState([mergedValue]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    prevValue = _useState4[0],\n    setPrevValue = _useState4[1];\n  useLayoutUpdateEffect(function () {\n    var prev = prevValue[0];\n    if (innerValue !== prev) {\n      onChangeFn(innerValue, prev);\n    }\n  }, [prevValue]);\n\n  // Sync value back to `undefined` when it from control to un-control\n  useLayoutUpdateEffect(function () {\n    if (!hasValue(value)) {\n      setInnerValue(value);\n    }\n  }, [value]);\n\n  // ====================== Update ======================\n  var triggerChange = useEvent(function (updater, ignoreDestroy) {\n    setInnerValue(updater, ignoreDestroy);\n    setPrevValue([mergedValue], ignoreDestroy);\n  });\n  return [postMergedValue, triggerChange];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,OAAOC,QAAQ,MAAM,YAAY;AACjC;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK,KAAKC,SAAS;AAC5B;;AAEA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,iBAAiB,EAAEC,MAAM,EAAE;EAChE,IAAIC,IAAI,GAAGD,MAAM,IAAI,CAAC,CAAC;IACrBE,YAAY,GAAGD,IAAI,CAACC,YAAY;IAChCN,KAAK,GAAGK,IAAI,CAACL,KAAK;IAClBO,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,SAAS,GAAGH,IAAI,CAACG,SAAS;;EAE5B;EACA,IAAIC,SAAS,GAAGX,QAAQ,CAAC,YAAY;MACjC,IAAIC,QAAQ,CAACC,KAAK,CAAC,EAAE;QACnB,OAAOA,KAAK;MACd,CAAC,MAAM,IAAID,QAAQ,CAACO,YAAY,CAAC,EAAE;QACjC,OAAO,OAAOA,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC,CAAC,GAAGA,YAAY;MAC3E,CAAC,MAAM;QACL,OAAO,OAAOH,iBAAiB,KAAK,UAAU,GAAGA,iBAAiB,CAAC,CAAC,GAAGA,iBAAiB;MAC1F;IACF,CAAC,CAAC;IACFO,UAAU,GAAGf,cAAc,CAACc,SAAS,EAAE,CAAC,CAAC;IACzCE,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC/B,IAAIG,WAAW,GAAGb,KAAK,KAAKC,SAAS,GAAGD,KAAK,GAAGW,UAAU;EAC1D,IAAIG,eAAe,GAAGN,SAAS,GAAGA,SAAS,CAACK,WAAW,CAAC,GAAGA,WAAW;;EAEtE;EACA,IAAIE,UAAU,GAAGnB,QAAQ,CAACW,QAAQ,CAAC;EACnC,IAAIS,UAAU,GAAGlB,QAAQ,CAAC,CAACe,WAAW,CAAC,CAAC;IACtCI,UAAU,GAAGtB,cAAc,CAACqB,UAAU,EAAE,CAAC,CAAC;IAC1CE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC9BpB,qBAAqB,CAAC,YAAY;IAChC,IAAIuB,IAAI,GAAGF,SAAS,CAAC,CAAC,CAAC;IACvB,IAAIP,UAAU,KAAKS,IAAI,EAAE;MACvBL,UAAU,CAACJ,UAAU,EAAES,IAAI,CAAC;IAC9B;EACF,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;;EAEf;EACArB,qBAAqB,CAAC,YAAY;IAChC,IAAI,CAACE,QAAQ,CAACC,KAAK,CAAC,EAAE;MACpBY,aAAa,CAACZ,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEX;EACA,IAAIqB,aAAa,GAAGzB,QAAQ,CAAC,UAAU0B,OAAO,EAAEC,aAAa,EAAE;IAC7DX,aAAa,CAACU,OAAO,EAAEC,aAAa,CAAC;IACrCJ,YAAY,CAAC,CAACN,WAAW,CAAC,EAAEU,aAAa,CAAC;EAC5C,CAAC,CAAC;EACF,OAAO,CAACT,eAAe,EAAEO,aAAa,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}