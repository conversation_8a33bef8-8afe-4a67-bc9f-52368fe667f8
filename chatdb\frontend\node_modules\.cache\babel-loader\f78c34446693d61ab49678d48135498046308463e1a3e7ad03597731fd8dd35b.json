{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nexport function pickClosable(context) {\n  if (!context) {\n    return undefined;\n  }\n  return {\n    closable: context.closable,\n    closeIcon: context.closeIcon\n  };\n}\n/** Convert `closable` and `closeIcon` to config object */\nfunction useClosableConfig(closableCollection) {\n  const {\n    closable,\n    closeIcon\n  } = closableCollection || {};\n  return React.useMemo(() => {\n    if (\n    // If `closable`, whatever rest be should be true\n    !closable && (closable === false || closeIcon === false || closeIcon === null)) {\n      return false;\n    }\n    if (closable === undefined && closeIcon === undefined) {\n      return null;\n    }\n    let closableConfig = {\n      closeIcon: typeof closeIcon !== 'boolean' && closeIcon !== null ? closeIcon : undefined\n    };\n    if (closable && typeof closable === 'object') {\n      closableConfig = Object.assign(Object.assign({}, closableConfig), closable);\n    }\n    return closableConfig;\n  }, [closable, closeIcon]);\n}\n/**\n * Assign object without `undefined` field. Will skip if is `false`.\n * This helps to handle both closableConfig or false\n */\nfunction assignWithoutUndefined() {\n  const target = {};\n  for (var _len = arguments.length, objList = new Array(_len), _key = 0; _key < _len; _key++) {\n    objList[_key] = arguments[_key];\n  }\n  objList.forEach(obj => {\n    if (obj) {\n      Object.keys(obj).forEach(key => {\n        if (obj[key] !== undefined) {\n          target[key] = obj[key];\n        }\n      });\n    }\n  });\n  return target;\n}\n/** Use same object to support `useMemo` optimization */\nconst EmptyFallbackCloseCollection = {};\nexport default function useClosable(propCloseCollection, contextCloseCollection) {\n  let fallbackCloseCollection = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : EmptyFallbackCloseCollection;\n  // Align the `props`, `context` `fallback` to config object first\n  const propCloseConfig = useClosableConfig(propCloseCollection);\n  const contextCloseConfig = useClosableConfig(contextCloseCollection);\n  const closeBtnIsDisabled = typeof propCloseConfig !== 'boolean' ? !!(propCloseConfig === null || propCloseConfig === void 0 ? void 0 : propCloseConfig.disabled) : false;\n  const mergedFallbackCloseCollection = React.useMemo(() => Object.assign({\n    closeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null)\n  }, fallbackCloseCollection), [fallbackCloseCollection]);\n  // Use fallback logic to fill the config\n  const mergedClosableConfig = React.useMemo(() => {\n    // ================ Props First ================\n    // Skip if prop is disabled\n    if (propCloseConfig === false) {\n      return false;\n    }\n    if (propCloseConfig) {\n      return assignWithoutUndefined(mergedFallbackCloseCollection, contextCloseConfig, propCloseConfig);\n    }\n    // =============== Context Second ==============\n    // Skip if context is disabled\n    if (contextCloseConfig === false) {\n      return false;\n    }\n    if (contextCloseConfig) {\n      return assignWithoutUndefined(mergedFallbackCloseCollection, contextCloseConfig);\n    }\n    // ============= Fallback Default ==============\n    return !mergedFallbackCloseCollection.closable ? false : mergedFallbackCloseCollection;\n  }, [propCloseConfig, contextCloseConfig, mergedFallbackCloseCollection]);\n  // Calculate the final closeIcon\n  return React.useMemo(() => {\n    if (mergedClosableConfig === false) {\n      return [false, null, closeBtnIsDisabled];\n    }\n    const {\n      closeIconRender\n    } = mergedFallbackCloseCollection;\n    const {\n      closeIcon\n    } = mergedClosableConfig;\n    let mergedCloseIcon = closeIcon;\n    if (mergedCloseIcon !== null && mergedCloseIcon !== undefined) {\n      // Wrap the closeIcon if needed\n      if (closeIconRender) {\n        mergedCloseIcon = closeIconRender(closeIcon);\n      }\n      // Wrap the closeIcon with aria props\n      const ariaProps = pickAttrs(mergedClosableConfig, true);\n      if (Object.keys(ariaProps).length) {\n        mergedCloseIcon = /*#__PURE__*/React.isValidElement(mergedCloseIcon) ? (/*#__PURE__*/React.cloneElement(mergedCloseIcon, ariaProps)) : (/*#__PURE__*/React.createElement(\"span\", Object.assign({}, ariaProps), mergedCloseIcon));\n      }\n    }\n    return [true, mergedCloseIcon, closeBtnIsDisabled];\n  }, [mergedClosableConfig, mergedFallbackCloseCollection]);\n}", "map": {"version": 3, "names": ["React", "CloseOutlined", "pickAttrs", "pickClosable", "context", "undefined", "closable", "closeIcon", "useClosableConfig", "closableCollection", "useMemo", "closableConfig", "Object", "assign", "assignWithoutUndefined", "target", "_len", "arguments", "length", "objList", "Array", "_key", "for<PERSON>ach", "obj", "keys", "key", "EmptyFallbackCloseCollection", "useClosable", "propCloseCollection", "contextCloseCollection", "fallbackCloseCollection", "propCloseConfig", "contextCloseConfig", "closeBtnIsDisabled", "disabled", "mergedFallbackCloseCollection", "createElement", "mergedClosableConfig", "closeIconRender", "mergedCloseIcon", "ariaProps", "isValidElement", "cloneElement"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/_util/hooks/useClosable.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport CloseOutlined from \"@ant-design/icons/es/icons/CloseOutlined\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nexport function pickClosable(context) {\n  if (!context) {\n    return undefined;\n  }\n  return {\n    closable: context.closable,\n    closeIcon: context.closeIcon\n  };\n}\n/** Convert `closable` and `closeIcon` to config object */\nfunction useClosableConfig(closableCollection) {\n  const {\n    closable,\n    closeIcon\n  } = closableCollection || {};\n  return React.useMemo(() => {\n    if (\n    // If `closable`, whatever rest be should be true\n    !closable && (closable === false || closeIcon === false || closeIcon === null)) {\n      return false;\n    }\n    if (closable === undefined && closeIcon === undefined) {\n      return null;\n    }\n    let closableConfig = {\n      closeIcon: typeof closeIcon !== 'boolean' && closeIcon !== null ? closeIcon : undefined\n    };\n    if (closable && typeof closable === 'object') {\n      closableConfig = Object.assign(Object.assign({}, closableConfig), closable);\n    }\n    return closableConfig;\n  }, [closable, closeIcon]);\n}\n/**\n * Assign object without `undefined` field. Will skip if is `false`.\n * This helps to handle both closableConfig or false\n */\nfunction assignWithoutUndefined() {\n  const target = {};\n  for (var _len = arguments.length, objList = new Array(_len), _key = 0; _key < _len; _key++) {\n    objList[_key] = arguments[_key];\n  }\n  objList.forEach(obj => {\n    if (obj) {\n      Object.keys(obj).forEach(key => {\n        if (obj[key] !== undefined) {\n          target[key] = obj[key];\n        }\n      });\n    }\n  });\n  return target;\n}\n/** Use same object to support `useMemo` optimization */\nconst EmptyFallbackCloseCollection = {};\nexport default function useClosable(propCloseCollection, contextCloseCollection) {\n  let fallbackCloseCollection = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : EmptyFallbackCloseCollection;\n  // Align the `props`, `context` `fallback` to config object first\n  const propCloseConfig = useClosableConfig(propCloseCollection);\n  const contextCloseConfig = useClosableConfig(contextCloseCollection);\n  const closeBtnIsDisabled = typeof propCloseConfig !== 'boolean' ? !!(propCloseConfig === null || propCloseConfig === void 0 ? void 0 : propCloseConfig.disabled) : false;\n  const mergedFallbackCloseCollection = React.useMemo(() => Object.assign({\n    closeIcon: /*#__PURE__*/React.createElement(CloseOutlined, null)\n  }, fallbackCloseCollection), [fallbackCloseCollection]);\n  // Use fallback logic to fill the config\n  const mergedClosableConfig = React.useMemo(() => {\n    // ================ Props First ================\n    // Skip if prop is disabled\n    if (propCloseConfig === false) {\n      return false;\n    }\n    if (propCloseConfig) {\n      return assignWithoutUndefined(mergedFallbackCloseCollection, contextCloseConfig, propCloseConfig);\n    }\n    // =============== Context Second ==============\n    // Skip if context is disabled\n    if (contextCloseConfig === false) {\n      return false;\n    }\n    if (contextCloseConfig) {\n      return assignWithoutUndefined(mergedFallbackCloseCollection, contextCloseConfig);\n    }\n    // ============= Fallback Default ==============\n    return !mergedFallbackCloseCollection.closable ? false : mergedFallbackCloseCollection;\n  }, [propCloseConfig, contextCloseConfig, mergedFallbackCloseCollection]);\n  // Calculate the final closeIcon\n  return React.useMemo(() => {\n    if (mergedClosableConfig === false) {\n      return [false, null, closeBtnIsDisabled];\n    }\n    const {\n      closeIconRender\n    } = mergedFallbackCloseCollection;\n    const {\n      closeIcon\n    } = mergedClosableConfig;\n    let mergedCloseIcon = closeIcon;\n    if (mergedCloseIcon !== null && mergedCloseIcon !== undefined) {\n      // Wrap the closeIcon if needed\n      if (closeIconRender) {\n        mergedCloseIcon = closeIconRender(closeIcon);\n      }\n      // Wrap the closeIcon with aria props\n      const ariaProps = pickAttrs(mergedClosableConfig, true);\n      if (Object.keys(ariaProps).length) {\n        mergedCloseIcon = /*#__PURE__*/React.isValidElement(mergedCloseIcon) ? (/*#__PURE__*/React.cloneElement(mergedCloseIcon, ariaProps)) : (/*#__PURE__*/React.createElement(\"span\", Object.assign({}, ariaProps), mergedCloseIcon));\n      }\n    }\n    return [true, mergedCloseIcon, closeBtnIsDisabled];\n  }, [mergedClosableConfig, mergedFallbackCloseCollection]);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EACpC,IAAI,CAACA,OAAO,EAAE;IACZ,OAAOC,SAAS;EAClB;EACA,OAAO;IACLC,QAAQ,EAAEF,OAAO,CAACE,QAAQ;IAC1BC,SAAS,EAAEH,OAAO,CAACG;EACrB,CAAC;AACH;AACA;AACA,SAASC,iBAAiBA,CAACC,kBAAkB,EAAE;EAC7C,MAAM;IACJH,QAAQ;IACRC;EACF,CAAC,GAAGE,kBAAkB,IAAI,CAAC,CAAC;EAC5B,OAAOT,KAAK,CAACU,OAAO,CAAC,MAAM;IACzB;IACA;IACA,CAACJ,QAAQ,KAAKA,QAAQ,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,IAAI,CAAC,EAAE;MAC9E,OAAO,KAAK;IACd;IACA,IAAID,QAAQ,KAAKD,SAAS,IAAIE,SAAS,KAAKF,SAAS,EAAE;MACrD,OAAO,IAAI;IACb;IACA,IAAIM,cAAc,GAAG;MACnBJ,SAAS,EAAE,OAAOA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,IAAI,GAAGA,SAAS,GAAGF;IAChF,CAAC;IACD,IAAIC,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC5CK,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,cAAc,CAAC,EAAEL,QAAQ,CAAC;IAC7E;IACA,OAAOK,cAAc;EACvB,CAAC,EAAE,CAACL,QAAQ,EAAEC,SAAS,CAAC,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,SAASO,sBAAsBA,CAAA,EAAG;EAChC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,OAAO,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IAC1FF,OAAO,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EACjC;EACAF,OAAO,CAACG,OAAO,CAACC,GAAG,IAAI;IACrB,IAAIA,GAAG,EAAE;MACPX,MAAM,CAACY,IAAI,CAACD,GAAG,CAAC,CAACD,OAAO,CAACG,GAAG,IAAI;QAC9B,IAAIF,GAAG,CAACE,GAAG,CAAC,KAAKpB,SAAS,EAAE;UAC1BU,MAAM,CAACU,GAAG,CAAC,GAAGF,GAAG,CAACE,GAAG,CAAC;QACxB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOV,MAAM;AACf;AACA;AACA,MAAMW,4BAA4B,GAAG,CAAC,CAAC;AACvC,eAAe,SAASC,WAAWA,CAACC,mBAAmB,EAAEC,sBAAsB,EAAE;EAC/E,IAAIC,uBAAuB,GAAGb,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKZ,SAAS,GAAGY,SAAS,CAAC,CAAC,CAAC,GAAGS,4BAA4B;EAC9H;EACA,MAAMK,eAAe,GAAGvB,iBAAiB,CAACoB,mBAAmB,CAAC;EAC9D,MAAMI,kBAAkB,GAAGxB,iBAAiB,CAACqB,sBAAsB,CAAC;EACpE,MAAMI,kBAAkB,GAAG,OAAOF,eAAe,KAAK,SAAS,GAAG,CAAC,EAAEA,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACG,QAAQ,CAAC,GAAG,KAAK;EACxK,MAAMC,6BAA6B,GAAGnC,KAAK,CAACU,OAAO,CAAC,MAAME,MAAM,CAACC,MAAM,CAAC;IACtEN,SAAS,EAAE,aAAaP,KAAK,CAACoC,aAAa,CAACnC,aAAa,EAAE,IAAI;EACjE,CAAC,EAAE6B,uBAAuB,CAAC,EAAE,CAACA,uBAAuB,CAAC,CAAC;EACvD;EACA,MAAMO,oBAAoB,GAAGrC,KAAK,CAACU,OAAO,CAAC,MAAM;IAC/C;IACA;IACA,IAAIqB,eAAe,KAAK,KAAK,EAAE;MAC7B,OAAO,KAAK;IACd;IACA,IAAIA,eAAe,EAAE;MACnB,OAAOjB,sBAAsB,CAACqB,6BAA6B,EAAEH,kBAAkB,EAAED,eAAe,CAAC;IACnG;IACA;IACA;IACA,IAAIC,kBAAkB,KAAK,KAAK,EAAE;MAChC,OAAO,KAAK;IACd;IACA,IAAIA,kBAAkB,EAAE;MACtB,OAAOlB,sBAAsB,CAACqB,6BAA6B,EAAEH,kBAAkB,CAAC;IAClF;IACA;IACA,OAAO,CAACG,6BAA6B,CAAC7B,QAAQ,GAAG,KAAK,GAAG6B,6BAA6B;EACxF,CAAC,EAAE,CAACJ,eAAe,EAAEC,kBAAkB,EAAEG,6BAA6B,CAAC,CAAC;EACxE;EACA,OAAOnC,KAAK,CAACU,OAAO,CAAC,MAAM;IACzB,IAAI2B,oBAAoB,KAAK,KAAK,EAAE;MAClC,OAAO,CAAC,KAAK,EAAE,IAAI,EAAEJ,kBAAkB,CAAC;IAC1C;IACA,MAAM;MACJK;IACF,CAAC,GAAGH,6BAA6B;IACjC,MAAM;MACJ5B;IACF,CAAC,GAAG8B,oBAAoB;IACxB,IAAIE,eAAe,GAAGhC,SAAS;IAC/B,IAAIgC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAKlC,SAAS,EAAE;MAC7D;MACA,IAAIiC,eAAe,EAAE;QACnBC,eAAe,GAAGD,eAAe,CAAC/B,SAAS,CAAC;MAC9C;MACA;MACA,MAAMiC,SAAS,GAAGtC,SAAS,CAACmC,oBAAoB,EAAE,IAAI,CAAC;MACvD,IAAIzB,MAAM,CAACY,IAAI,CAACgB,SAAS,CAAC,CAACtB,MAAM,EAAE;QACjCqB,eAAe,GAAG,aAAavC,KAAK,CAACyC,cAAc,CAACF,eAAe,CAAC,IAAI,aAAavC,KAAK,CAAC0C,YAAY,CAACH,eAAe,EAAEC,SAAS,CAAC,KAAK,aAAaxC,KAAK,CAACoC,aAAa,CAAC,MAAM,EAAExB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE2B,SAAS,CAAC,EAAED,eAAe,CAAC,CAAC;MAClO;IACF;IACA,OAAO,CAAC,IAAI,EAAEA,eAAe,EAAEN,kBAAkB,CAAC;EACpD,CAAC,EAAE,CAACI,oBAAoB,EAAEF,6BAA6B,CAAC,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}