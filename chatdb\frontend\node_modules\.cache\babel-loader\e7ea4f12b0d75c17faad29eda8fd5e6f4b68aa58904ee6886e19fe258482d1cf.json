{"ast": null, "code": "'use strict';\n\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\n\n// V8 ~ Chrome 36-\n// https://bugs.chromium.org/p/v8/issues/detail?id=3334\nmodule.exports = DESCRIPTORS && fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(function () {/* empty */}, 'prototype', {\n    value: 42,\n    writable: false\n  }).prototype !== 42;\n});", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "fails", "module", "exports", "Object", "defineProperty", "value", "writable", "prototype"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@pmmmwh/react-refresh-webpack-plugin/node_modules/core-js-pure/internals/v8-prototype-define-bug.js"], "sourcesContent": ["'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\n\n// V8 ~ Chrome 36-\n// https://bugs.chromium.org/p/v8/issues/detail?id=3334\nmodule.exports = DESCRIPTORS && fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(function () { /* empty */ }, 'prototype', {\n    value: 42,\n    writable: false\n  }).prototype !== 42;\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACrD,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAoB,CAAC;;AAEzC;AACA;AACAE,MAAM,CAACC,OAAO,GAAGJ,WAAW,IAAIE,KAAK,CAAC,YAAY;EAChD;EACA,OAAOG,MAAM,CAACC,cAAc,CAAC,YAAY,CAAE,YAAa,EAAE,WAAW,EAAE;IACrEC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAACC,SAAS,KAAK,EAAE;AACrB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}