{"ast": null, "code": "import defaultLocale from '../locale/en_US';\nlet runtimeLocale = Object.assign({}, defaultLocale.Modal);\nlet localeList = [];\nconst generateLocale = () => localeList.reduce((merged, locale) => Object.assign(Object.assign({}, merged), locale), defaultLocale.Modal);\nexport function changeConfirmLocale(newLocale) {\n  if (newLocale) {\n    const cloneLocale = Object.assign({}, newLocale);\n    localeList.push(cloneLocale);\n    runtimeLocale = generateLocale();\n    return () => {\n      localeList = localeList.filter(locale => locale !== cloneLocale);\n      runtimeLocale = generateLocale();\n    };\n  }\n  runtimeLocale = Object.assign({}, defaultLocale.Modal);\n}\nexport function getConfirmLocale() {\n  return runtimeLocale;\n}", "map": {"version": 3, "names": ["defaultLocale", "runtimeLocale", "Object", "assign", "Modal", "localeList", "generateLocale", "reduce", "merged", "locale", "changeConfirmLocale", "newLocale", "cloneLocale", "push", "filter", "getConfirmLocale"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/modal/locale.js"], "sourcesContent": ["import defaultLocale from '../locale/en_US';\nlet runtimeLocale = Object.assign({}, defaultLocale.Modal);\nlet localeList = [];\nconst generateLocale = () => localeList.reduce((merged, locale) => Object.assign(Object.assign({}, merged), locale), defaultLocale.Modal);\nexport function changeConfirmLocale(newLocale) {\n  if (newLocale) {\n    const cloneLocale = Object.assign({}, newLocale);\n    localeList.push(cloneLocale);\n    runtimeLocale = generateLocale();\n    return () => {\n      localeList = localeList.filter(locale => locale !== cloneLocale);\n      runtimeLocale = generateLocale();\n    };\n  }\n  runtimeLocale = Object.assign({}, defaultLocale.Modal);\n}\nexport function getConfirmLocale() {\n  return runtimeLocale;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,IAAIC,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,aAAa,CAACI,KAAK,CAAC;AAC1D,IAAIC,UAAU,GAAG,EAAE;AACnB,MAAMC,cAAc,GAAGA,CAAA,KAAMD,UAAU,CAACE,MAAM,CAAC,CAACC,MAAM,EAAEC,MAAM,KAAKP,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEK,MAAM,CAAC,EAAEC,MAAM,CAAC,EAAET,aAAa,CAACI,KAAK,CAAC;AACzI,OAAO,SAASM,mBAAmBA,CAACC,SAAS,EAAE;EAC7C,IAAIA,SAAS,EAAE;IACb,MAAMC,WAAW,GAAGV,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEQ,SAAS,CAAC;IAChDN,UAAU,CAACQ,IAAI,CAACD,WAAW,CAAC;IAC5BX,aAAa,GAAGK,cAAc,CAAC,CAAC;IAChC,OAAO,MAAM;MACXD,UAAU,GAAGA,UAAU,CAACS,MAAM,CAACL,MAAM,IAAIA,MAAM,KAAKG,WAAW,CAAC;MAChEX,aAAa,GAAGK,cAAc,CAAC,CAAC;IAClC,CAAC;EACH;EACAL,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,aAAa,CAACI,KAAK,CAAC;AACxD;AACA,OAAO,SAASW,gBAAgBA,CAAA,EAAG;EACjC,OAAOd,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}