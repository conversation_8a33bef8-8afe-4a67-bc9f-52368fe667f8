{"ast": null, "code": "import * as React from 'react';\nimport { renderColumnTitle } from '../util';\nconst fillTitle = (columns, columnTitleProps) => {\n  const finalColumns = columns.map(column => {\n    const cloneColumn = Object.assign({}, column);\n    cloneColumn.title = renderColumnTitle(column.title, columnTitleProps);\n    if ('children' in cloneColumn) {\n      cloneColumn.children = fillTitle(cloneColumn.children, columnTitleProps);\n    }\n    return cloneColumn;\n  });\n  return finalColumns;\n};\nconst useTitleColumns = columnTitleProps => {\n  const filledColumns = React.useCallback(columns => fillTitle(columns, columnTitleProps), [columnTitleProps]);\n  return [filledColumns];\n};\nexport default useTitleColumns;", "map": {"version": 3, "names": ["React", "renderColumnTitle", "fill<PERSON>itle", "columns", "columnTitleProps", "finalColumns", "map", "column", "cloneColumn", "Object", "assign", "title", "children", "useTitleColumns", "filledColumns", "useCallback"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/table/hooks/useTitleColumns.js"], "sourcesContent": ["import * as React from 'react';\nimport { renderColumnTitle } from '../util';\nconst fillTitle = (columns, columnTitleProps) => {\n  const finalColumns = columns.map(column => {\n    const cloneColumn = Object.assign({}, column);\n    cloneColumn.title = renderColumnTitle(column.title, columnTitleProps);\n    if ('children' in cloneColumn) {\n      cloneColumn.children = fillTitle(cloneColumn.children, columnTitleProps);\n    }\n    return cloneColumn;\n  });\n  return finalColumns;\n};\nconst useTitleColumns = columnTitleProps => {\n  const filledColumns = React.useCallback(columns => fillTitle(columns, columnTitleProps), [columnTitleProps]);\n  return [filledColumns];\n};\nexport default useTitleColumns;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,SAAS;AAC3C,MAAMC,SAAS,GAAGA,CAACC,OAAO,EAAEC,gBAAgB,KAAK;EAC/C,MAAMC,YAAY,GAAGF,OAAO,CAACG,GAAG,CAACC,MAAM,IAAI;IACzC,MAAMC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;IAC7CC,WAAW,CAACG,KAAK,GAAGV,iBAAiB,CAACM,MAAM,CAACI,KAAK,EAAEP,gBAAgB,CAAC;IACrE,IAAI,UAAU,IAAII,WAAW,EAAE;MAC7BA,WAAW,CAACI,QAAQ,GAAGV,SAAS,CAACM,WAAW,CAACI,QAAQ,EAAER,gBAAgB,CAAC;IAC1E;IACA,OAAOI,WAAW;EACpB,CAAC,CAAC;EACF,OAAOH,YAAY;AACrB,CAAC;AACD,MAAMQ,eAAe,GAAGT,gBAAgB,IAAI;EAC1C,MAAMU,aAAa,GAAGd,KAAK,CAACe,WAAW,CAACZ,OAAO,IAAID,SAAS,CAACC,OAAO,EAAEC,gBAAgB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAC5G,OAAO,CAACU,aAAa,CAAC;AACxB,CAAC;AACD,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}