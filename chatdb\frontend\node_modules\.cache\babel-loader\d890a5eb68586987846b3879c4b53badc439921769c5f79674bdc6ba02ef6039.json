{"ast": null, "code": "\"use client\";\n\nimport React from 'react';\nimport { generateColor } from '../util';\nconst ColorClear = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const handleClick = () => {\n    if (onChange && value && !value.cleared) {\n      const hsba = value.toHsb();\n      hsba.a = 0;\n      const genColor = generateColor(hsba);\n      genColor.cleared = true;\n      onChange(genColor);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-clear`,\n    onClick: handleClick\n  });\n};\nexport default ColorClear;", "map": {"version": 3, "names": ["React", "generateColor", "ColorClear", "_ref", "prefixCls", "value", "onChange", "handleClick", "cleared", "hsba", "toHsb", "a", "genColor", "createElement", "className", "onClick"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/color-picker/components/ColorClear.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { generateColor } from '../util';\nconst ColorClear = _ref => {\n  let {\n    prefixCls,\n    value,\n    onChange\n  } = _ref;\n  const handleClick = () => {\n    if (onChange && value && !value.cleared) {\n      const hsba = value.toHsb();\n      hsba.a = 0;\n      const genColor = generateColor(hsba);\n      genColor.cleared = true;\n      onChange(genColor);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-clear`,\n    onClick: handleClick\n  });\n};\nexport default ColorClear;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,SAAS;AACvC,MAAMC,UAAU,GAAGC,IAAI,IAAI;EACzB,IAAI;IACFC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAID,QAAQ,IAAID,KAAK,IAAI,CAACA,KAAK,CAACG,OAAO,EAAE;MACvC,MAAMC,IAAI,GAAGJ,KAAK,CAACK,KAAK,CAAC,CAAC;MAC1BD,IAAI,CAACE,CAAC,GAAG,CAAC;MACV,MAAMC,QAAQ,GAAGX,aAAa,CAACQ,IAAI,CAAC;MACpCG,QAAQ,CAACJ,OAAO,GAAG,IAAI;MACvBF,QAAQ,CAACM,QAAQ,CAAC;IACpB;EACF,CAAC;EACD,OAAO,aAAaZ,KAAK,CAACa,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,GAAGV,SAAS,QAAQ;IAC/BW,OAAO,EAAER;EACX,CAAC,CAAC;AACJ,CAAC;AACD,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}