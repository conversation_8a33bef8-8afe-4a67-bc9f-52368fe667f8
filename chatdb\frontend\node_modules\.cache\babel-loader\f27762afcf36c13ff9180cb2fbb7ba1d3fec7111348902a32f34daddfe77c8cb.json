{"ast": null, "code": "import React from 'react';\nexport function getPtg(str) {\n  return Number(str.slice(0, -1)) / 100;\n}\nfunction isPtg(itemSize) {\n  return typeof itemSize === 'string' && itemSize.endsWith('%');\n}\n/**\n * Save the size state.\n * Align the size into flex percentage base.\n */\nexport default function useSizes(items, containerSize) {\n  const propSizes = items.map(item => item.size);\n  const itemsCount = items.length;\n  const mergedContainerSize = containerSize || 0;\n  const ptg2px = ptg => ptg * mergedContainerSize;\n  // We do not need care the size state match the `items` length in `useState`.\n  // It will calculate later.\n  const [innerSizes, setInnerSizes] = React.useState(() => items.map(item => item.defaultSize));\n  const sizes = React.useMemo(() => {\n    var _a;\n    const mergedSizes = [];\n    for (let i = 0; i < itemsCount; i += 1) {\n      mergedSizes[i] = (_a = propSizes[i]) !== null && _a !== void 0 ? _a : innerSizes[i];\n    }\n    return mergedSizes;\n  }, [itemsCount, innerSizes, propSizes]);\n  // Post handle the size. Will do:\n  // 1. Convert all the px into percentage if not empty.\n  // 2. Get rest percentage for exist percentage.\n  // 3. Fill the rest percentage into empty item.\n  const postPercentSizes = React.useMemo(() => {\n    let ptgList = [];\n    let emptyCount = 0;\n    // Fill default percentage\n    for (let i = 0; i < itemsCount; i += 1) {\n      const itemSize = sizes[i];\n      if (isPtg(itemSize)) {\n        ptgList[i] = getPtg(itemSize);\n      } else if (itemSize || itemSize === 0) {\n        const num = Number(itemSize);\n        if (!Number.isNaN(num)) {\n          ptgList[i] = num / mergedContainerSize;\n        }\n      } else {\n        emptyCount += 1;\n        ptgList[i] = undefined;\n      }\n    }\n    const totalPtg = ptgList.reduce((acc, ptg) => acc + (ptg || 0), 0);\n    if (totalPtg > 1 || !emptyCount) {\n      // If total percentage is larger than 1, we will scale it down.\n      const scale = 1 / totalPtg;\n      ptgList = ptgList.map(ptg => ptg === undefined ? 0 : ptg * scale);\n    } else {\n      // If total percentage is smaller than 1, we will fill the rest.\n      const avgRest = (1 - totalPtg) / emptyCount;\n      ptgList = ptgList.map(ptg => ptg === undefined ? avgRest : ptg);\n    }\n    return ptgList;\n  }, [sizes, mergedContainerSize]);\n  const postPxSizes = React.useMemo(() => postPercentSizes.map(ptg2px), [postPercentSizes, mergedContainerSize]);\n  const postPercentMinSizes = React.useMemo(() => items.map(item => {\n    if (isPtg(item.min)) {\n      return getPtg(item.min);\n    }\n    return (item.min || 0) / mergedContainerSize;\n  }), [items, mergedContainerSize]);\n  const postPercentMaxSizes = React.useMemo(() => items.map(item => {\n    if (isPtg(item.max)) {\n      return getPtg(item.max);\n    }\n    return (item.max || mergedContainerSize) / mergedContainerSize;\n  }), [items, mergedContainerSize]);\n  // If ssr, we will use the size from developer config first.\n  const panelSizes = React.useMemo(() => containerSize ? postPxSizes : sizes, [postPxSizes, containerSize]);\n  return [panelSizes, postPxSizes, postPercentSizes, postPercentMinSizes, postPercentMaxSizes, setInnerSizes];\n}", "map": {"version": 3, "names": ["React", "getPtg", "str", "Number", "slice", "isPtg", "itemSize", "endsWith", "useSizes", "items", "containerSize", "propSizes", "map", "item", "size", "itemsCount", "length", "mergedContainerSize", "ptg2px", "ptg", "innerSizes", "setInnerSizes", "useState", "defaultSize", "sizes", "useMemo", "_a", "mergedSizes", "i", "postPercentSizes", "ptgList", "emptyCount", "num", "isNaN", "undefined", "totalPtg", "reduce", "acc", "scale", "avgRest", "postPxSizes", "postPercentMinSizes", "min", "postPercentMaxSizes", "max", "panelSizes"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/splitter/hooks/useSizes.js"], "sourcesContent": ["import React from 'react';\nexport function getPtg(str) {\n  return Number(str.slice(0, -1)) / 100;\n}\nfunction isPtg(itemSize) {\n  return typeof itemSize === 'string' && itemSize.endsWith('%');\n}\n/**\n * Save the size state.\n * Align the size into flex percentage base.\n */\nexport default function useSizes(items, containerSize) {\n  const propSizes = items.map(item => item.size);\n  const itemsCount = items.length;\n  const mergedContainerSize = containerSize || 0;\n  const ptg2px = ptg => ptg * mergedContainerSize;\n  // We do not need care the size state match the `items` length in `useState`.\n  // It will calculate later.\n  const [innerSizes, setInnerSizes] = React.useState(() => items.map(item => item.defaultSize));\n  const sizes = React.useMemo(() => {\n    var _a;\n    const mergedSizes = [];\n    for (let i = 0; i < itemsCount; i += 1) {\n      mergedSizes[i] = (_a = propSizes[i]) !== null && _a !== void 0 ? _a : innerSizes[i];\n    }\n    return mergedSizes;\n  }, [itemsCount, innerSizes, propSizes]);\n  // Post handle the size. Will do:\n  // 1. Convert all the px into percentage if not empty.\n  // 2. Get rest percentage for exist percentage.\n  // 3. Fill the rest percentage into empty item.\n  const postPercentSizes = React.useMemo(() => {\n    let ptgList = [];\n    let emptyCount = 0;\n    // Fill default percentage\n    for (let i = 0; i < itemsCount; i += 1) {\n      const itemSize = sizes[i];\n      if (isPtg(itemSize)) {\n        ptgList[i] = getPtg(itemSize);\n      } else if (itemSize || itemSize === 0) {\n        const num = Number(itemSize);\n        if (!Number.isNaN(num)) {\n          ptgList[i] = num / mergedContainerSize;\n        }\n      } else {\n        emptyCount += 1;\n        ptgList[i] = undefined;\n      }\n    }\n    const totalPtg = ptgList.reduce((acc, ptg) => acc + (ptg || 0), 0);\n    if (totalPtg > 1 || !emptyCount) {\n      // If total percentage is larger than 1, we will scale it down.\n      const scale = 1 / totalPtg;\n      ptgList = ptgList.map(ptg => ptg === undefined ? 0 : ptg * scale);\n    } else {\n      // If total percentage is smaller than 1, we will fill the rest.\n      const avgRest = (1 - totalPtg) / emptyCount;\n      ptgList = ptgList.map(ptg => ptg === undefined ? avgRest : ptg);\n    }\n    return ptgList;\n  }, [sizes, mergedContainerSize]);\n  const postPxSizes = React.useMemo(() => postPercentSizes.map(ptg2px), [postPercentSizes, mergedContainerSize]);\n  const postPercentMinSizes = React.useMemo(() => items.map(item => {\n    if (isPtg(item.min)) {\n      return getPtg(item.min);\n    }\n    return (item.min || 0) / mergedContainerSize;\n  }), [items, mergedContainerSize]);\n  const postPercentMaxSizes = React.useMemo(() => items.map(item => {\n    if (isPtg(item.max)) {\n      return getPtg(item.max);\n    }\n    return (item.max || mergedContainerSize) / mergedContainerSize;\n  }), [items, mergedContainerSize]);\n  // If ssr, we will use the size from developer config first.\n  const panelSizes = React.useMemo(() => containerSize ? postPxSizes : sizes, [postPxSizes, containerSize]);\n  return [panelSizes, postPxSizes, postPercentSizes, postPercentMinSizes, postPercentMaxSizes, setInnerSizes];\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,SAASC,MAAMA,CAACC,GAAG,EAAE;EAC1B,OAAOC,MAAM,CAACD,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;AACvC;AACA,SAASC,KAAKA,CAACC,QAAQ,EAAE;EACvB,OAAO,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,GAAG,CAAC;AAC/D;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,KAAK,EAAEC,aAAa,EAAE;EACrD,MAAMC,SAAS,GAAGF,KAAK,CAACG,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC;EAC9C,MAAMC,UAAU,GAAGN,KAAK,CAACO,MAAM;EAC/B,MAAMC,mBAAmB,GAAGP,aAAa,IAAI,CAAC;EAC9C,MAAMQ,MAAM,GAAGC,GAAG,IAAIA,GAAG,GAAGF,mBAAmB;EAC/C;EACA;EACA,MAAM,CAACG,UAAU,EAAEC,aAAa,CAAC,GAAGrB,KAAK,CAACsB,QAAQ,CAAC,MAAMb,KAAK,CAACG,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACU,WAAW,CAAC,CAAC;EAC7F,MAAMC,KAAK,GAAGxB,KAAK,CAACyB,OAAO,CAAC,MAAM;IAChC,IAAIC,EAAE;IACN,MAAMC,WAAW,GAAG,EAAE;IACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,UAAU,EAAEa,CAAC,IAAI,CAAC,EAAE;MACtCD,WAAW,CAACC,CAAC,CAAC,GAAG,CAACF,EAAE,GAAGf,SAAS,CAACiB,CAAC,CAAC,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGN,UAAU,CAACQ,CAAC,CAAC;IACrF;IACA,OAAOD,WAAW;EACpB,CAAC,EAAE,CAACZ,UAAU,EAAEK,UAAU,EAAET,SAAS,CAAC,CAAC;EACvC;EACA;EACA;EACA;EACA,MAAMkB,gBAAgB,GAAG7B,KAAK,CAACyB,OAAO,CAAC,MAAM;IAC3C,IAAIK,OAAO,GAAG,EAAE;IAChB,IAAIC,UAAU,GAAG,CAAC;IAClB;IACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,UAAU,EAAEa,CAAC,IAAI,CAAC,EAAE;MACtC,MAAMtB,QAAQ,GAAGkB,KAAK,CAACI,CAAC,CAAC;MACzB,IAAIvB,KAAK,CAACC,QAAQ,CAAC,EAAE;QACnBwB,OAAO,CAACF,CAAC,CAAC,GAAG3B,MAAM,CAACK,QAAQ,CAAC;MAC/B,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,KAAK,CAAC,EAAE;QACrC,MAAM0B,GAAG,GAAG7B,MAAM,CAACG,QAAQ,CAAC;QAC5B,IAAI,CAACH,MAAM,CAAC8B,KAAK,CAACD,GAAG,CAAC,EAAE;UACtBF,OAAO,CAACF,CAAC,CAAC,GAAGI,GAAG,GAAGf,mBAAmB;QACxC;MACF,CAAC,MAAM;QACLc,UAAU,IAAI,CAAC;QACfD,OAAO,CAACF,CAAC,CAAC,GAAGM,SAAS;MACxB;IACF;IACA,MAAMC,QAAQ,GAAGL,OAAO,CAACM,MAAM,CAAC,CAACC,GAAG,EAAElB,GAAG,KAAKkB,GAAG,IAAIlB,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAClE,IAAIgB,QAAQ,GAAG,CAAC,IAAI,CAACJ,UAAU,EAAE;MAC/B;MACA,MAAMO,KAAK,GAAG,CAAC,GAAGH,QAAQ;MAC1BL,OAAO,GAAGA,OAAO,CAAClB,GAAG,CAACO,GAAG,IAAIA,GAAG,KAAKe,SAAS,GAAG,CAAC,GAAGf,GAAG,GAAGmB,KAAK,CAAC;IACnE,CAAC,MAAM;MACL;MACA,MAAMC,OAAO,GAAG,CAAC,CAAC,GAAGJ,QAAQ,IAAIJ,UAAU;MAC3CD,OAAO,GAAGA,OAAO,CAAClB,GAAG,CAACO,GAAG,IAAIA,GAAG,KAAKe,SAAS,GAAGK,OAAO,GAAGpB,GAAG,CAAC;IACjE;IACA,OAAOW,OAAO;EAChB,CAAC,EAAE,CAACN,KAAK,EAAEP,mBAAmB,CAAC,CAAC;EAChC,MAAMuB,WAAW,GAAGxC,KAAK,CAACyB,OAAO,CAAC,MAAMI,gBAAgB,CAACjB,GAAG,CAACM,MAAM,CAAC,EAAE,CAACW,gBAAgB,EAAEZ,mBAAmB,CAAC,CAAC;EAC9G,MAAMwB,mBAAmB,GAAGzC,KAAK,CAACyB,OAAO,CAAC,MAAMhB,KAAK,CAACG,GAAG,CAACC,IAAI,IAAI;IAChE,IAAIR,KAAK,CAACQ,IAAI,CAAC6B,GAAG,CAAC,EAAE;MACnB,OAAOzC,MAAM,CAACY,IAAI,CAAC6B,GAAG,CAAC;IACzB;IACA,OAAO,CAAC7B,IAAI,CAAC6B,GAAG,IAAI,CAAC,IAAIzB,mBAAmB;EAC9C,CAAC,CAAC,EAAE,CAACR,KAAK,EAAEQ,mBAAmB,CAAC,CAAC;EACjC,MAAM0B,mBAAmB,GAAG3C,KAAK,CAACyB,OAAO,CAAC,MAAMhB,KAAK,CAACG,GAAG,CAACC,IAAI,IAAI;IAChE,IAAIR,KAAK,CAACQ,IAAI,CAAC+B,GAAG,CAAC,EAAE;MACnB,OAAO3C,MAAM,CAACY,IAAI,CAAC+B,GAAG,CAAC;IACzB;IACA,OAAO,CAAC/B,IAAI,CAAC+B,GAAG,IAAI3B,mBAAmB,IAAIA,mBAAmB;EAChE,CAAC,CAAC,EAAE,CAACR,KAAK,EAAEQ,mBAAmB,CAAC,CAAC;EACjC;EACA,MAAM4B,UAAU,GAAG7C,KAAK,CAACyB,OAAO,CAAC,MAAMf,aAAa,GAAG8B,WAAW,GAAGhB,KAAK,EAAE,CAACgB,WAAW,EAAE9B,aAAa,CAAC,CAAC;EACzG,OAAO,CAACmC,UAAU,EAAEL,WAAW,EAAEX,gBAAgB,EAAEY,mBAAmB,EAAEE,mBAAmB,EAAEtB,aAAa,CAAC;AAC7G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}