{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SafetyCertificateOutlinedSvg from \"@ant-design/icons-svg/es/asn/SafetyCertificateOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SafetyCertificateOutlined = function SafetyCertificateOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SafetyCertificateOutlinedSvg\n  }));\n};\n\n/**![safety-certificate](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnptLTQwNS44LTIwMWMtMy00LjEtNy44LTYuNi0xMy02LjZIMzM2Yy02LjUgMC0xMC4zIDcuNC02LjUgMTIuN2wxMjYuNCAxNzRhMTYuMSAxNi4xIDAgMDAyNiAwbDIxMi42LTI5Mi43YzMuOC01LjMgMC0xMi43LTYuNS0xMi43aC01NS4yYy01LjEgMC0xMCAyLjUtMTMgNi42TDQ2OC45IDU0Mi40bC02NC43LTg5LjF6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SafetyCertificateOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SafetyCertificateOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "SafetyCertificateOutlinedSvg", "AntdIcon", "SafetyCertificateOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/@ant-design/icons/es/icons/SafetyCertificateOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport SafetyCertificateOutlinedSvg from \"@ant-design/icons-svg/es/asn/SafetyCertificateOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar SafetyCertificateOutlined = function SafetyCertificateOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: SafetyCertificateOutlinedSvg\n  }));\n};\n\n/**![safety-certificate](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnptLTQwNS44LTIwMWMtMy00LjEtNy44LTYuNi0xMy02LjZIMzM2Yy02LjUgMC0xMC4zIDcuNC02LjUgMTIuN2wxMjYuNCAxNzRhMTYuMSAxNi4xIDAgMDAyNiAwbDIxMi42LTI5Mi43YzMuOC01LjMgMC0xMi43LTYuNS0xMi43aC01NS4yYy01LjEgMC0xMCAyLjUtMTMgNi42TDQ2OC45IDU0Mi40bC02NC43LTg5LjF6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(SafetyCertificateOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'SafetyCertificateOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,4BAA4B,MAAM,wDAAwD;AACjG,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7E,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,yBAAyB,CAAC;AACtE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,2BAA2B;AACnD;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}