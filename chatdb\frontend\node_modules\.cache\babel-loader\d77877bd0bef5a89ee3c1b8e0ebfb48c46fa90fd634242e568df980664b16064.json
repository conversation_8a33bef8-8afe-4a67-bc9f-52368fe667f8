{"ast": null, "code": "'use strict';\n\nmodule.exports = gedcom;\ngedcom.displayName = 'gedcom';\ngedcom.aliases = [];\nfunction gedcom(Prism) {\n  Prism.languages.gedcom = {\n    'line-value': {\n      // Preceded by level, optional pointer, and tag\n      pattern: /(^[\\t ]*\\d+ +(?:@\\w[\\w!\"$%&'()*+,\\-./:;<=>?[\\\\\\]^`{|}~\\x80-\\xfe #]*@ +)?\\w+ ).+/m,\n      lookbehind: true,\n      inside: {\n        pointer: {\n          pattern: /^@\\w[\\w!\"$%&'()*+,\\-./:;<=>?[\\\\\\]^`{|}~\\x80-\\xfe #]*@$/,\n          alias: 'variable'\n        }\n      }\n    },\n    tag: {\n      // Preceded by level and optional pointer\n      pattern: /(^[\\t ]*\\d+ +(?:@\\w[\\w!\"$%&'()*+,\\-./:;<=>?[\\\\\\]^`{|}~\\x80-\\xfe #]*@ +)?)\\w+/m,\n      lookbehind: true,\n      alias: 'string'\n    },\n    level: {\n      pattern: /(^[\\t ]*)\\d+/m,\n      lookbehind: true,\n      alias: 'number'\n    },\n    pointer: {\n      pattern: /@\\w[\\w!\"$%&'()*+,\\-./:;<=>?[\\\\\\]^`{|}~\\x80-\\xfe #]*@/,\n      alias: 'variable'\n    }\n  };\n}", "map": {"version": 3, "names": ["module", "exports", "gedcom", "displayName", "aliases", "Prism", "languages", "pattern", "lookbehind", "inside", "pointer", "alias", "tag", "level"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/gedcom.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = gedcom\ngedcom.displayName = 'gedcom'\ngedcom.aliases = []\nfunction gedcom(Prism) {\n  Prism.languages.gedcom = {\n    'line-value': {\n      // Preceded by level, optional pointer, and tag\n      pattern:\n        /(^[\\t ]*\\d+ +(?:@\\w[\\w!\"$%&'()*+,\\-./:;<=>?[\\\\\\]^`{|}~\\x80-\\xfe #]*@ +)?\\w+ ).+/m,\n      lookbehind: true,\n      inside: {\n        pointer: {\n          pattern: /^@\\w[\\w!\"$%&'()*+,\\-./:;<=>?[\\\\\\]^`{|}~\\x80-\\xfe #]*@$/,\n          alias: 'variable'\n        }\n      }\n    },\n    tag: {\n      // Preceded by level and optional pointer\n      pattern:\n        /(^[\\t ]*\\d+ +(?:@\\w[\\w!\"$%&'()*+,\\-./:;<=>?[\\\\\\]^`{|}~\\x80-\\xfe #]*@ +)?)\\w+/m,\n      lookbehind: true,\n      alias: 'string'\n    },\n    level: {\n      pattern: /(^[\\t ]*)\\d+/m,\n      lookbehind: true,\n      alias: 'number'\n    },\n    pointer: {\n      pattern: /@\\w[\\w!\"$%&'()*+,\\-./:;<=>?[\\\\\\]^`{|}~\\x80-\\xfe #]*@/,\n      alias: 'variable'\n    }\n  }\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,MAAM;AACvBA,MAAM,CAACC,WAAW,GAAG,QAAQ;AAC7BD,MAAM,CAACE,OAAO,GAAG,EAAE;AACnB,SAASF,MAAMA,CAACG,KAAK,EAAE;EACrBA,KAAK,CAACC,SAAS,CAACJ,MAAM,GAAG;IACvB,YAAY,EAAE;MACZ;MACAK,OAAO,EACL,kFAAkF;MACpFC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNC,OAAO,EAAE;UACPH,OAAO,EAAE,wDAAwD;UACjEI,KAAK,EAAE;QACT;MACF;IACF,CAAC;IACDC,GAAG,EAAE;MACH;MACAL,OAAO,EACL,+EAA+E;MACjFC,UAAU,EAAE,IAAI;MAChBG,KAAK,EAAE;IACT,CAAC;IACDE,KAAK,EAAE;MACLN,OAAO,EAAE,eAAe;MACxBC,UAAU,EAAE,IAAI;MAChBG,KAAK,EAAE;IACT,CAAC;IACDD,OAAO,EAAE;MACPH,OAAO,EAAE,sDAAsD;MAC/DI,KAAK,EAAE;IACT;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}