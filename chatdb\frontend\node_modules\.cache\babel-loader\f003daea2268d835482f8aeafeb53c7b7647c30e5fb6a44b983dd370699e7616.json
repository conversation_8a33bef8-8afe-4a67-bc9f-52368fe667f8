{"ast": null, "code": "import * as React from 'react';\nconst useLazyKVMap = (data, childrenColumnName, getRowKey) => {\n  const mapCacheRef = React.useRef({});\n  function getRecordByKey(key) {\n    var _a;\n    if (!mapCacheRef.current || mapCacheRef.current.data !== data || mapCacheRef.current.childrenColumnName !== childrenColumnName || mapCacheRef.current.getRowKey !== getRowKey) {\n      const kvMap = new Map();\n      function dig(records) {\n        records.forEach((record, index) => {\n          const rowKey = getRowKey(record, index);\n          kvMap.set(rowKey, record);\n          if (record && typeof record === 'object' && childrenColumnName in record) {\n            dig(record[childrenColumnName] || []);\n          }\n        });\n      }\n      dig(data);\n      mapCacheRef.current = {\n        data,\n        childrenColumnName,\n        kvMap,\n        getRowKey\n      };\n    }\n    return (_a = mapCacheRef.current.kvMap) === null || _a === void 0 ? void 0 : _a.get(key);\n  }\n  return [getRecordByKey];\n};\nexport default useLazyKVMap;", "map": {"version": 3, "names": ["React", "useLazyKVMap", "data", "childrenColumnName", "getRowKey", "mapCacheRef", "useRef", "getRecordByKey", "key", "_a", "current", "kvMap", "Map", "dig", "records", "for<PERSON>ach", "record", "index", "<PERSON><PERSON><PERSON>", "set", "get"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/table/hooks/useLazyKVMap.js"], "sourcesContent": ["import * as React from 'react';\nconst useLazyKVMap = (data, childrenColumnName, getRowKey) => {\n  const mapCacheRef = React.useRef({});\n  function getRecordByKey(key) {\n    var _a;\n    if (!mapCacheRef.current || mapCacheRef.current.data !== data || mapCacheRef.current.childrenColumnName !== childrenColumnName || mapCacheRef.current.getRowKey !== getRowKey) {\n      const kvMap = new Map();\n      function dig(records) {\n        records.forEach((record, index) => {\n          const rowKey = getRowKey(record, index);\n          kvMap.set(rowKey, record);\n          if (record && typeof record === 'object' && childrenColumnName in record) {\n            dig(record[childrenColumnName] || []);\n          }\n        });\n      }\n      dig(data);\n      mapCacheRef.current = {\n        data,\n        childrenColumnName,\n        kvMap,\n        getRowKey\n      };\n    }\n    return (_a = mapCacheRef.current.kvMap) === null || _a === void 0 ? void 0 : _a.get(key);\n  }\n  return [getRecordByKey];\n};\nexport default useLazyKVMap;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,kBAAkB,EAAEC,SAAS,KAAK;EAC5D,MAAMC,WAAW,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EACpC,SAASC,cAAcA,CAACC,GAAG,EAAE;IAC3B,IAAIC,EAAE;IACN,IAAI,CAACJ,WAAW,CAACK,OAAO,IAAIL,WAAW,CAACK,OAAO,CAACR,IAAI,KAAKA,IAAI,IAAIG,WAAW,CAACK,OAAO,CAACP,kBAAkB,KAAKA,kBAAkB,IAAIE,WAAW,CAACK,OAAO,CAACN,SAAS,KAAKA,SAAS,EAAE;MAC7K,MAAMO,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;MACvB,SAASC,GAAGA,CAACC,OAAO,EAAE;QACpBA,OAAO,CAACC,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;UACjC,MAAMC,MAAM,GAAGd,SAAS,CAACY,MAAM,EAAEC,KAAK,CAAC;UACvCN,KAAK,CAACQ,GAAG,CAACD,MAAM,EAAEF,MAAM,CAAC;UACzB,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIb,kBAAkB,IAAIa,MAAM,EAAE;YACxEH,GAAG,CAACG,MAAM,CAACb,kBAAkB,CAAC,IAAI,EAAE,CAAC;UACvC;QACF,CAAC,CAAC;MACJ;MACAU,GAAG,CAACX,IAAI,CAAC;MACTG,WAAW,CAACK,OAAO,GAAG;QACpBR,IAAI;QACJC,kBAAkB;QAClBQ,KAAK;QACLP;MACF,CAAC;IACH;IACA,OAAO,CAACK,EAAE,GAAGJ,WAAW,CAACK,OAAO,CAACC,KAAK,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,GAAG,CAACZ,GAAG,CAAC;EAC1F;EACA,OAAO,CAACD,cAAc,CAAC;AACzB,CAAC;AACD,eAAeN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}