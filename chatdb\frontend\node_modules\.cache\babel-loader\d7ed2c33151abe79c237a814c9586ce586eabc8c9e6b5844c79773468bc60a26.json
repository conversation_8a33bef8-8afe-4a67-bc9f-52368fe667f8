{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\\\u667A\\u80FD\\u6570\\u636E\\u5206\\u6790\\u7CFB\\u7EDF\\\\chatdb\\\\frontend\\\\src\\\\pages\\\\ConnectionsPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, message, Popconfirm, Card, Typography } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, CheckCircleOutlined, DatabaseOutlined } from '@ant-design/icons';\nimport * as api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  Title\n} = Typography;\nconst ConnectionsPage = () => {\n  _s();\n  const [connections, setConnections] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingConnection, setEditingConnection] = useState(null);\n  const [discoveringSchema, setDiscoveringSchema] = useState(false);\n  const [form] = Form.useForm();\n  useEffect(() => {\n    fetchConnections();\n  }, []);\n  const fetchConnections = async () => {\n    setLoading(true);\n    try {\n      const response = await api.getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取连接失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const showModal = connection => {\n    setEditingConnection(connection || null);\n    form.resetFields();\n    if (connection) {\n      form.setFieldsValue({\n        name: connection.name,\n        db_type: connection.db_type,\n        host: connection.host,\n        port: connection.port,\n        username: connection.username,\n        database_name: connection.database_name\n      });\n    }\n    setModalVisible(true);\n  };\n\n  // 监听数据库类型变化，动态调整表单\n  const handleDbTypeChange = value => {\n    if (value === 'sqlite') {\n      // SQLite不需要这些字段，清空它们\n      form.setFieldsValue({\n        host: undefined,\n        port: undefined,\n        username: undefined,\n        password: undefined\n      });\n    } else if (value === 'mysql') {\n      // MySQL设置默认端口\n      form.setFieldsValue({\n        port: 3306\n      });\n    } else if (value === 'postgresql') {\n      // PostgreSQL设置默认端口\n      form.setFieldsValue({\n        port: 5432\n      });\n    }\n  };\n  const handleCancel = () => {\n    setModalVisible(false);\n  };\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingConnection) {\n        await api.updateConnection(editingConnection.id, values);\n        message.success('连接更新成功');\n        setModalVisible(false);\n        fetchConnections();\n      } else {\n        // 创建新连接\n        setModalVisible(false); // 先关闭模态框\n        const createResponse = await api.createConnection(values);\n        message.success('连接创建成功');\n        fetchConnections();\n\n        // 自动发现并保存数据库结构\n        await handleDiscoverSchema(createResponse.data.id);\n      }\n    } catch (error) {\n      message.error('保存连接失败');\n      console.error(error);\n    }\n  };\n\n  // 发现并保存数据库结构\n  const handleDiscoverSchema = async connectionId => {\n    try {\n      setDiscoveringSchema(true);\n      message.loading({\n        content: '正在分析数据库结构...',\n        key: 'discoverSchema',\n        duration: 0\n      });\n      const response = await api.discoverAndSaveSchema(connectionId);\n      if (response.data.status === 'success') {\n        message.success({\n          content: '数据库结构分析完成',\n          key: 'discoverSchema'\n        });\n        console.log('Discovered schema:', response.data);\n      } else {\n        message.error({\n          content: '数据库结构分析失败',\n          key: 'discoverSchema'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to discover schema:', error);\n      message.error({\n        content: '数据库结构分析失败',\n        key: 'discoverSchema'\n      });\n    } finally {\n      setDiscoveringSchema(false);\n    }\n  };\n  const handleDelete = async id => {\n    try {\n      await api.deleteConnection(id);\n      message.success('连接删除成功');\n      fetchConnections();\n    } catch (error) {\n      message.error('删除连接失败');\n      console.error(error);\n    }\n  };\n  const handleTest = async id => {\n    try {\n      const response = await api.testConnection(id);\n      if (response.data.status === 'success') {\n        message.success('连接测试成功');\n      } else {\n        message.error(`连接测试失败: ${response.data.message}`);\n      }\n    } catch (error) {\n      message.error('连接测试失败');\n      console.error(error);\n    }\n  };\n  const columns = [{\n    title: '名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '类型',\n    dataIndex: 'db_type',\n    key: 'db_type'\n  }, {\n    title: '主机',\n    dataIndex: 'host',\n    key: 'host'\n  }, {\n    title: '端口',\n    dataIndex: 'port',\n    key: 'port'\n  }, {\n    title: '数据库',\n    dataIndex: 'database_name',\n    key: 'database_name'\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleTest(record.id),\n        size: \"small\",\n        children: \"\\u6D4B\\u8BD5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(DatabaseOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDiscoverSchema(record.id),\n        size: \"small\",\n        loading: discoveringSchema,\n        children: \"\\u5206\\u6790\\u7ED3\\u6784\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 19\n        }, this),\n        onClick: () => showModal(record),\n        size: \"small\",\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u8FDE\\u63A5\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u662F\",\n        cancelText: \"\\u5426\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginBottom: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u6570\\u636E\\u5E93\\u8FDE\\u63A5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 19\n          }, this),\n          onClick: () => showModal(),\n          disabled: discoveringSchema,\n          children: \"\\u6DFB\\u52A0\\u8FDE\\u63A5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: connections,\n        rowKey: \"id\",\n        loading: loading || discoveringSchema\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingConnection ? '编辑连接' : '添加连接',\n      open: modalVisible,\n      onOk: handleSubmit,\n      onCancel: handleCancel,\n      width: 600,\n      confirmLoading: discoveringSchema,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          db_type: 'mysql',\n          port: 3306\n        },\n        disabled: discoveringSchema,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8FDE\\u63A5\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u6211\\u7684\\u6570\\u636E\\u5E93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"db_type\",\n          label: \"\\u6570\\u636E\\u5E93\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择数据库类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u6570\\u636E\\u5E93\\u7C7B\\u578B\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"mysql\",\n              children: \"MySQL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"postgresql\",\n              children: \"PostgreSQL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"sqlite\",\n              children: \"SQLite\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"host\",\n          label: \"\\u4E3B\\u673A\",\n          rules: [{\n            required: true,\n            message: '请输入主机'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"localhost\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"port\",\n          label: \"\\u7AEF\\u53E3\",\n          rules: [{\n            required: true,\n            message: '请输入端口'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"number\",\n            placeholder: \"3306\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          label: \"\\u7528\\u6237\\u540D\",\n          rules: [{\n            required: true,\n            message: '请输入用户名'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"root\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"\\u5BC6\\u7801\",\n          rules: [{\n            required: !editingConnection,\n            message: '请输入密码'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            placeholder: \"\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"database_name\",\n          label: \"\\u6570\\u636E\\u5E93\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入数据库名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u6211\\u7684\\u6570\\u636E\\u5E93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n_s(ConnectionsPage, \"8fyQqnWf0xxnwi1jbRFUz9BfmNw=\", false, function () {\n  return [Form.useForm];\n});\n_c = ConnectionsPage;\nexport default ConnectionsPage;\nvar _c;\n$RefreshReg$(_c, \"ConnectionsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Space", "message", "Popconfirm", "Card", "Typography", "PlusOutlined", "EditOutlined", "DeleteOutlined", "CheckCircleOutlined", "DatabaseOutlined", "api", "jsxDEV", "_jsxDEV", "Option", "Title", "ConnectionsPage", "_s", "connections", "setConnections", "loading", "setLoading", "modalVisible", "setModalVisible", "editingConnection", "setEditingConnection", "discoveringSchema", "setDiscoveringSchema", "form", "useForm", "fetchConnections", "response", "getConnections", "data", "error", "console", "showModal", "connection", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "db_type", "host", "port", "username", "database_name", "handleDbTypeChange", "value", "undefined", "password", "handleCancel", "handleSubmit", "values", "validateFields", "updateConnection", "id", "success", "createResponse", "createConnection", "handleDiscoverSchema", "connectionId", "content", "key", "duration", "discoverAndSaveSchema", "status", "log", "handleDelete", "deleteConnection", "handleTest", "testConnection", "columns", "title", "dataIndex", "render", "_", "record", "size", "children", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onConfirm", "okText", "cancelText", "danger", "style", "display", "justifyContent", "marginBottom", "level", "disabled", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "width", "confirmLoading", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "Password", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/pages/ConnectionsPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table, Button, Modal, Form, Input, Select,\n  Space, message, Popconfirm, Card, Typography\n} from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, CheckCircleOutlined, DatabaseOutlined } from '@ant-design/icons';\nimport * as api from '../services/api';\n\nconst { Option } = Select;\nconst { Title } = Typography;\n\ninterface Connection {\n  id: number;\n  name: string;\n  db_type: string;\n  host: string;\n  port: number;\n  username: string;\n  database_name: string;\n  created_at: string;\n  updated_at: string;\n}\n\nconst ConnectionsPage: React.FC = () => {\n  const [connections, setConnections] = useState<Connection[]>([]);\n  const [loading, setLoading] = useState<boolean>(false);\n  const [modalVisible, setModalVisible] = useState<boolean>(false);\n  const [editingConnection, setEditingConnection] = useState<Connection | null>(null);\n  const [discoveringSchema, setDiscoveringSchema] = useState<boolean>(false);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchConnections();\n  }, []);\n\n  const fetchConnections = async () => {\n    setLoading(true);\n    try {\n      const response = await api.getConnections();\n      setConnections(response.data);\n    } catch (error) {\n      message.error('获取连接失败');\n      console.error(error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const showModal = (connection?: Connection) => {\n    setEditingConnection(connection || null);\n    form.resetFields();\n    if (connection) {\n      form.setFieldsValue({\n        name: connection.name,\n        db_type: connection.db_type,\n        host: connection.host,\n        port: connection.port,\n        username: connection.username,\n        database_name: connection.database_name,\n      });\n    }\n    setModalVisible(true);\n  };\n\n  // 监听数据库类型变化，动态调整表单\n  const handleDbTypeChange = (value: string) => {\n    if (value === 'sqlite') {\n      // SQLite不需要这些字段，清空它们\n      form.setFieldsValue({\n        host: undefined,\n        port: undefined,\n        username: undefined,\n        password: undefined,\n      });\n    } else if (value === 'mysql') {\n      // MySQL设置默认端口\n      form.setFieldsValue({\n        port: 3306,\n      });\n    } else if (value === 'postgresql') {\n      // PostgreSQL设置默认端口\n      form.setFieldsValue({\n        port: 5432,\n      });\n    }\n  };\n\n  const handleCancel = () => {\n    setModalVisible(false);\n  };\n\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (editingConnection) {\n        await api.updateConnection(editingConnection.id, values);\n        message.success('连接更新成功');\n        setModalVisible(false);\n        fetchConnections();\n      } else {\n        // 创建新连接\n        setModalVisible(false); // 先关闭模态框\n        const createResponse = await api.createConnection(values);\n        message.success('连接创建成功');\n        fetchConnections();\n\n        // 自动发现并保存数据库结构\n        await handleDiscoverSchema(createResponse.data.id);\n      }\n    } catch (error) {\n      message.error('保存连接失败');\n      console.error(error);\n    }\n  };\n\n  // 发现并保存数据库结构\n  const handleDiscoverSchema = async (connectionId: number) => {\n    try {\n      setDiscoveringSchema(true);\n      message.loading({ content: '正在分析数据库结构...', key: 'discoverSchema', duration: 0 });\n\n      const response = await api.discoverAndSaveSchema(connectionId);\n\n      if (response.data.status === 'success') {\n        message.success({ content: '数据库结构分析完成', key: 'discoverSchema' });\n        console.log('Discovered schema:', response.data);\n      } else {\n        message.error({ content: '数据库结构分析失败', key: 'discoverSchema' });\n      }\n    } catch (error) {\n      console.error('Failed to discover schema:', error);\n      message.error({ content: '数据库结构分析失败', key: 'discoverSchema' });\n    } finally {\n      setDiscoveringSchema(false);\n    }\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await api.deleteConnection(id);\n      message.success('连接删除成功');\n      fetchConnections();\n    } catch (error) {\n      message.error('删除连接失败');\n      console.error(error);\n    }\n  };\n\n  const handleTest = async (id: number) => {\n    try {\n      const response = await api.testConnection(id);\n      if (response.data.status === 'success') {\n        message.success('连接测试成功');\n      } else {\n        message.error(`连接测试失败: ${response.data.message}`);\n      }\n    } catch (error) {\n      message.error('连接测试失败');\n      console.error(error);\n    }\n  };\n\n  const columns = [\n    {\n      title: '名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '类型',\n      dataIndex: 'db_type',\n      key: 'db_type',\n    },\n    {\n      title: '主机',\n      dataIndex: 'host',\n      key: 'host',\n    },\n    {\n      title: '端口',\n      dataIndex: 'port',\n      key: 'port',\n    },\n    {\n      title: '数据库',\n      dataIndex: 'database_name',\n      key: 'database_name',\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_: any, record: Connection) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"primary\"\n            icon={<CheckCircleOutlined />}\n            onClick={() => handleTest(record.id)}\n            size=\"small\"\n          >\n            测试\n          </Button>\n          <Button\n            icon={<DatabaseOutlined />}\n            onClick={() => handleDiscoverSchema(record.id)}\n            size=\"small\"\n            loading={discoveringSchema}\n          >\n            分析结构\n          </Button>\n          <Button\n            icon={<EditOutlined />}\n            onClick={() => showModal(record)}\n            size=\"small\"\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个连接吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"是\"\n            cancelText=\"否\"\n          >\n            <Button\n              danger\n              icon={<DeleteOutlined />}\n              size=\"small\"\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>\n          <Title level={4}>数据库连接</Title>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => showModal()}\n            disabled={discoveringSchema}\n          >\n            添加连接\n          </Button>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={connections}\n          rowKey=\"id\"\n          loading={loading || discoveringSchema}\n        />\n      </Card>\n\n      <Modal\n        title={editingConnection ? '编辑连接' : '添加连接'}\n        open={modalVisible}\n        onOk={handleSubmit}\n        onCancel={handleCancel}\n        width={600}\n        confirmLoading={discoveringSchema}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{ db_type: 'mysql', port: 3306 }}\n          disabled={discoveringSchema}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"连接名称\"\n            rules={[{ required: true, message: '请输入名称' }]}\n          >\n            <Input placeholder=\"我的数据库\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"db_type\"\n            label=\"数据库类型\"\n            rules={[{ required: true, message: '请选择数据库类型' }]}\n          >\n            <Select placeholder=\"选择数据库类型\">\n              <Option value=\"mysql\">MySQL</Option>\n              <Option value=\"postgresql\">PostgreSQL</Option>\n              <Option value=\"sqlite\">SQLite</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"host\"\n            label=\"主机\"\n            rules={[{ required: true, message: '请输入主机' }]}\n          >\n            <Input placeholder=\"localhost\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"port\"\n            label=\"端口\"\n            rules={[{ required: true, message: '请输入端口' }]}\n          >\n            <Input type=\"number\" placeholder=\"3306\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"username\"\n            label=\"用户名\"\n            rules={[{ required: true, message: '请输入用户名' }]}\n          >\n            <Input placeholder=\"root\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            label=\"密码\"\n            rules={[\n              {\n                required: !editingConnection,\n                message: '请输入密码'\n              }\n            ]}\n          >\n            <Input.Password placeholder=\"密码\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"database_name\"\n            label=\"数据库名称\"\n            rules={[{ required: true, message: '请输入数据库名称' }]}\n          >\n            <Input placeholder=\"我的数据库\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ConnectionsPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EACzCC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,EAAEC,UAAU,QACvC,MAAM;AACb,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,gBAAgB,QAAQ,mBAAmB;AACrH,OAAO,KAAKC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAM;EAAEC;AAAO,CAAC,GAAGd,MAAM;AACzB,MAAM;EAAEe;AAAM,CAAC,GAAGV,UAAU;AAc5B,MAAMW,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAAC+B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhC,QAAQ,CAAoB,IAAI,CAAC;EACnF,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAU,KAAK,CAAC;EAC1E,MAAM,CAACmC,IAAI,CAAC,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC;EAE7BnC,SAAS,CAAC,MAAM;IACdoC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCT,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMpB,GAAG,CAACqB,cAAc,CAAC,CAAC;MAC3Cb,cAAc,CAACY,QAAQ,CAACE,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,SAAS,GAAIC,UAAuB,IAAK;IAC7CZ,oBAAoB,CAACY,UAAU,IAAI,IAAI,CAAC;IACxCT,IAAI,CAACU,WAAW,CAAC,CAAC;IAClB,IAAID,UAAU,EAAE;MACdT,IAAI,CAACW,cAAc,CAAC;QAClBC,IAAI,EAAEH,UAAU,CAACG,IAAI;QACrBC,OAAO,EAAEJ,UAAU,CAACI,OAAO;QAC3BC,IAAI,EAAEL,UAAU,CAACK,IAAI;QACrBC,IAAI,EAAEN,UAAU,CAACM,IAAI;QACrBC,QAAQ,EAAEP,UAAU,CAACO,QAAQ;QAC7BC,aAAa,EAAER,UAAU,CAACQ;MAC5B,CAAC,CAAC;IACJ;IACAtB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuB,kBAAkB,GAAIC,KAAa,IAAK;IAC5C,IAAIA,KAAK,KAAK,QAAQ,EAAE;MACtB;MACAnB,IAAI,CAACW,cAAc,CAAC;QAClBG,IAAI,EAAEM,SAAS;QACfL,IAAI,EAAEK,SAAS;QACfJ,QAAQ,EAAEI,SAAS;QACnBC,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM,IAAID,KAAK,KAAK,OAAO,EAAE;MAC5B;MACAnB,IAAI,CAACW,cAAc,CAAC;QAClBI,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,MAAM,IAAII,KAAK,KAAK,YAAY,EAAE;MACjC;MACAnB,IAAI,CAACW,cAAc,CAAC;QAClBI,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzB3B,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAM4B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMxB,IAAI,CAACyB,cAAc,CAAC,CAAC;MAE1C,IAAI7B,iBAAiB,EAAE;QACrB,MAAMb,GAAG,CAAC2C,gBAAgB,CAAC9B,iBAAiB,CAAC+B,EAAE,EAAEH,MAAM,CAAC;QACxDlD,OAAO,CAACsD,OAAO,CAAC,QAAQ,CAAC;QACzBjC,eAAe,CAAC,KAAK,CAAC;QACtBO,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM;QACL;QACAP,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;QACxB,MAAMkC,cAAc,GAAG,MAAM9C,GAAG,CAAC+C,gBAAgB,CAACN,MAAM,CAAC;QACzDlD,OAAO,CAACsD,OAAO,CAAC,QAAQ,CAAC;QACzB1B,gBAAgB,CAAC,CAAC;;QAElB;QACA,MAAM6B,oBAAoB,CAACF,cAAc,CAACxB,IAAI,CAACsB,EAAE,CAAC;MACpD;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMyB,oBAAoB,GAAG,MAAOC,YAAoB,IAAK;IAC3D,IAAI;MACFjC,oBAAoB,CAAC,IAAI,CAAC;MAC1BzB,OAAO,CAACkB,OAAO,CAAC;QAAEyC,OAAO,EAAE,cAAc;QAAEC,GAAG,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAE,CAAC,CAAC;MAEhF,MAAMhC,QAAQ,GAAG,MAAMpB,GAAG,CAACqD,qBAAqB,CAACJ,YAAY,CAAC;MAE9D,IAAI7B,QAAQ,CAACE,IAAI,CAACgC,MAAM,KAAK,SAAS,EAAE;QACtC/D,OAAO,CAACsD,OAAO,CAAC;UAAEK,OAAO,EAAE,WAAW;UAAEC,GAAG,EAAE;QAAiB,CAAC,CAAC;QAChE3B,OAAO,CAAC+B,GAAG,CAAC,oBAAoB,EAAEnC,QAAQ,CAACE,IAAI,CAAC;MAClD,CAAC,MAAM;QACL/B,OAAO,CAACgC,KAAK,CAAC;UAAE2B,OAAO,EAAE,WAAW;UAAEC,GAAG,EAAE;QAAiB,CAAC,CAAC;MAChE;IACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDhC,OAAO,CAACgC,KAAK,CAAC;QAAE2B,OAAO,EAAE,WAAW;QAAEC,GAAG,EAAE;MAAiB,CAAC,CAAC;IAChE,CAAC,SAAS;MACRnC,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAMwC,YAAY,GAAG,MAAOZ,EAAU,IAAK;IACzC,IAAI;MACF,MAAM5C,GAAG,CAACyD,gBAAgB,CAACb,EAAE,CAAC;MAC9BrD,OAAO,CAACsD,OAAO,CAAC,QAAQ,CAAC;MACzB1B,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMmC,UAAU,GAAG,MAAOd,EAAU,IAAK;IACvC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAMpB,GAAG,CAAC2D,cAAc,CAACf,EAAE,CAAC;MAC7C,IAAIxB,QAAQ,CAACE,IAAI,CAACgC,MAAM,KAAK,SAAS,EAAE;QACtC/D,OAAO,CAACsD,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACLtD,OAAO,CAACgC,KAAK,CAAC,WAAWH,QAAQ,CAACE,IAAI,CAAC/B,OAAO,EAAE,CAAC;MACnD;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,QAAQ,CAAC;MACvBC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMqC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,SAAS;IACpBX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,eAAe;IAC1BX,GAAG,EAAE;EACP,CAAC,EACD;IACEU,KAAK,EAAE,IAAI;IACXV,GAAG,EAAE,SAAS;IACdY,MAAM,EAAEA,CAACC,CAAM,EAAEC,MAAkB,kBACjC/D,OAAA,CAACZ,KAAK;MAAC4E,IAAI,EAAC,QAAQ;MAAAC,QAAA,gBAClBjE,OAAA,CAACjB,MAAM;QACLmF,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEnE,OAAA,CAACJ,mBAAmB;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC9BC,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAACO,MAAM,CAACrB,EAAE,CAAE;QACrCsB,IAAI,EAAC,OAAO;QAAAC,QAAA,EACb;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvE,OAAA,CAACjB,MAAM;QACLoF,IAAI,eAAEnE,OAAA,CAACH,gBAAgB;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BC,OAAO,EAAEA,CAAA,KAAM1B,oBAAoB,CAACiB,MAAM,CAACrB,EAAE,CAAE;QAC/CsB,IAAI,EAAC,OAAO;QACZzD,OAAO,EAAEM,iBAAkB;QAAAoD,QAAA,EAC5B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvE,OAAA,CAACjB,MAAM;QACLoF,IAAI,eAAEnE,OAAA,CAACN,YAAY;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,OAAO,EAAEA,CAAA,KAAMjD,SAAS,CAACwC,MAAM,CAAE;QACjCC,IAAI,EAAC,OAAO;QAAAC,QAAA,EACb;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvE,OAAA,CAACV,UAAU;QACTqE,KAAK,EAAC,oEAAa;QACnBc,SAAS,EAAEA,CAAA,KAAMnB,YAAY,CAACS,MAAM,CAACrB,EAAE,CAAE;QACzCgC,MAAM,EAAC,QAAG;QACVC,UAAU,EAAC,QAAG;QAAAV,QAAA,eAEdjE,OAAA,CAACjB,MAAM;UACL6F,MAAM;UACNT,IAAI,eAAEnE,OAAA,CAACL,cAAc;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBP,IAAI,EAAC,OAAO;UAAAC,QAAA,EACb;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEvE,OAAA;IAAAiE,QAAA,gBACEjE,OAAA,CAACT,IAAI;MAAA0E,QAAA,gBACHjE,OAAA;QAAK6E,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAf,QAAA,gBACjFjE,OAAA,CAACE,KAAK;UAAC+E,KAAK,EAAE,CAAE;UAAAhB,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9BvE,OAAA,CAACjB,MAAM;UACLmF,IAAI,EAAC,SAAS;UACdC,IAAI,eAAEnE,OAAA,CAACP,YAAY;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEA,CAAA,KAAMjD,SAAS,CAAC,CAAE;UAC3B2D,QAAQ,EAAErE,iBAAkB;UAAAoD,QAAA,EAC7B;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENvE,OAAA,CAAClB,KAAK;QACJ4E,OAAO,EAAEA,OAAQ;QACjByB,UAAU,EAAE9E,WAAY;QACxB+E,MAAM,EAAC,IAAI;QACX7E,OAAO,EAAEA,OAAO,IAAIM;MAAkB;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPvE,OAAA,CAAChB,KAAK;MACJ2E,KAAK,EAAEhD,iBAAiB,GAAG,MAAM,GAAG,MAAO;MAC3C0E,IAAI,EAAE5E,YAAa;MACnB6E,IAAI,EAAEhD,YAAa;MACnBiD,QAAQ,EAAElD,YAAa;MACvBmD,KAAK,EAAE,GAAI;MACXC,cAAc,EAAE5E,iBAAkB;MAAAoD,QAAA,eAElCjE,OAAA,CAACf,IAAI;QACH8B,IAAI,EAAEA,IAAK;QACX2E,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UAAE/D,OAAO,EAAE,OAAO;UAAEE,IAAI,EAAE;QAAK,CAAE;QAChDoD,QAAQ,EAAErE,iBAAkB;QAAAoD,QAAA,gBAE5BjE,OAAA,CAACf,IAAI,CAAC2G,IAAI;UACRjE,IAAI,EAAC,MAAM;UACXkE,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1G,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA4E,QAAA,eAE9CjE,OAAA,CAACd,KAAK;YAAC8G,WAAW,EAAC;UAAO;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZvE,OAAA,CAACf,IAAI,CAAC2G,IAAI;UACRjE,IAAI,EAAC,SAAS;UACdkE,KAAK,EAAC,gCAAO;UACbC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1G,OAAO,EAAE;UAAW,CAAC,CAAE;UAAA4E,QAAA,eAEjDjE,OAAA,CAACb,MAAM;YAAC6G,WAAW,EAAC,4CAAS;YAAA/B,QAAA,gBAC3BjE,OAAA,CAACC,MAAM;cAACiC,KAAK,EAAC,OAAO;cAAA+B,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpCvE,OAAA,CAACC,MAAM;cAACiC,KAAK,EAAC,YAAY;cAAA+B,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9CvE,OAAA,CAACC,MAAM;cAACiC,KAAK,EAAC,QAAQ;cAAA+B,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZvE,OAAA,CAACf,IAAI,CAAC2G,IAAI;UACRjE,IAAI,EAAC,MAAM;UACXkE,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1G,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA4E,QAAA,eAE9CjE,OAAA,CAACd,KAAK;YAAC8G,WAAW,EAAC;UAAW;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAEZvE,OAAA,CAACf,IAAI,CAAC2G,IAAI;UACRjE,IAAI,EAAC,MAAM;UACXkE,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1G,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA4E,QAAA,eAE9CjE,OAAA,CAACd,KAAK;YAACgF,IAAI,EAAC,QAAQ;YAAC8B,WAAW,EAAC;UAAM;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEZvE,OAAA,CAACf,IAAI,CAAC2G,IAAI;UACRjE,IAAI,EAAC,UAAU;UACfkE,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1G,OAAO,EAAE;UAAS,CAAC,CAAE;UAAA4E,QAAA,eAE/CjE,OAAA,CAACd,KAAK;YAAC8G,WAAW,EAAC;UAAM;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eAEZvE,OAAA,CAACf,IAAI,CAAC2G,IAAI;UACRjE,IAAI,EAAC,UAAU;UACfkE,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YACEC,QAAQ,EAAE,CAACpF,iBAAiB;YAC5BtB,OAAO,EAAE;UACX,CAAC,CACD;UAAA4E,QAAA,eAEFjE,OAAA,CAACd,KAAK,CAAC+G,QAAQ;YAACD,WAAW,EAAC;UAAI;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEZvE,OAAA,CAACf,IAAI,CAAC2G,IAAI;UACRjE,IAAI,EAAC,eAAe;UACpBkE,KAAK,EAAC,gCAAO;UACbC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1G,OAAO,EAAE;UAAW,CAAC,CAAE;UAAA4E,QAAA,eAEjDjE,OAAA,CAACd,KAAK;YAAC8G,WAAW,EAAC;UAAO;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnE,EAAA,CA9TID,eAAyB;EAAA,QAMdlB,IAAI,CAAC+B,OAAO;AAAA;AAAAkF,EAAA,GANvB/F,eAAyB;AAgU/B,eAAeA,eAAe;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}