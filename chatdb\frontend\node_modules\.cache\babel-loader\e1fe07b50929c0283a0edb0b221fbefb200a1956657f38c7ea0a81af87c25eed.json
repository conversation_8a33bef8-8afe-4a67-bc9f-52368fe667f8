{"ast": null, "code": "'use strict';\n\nmodule.exports = gap;\ngap.displayName = 'gap';\ngap.aliases = [];\nfunction gap(Prism) {\n  // https://www.gap-system.org/Manuals/doc/ref/chap4.html\n  // https://www.gap-system.org/Manuals/doc/ref/chap27.html\n  Prism.languages.gap = {\n    shell: {\n      pattern: /^gap>[\\s\\S]*?(?=^gap>|$(?![\\s\\S]))/m,\n      greedy: true,\n      inside: {\n        gap: {\n          pattern: /^(gap>).+(?:(?:\\r(?:\\n|(?!\\n))|\\n)>.*)*/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        punctuation: /^gap>/\n      }\n    },\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    string: {\n      pattern: /(^|[^\\\\'\"])(?:'(?:[^\\r\\n\\\\']|\\\\.){1,10}'|\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"(?!\")|\"\"\"[\\s\\S]*?\"\"\")/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        continuation: {\n          pattern: /([\\r\\n])>/,\n          lookbehind: true,\n          alias: 'punctuation'\n        }\n      }\n    },\n    keyword: /\\b(?:Assert|Info|IsBound|QUIT|TryNextMethod|Unbind|and|atomic|break|continue|do|elif|else|end|fi|for|function|if|in|local|mod|not|od|or|quit|readonly|readwrite|rec|repeat|return|then|until|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: {\n      pattern: /(^|[^\\w.]|\\.\\.)(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?(?:_[a-z]?)?(?=$|[^\\w.]|\\.\\.)/,\n      lookbehind: true\n    },\n    continuation: {\n      pattern: /([\\r\\n])>/,\n      lookbehind: true,\n      alias: 'punctuation'\n    },\n    operator: /->|[-+*/^~=!]|<>|[<>]=?|:=|\\.\\./,\n    punctuation: /[()[\\]{},;.:]/\n  };\n  Prism.languages.gap.shell.inside.gap.inside = Prism.languages.gap;\n}", "map": {"version": 3, "names": ["module", "exports", "gap", "displayName", "aliases", "Prism", "languages", "shell", "pattern", "greedy", "inside", "lookbehind", "punctuation", "comment", "string", "continuation", "alias", "keyword", "boolean", "function", "number", "operator"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/gap.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = gap\ngap.displayName = 'gap'\ngap.aliases = []\nfunction gap(Prism) {\n  // https://www.gap-system.org/Manuals/doc/ref/chap4.html\n  // https://www.gap-system.org/Manuals/doc/ref/chap27.html\n  Prism.languages.gap = {\n    shell: {\n      pattern: /^gap>[\\s\\S]*?(?=^gap>|$(?![\\s\\S]))/m,\n      greedy: true,\n      inside: {\n        gap: {\n          pattern: /^(gap>).+(?:(?:\\r(?:\\n|(?!\\n))|\\n)>.*)*/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        punctuation: /^gap>/\n      }\n    },\n    comment: {\n      pattern: /#.*/,\n      greedy: true\n    },\n    string: {\n      pattern:\n        /(^|[^\\\\'\"])(?:'(?:[^\\r\\n\\\\']|\\\\.){1,10}'|\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"(?!\")|\"\"\"[\\s\\S]*?\"\"\")/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        continuation: {\n          pattern: /([\\r\\n])>/,\n          lookbehind: true,\n          alias: 'punctuation'\n        }\n      }\n    },\n    keyword:\n      /\\b(?:Assert|Info|IsBound|QUIT|TryNextMethod|Unbind|and|atomic|break|continue|do|elif|else|end|fi|for|function|if|in|local|mod|not|od|or|quit|readonly|readwrite|rec|repeat|return|then|until|while)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: {\n      pattern:\n        /(^|[^\\w.]|\\.\\.)(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?(?:_[a-z]?)?(?=$|[^\\w.]|\\.\\.)/,\n      lookbehind: true\n    },\n    continuation: {\n      pattern: /([\\r\\n])>/,\n      lookbehind: true,\n      alias: 'punctuation'\n    },\n    operator: /->|[-+*/^~=!]|<>|[<>]=?|:=|\\.\\./,\n    punctuation: /[()[\\]{},;.:]/\n  }\n  Prism.languages.gap.shell.inside.gap.inside = Prism.languages.gap\n}\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBA,GAAG,CAACC,WAAW,GAAG,KAAK;AACvBD,GAAG,CAACE,OAAO,GAAG,EAAE;AAChB,SAASF,GAAGA,CAACG,KAAK,EAAE;EAClB;EACA;EACAA,KAAK,CAACC,SAAS,CAACJ,GAAG,GAAG;IACpBK,KAAK,EAAE;MACLC,OAAO,EAAE,qCAAqC;MAC9CC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNR,GAAG,EAAE;UACHM,OAAO,EAAE,yCAAyC;UAClDG,UAAU,EAAE,IAAI;UAChBD,MAAM,EAAE,IAAI,CAAC;QACf,CAAC;QACDE,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EAAE;MACPL,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;IACV,CAAC;IACDK,MAAM,EAAE;MACNN,OAAO,EACL,qFAAqF;MACvFG,UAAU,EAAE,IAAI;MAChBF,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;QACNK,YAAY,EAAE;UACZP,OAAO,EAAE,WAAW;UACpBG,UAAU,EAAE,IAAI;UAChBK,KAAK,EAAE;QACT;MACF;IACF,CAAC;IACDC,OAAO,EACL,uMAAuM;IACzMC,OAAO,EAAE,oBAAoB;IAC7BC,QAAQ,EAAE,uBAAuB;IACjCC,MAAM,EAAE;MACNZ,OAAO,EACL,sFAAsF;MACxFG,UAAU,EAAE;IACd,CAAC;IACDI,YAAY,EAAE;MACZP,OAAO,EAAE,WAAW;MACpBG,UAAU,EAAE,IAAI;MAChBK,KAAK,EAAE;IACT,CAAC;IACDK,QAAQ,EAAE,iCAAiC;IAC3CT,WAAW,EAAE;EACf,CAAC;EACDP,KAAK,CAACC,SAAS,CAACJ,GAAG,CAACK,KAAK,CAACG,MAAM,CAACR,GAAG,CAACQ,MAAM,GAAGL,KAAK,CAACC,SAAS,CAACJ,GAAG;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}