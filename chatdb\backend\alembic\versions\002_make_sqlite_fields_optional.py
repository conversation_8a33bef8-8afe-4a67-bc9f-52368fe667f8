"""Make SQLite connection fields optional

Revision ID: 002
Revises: 001
Create Date: 2025-01-27 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    """Make host, port, username, and password_encrypted fields nullable for SQLite connections"""
    # Make fields nullable
    op.alter_column('db_connections', 'host',
                    existing_type=sa.String(255),
                    nullable=True,
                    existing_nullable=False)
    
    op.alter_column('db_connections', 'port',
                    existing_type=sa.Integer(),
                    nullable=True,
                    existing_nullable=False)
    
    op.alter_column('db_connections', 'username',
                    existing_type=sa.String(255),
                    nullable=True,
                    existing_nullable=False)
    
    op.alter_column('db_connections', 'password_encrypted',
                    existing_type=sa.String(255),
                    nullable=True,
                    existing_nullable=False)


def downgrade():
    """Revert fields back to non-nullable"""
    # First, update any NULL values to empty strings/default values
    op.execute("UPDATE db_connections SET host = 'localhost' WHERE host IS NULL")
    op.execute("UPDATE db_connections SET port = 0 WHERE port IS NULL")
    op.execute("UPDATE db_connections SET username = '' WHERE username IS NULL")
    op.execute("UPDATE db_connections SET password_encrypted = '' WHERE password_encrypted IS NULL")
    
    # Make fields non-nullable again
    op.alter_column('db_connections', 'password_encrypted',
                    existing_type=sa.String(255),
                    nullable=False,
                    existing_nullable=True)
    
    op.alter_column('db_connections', 'username',
                    existing_type=sa.String(255),
                    nullable=False,
                    existing_nullable=True)
    
    op.alter_column('db_connections', 'port',
                    existing_type=sa.Integer(),
                    nullable=False,
                    existing_nullable=True)
    
    op.alter_column('db_connections', 'host',
                    existing_type=sa.String(255),
                    nullable=False,
                    existing_nullable=True)
