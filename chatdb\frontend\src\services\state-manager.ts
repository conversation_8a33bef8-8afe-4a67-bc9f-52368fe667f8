/**
 * 统一状态管理服务
 * 提供响应式状态管理、持久化和同步功能
 */
import { BehaviorSubject, Observable, combineLatest, distinctUntilChanged, map } from 'rxjs';

// 状态接口定义
export interface AppState {
  // 连接状态
  connections: {
    list: any[];
    selected: number | null;
    loading: boolean;
  };
  
  // 查询状态
  query: {
    current: string;
    history: string[];
    loading: boolean;
    results: any[];
    error: string | null;
  };
  
  // UI状态
  ui: {
    sidebarCollapsed: boolean;
    activeTab: string;
    theme: 'light' | 'dark';
    language: 'zh' | 'en';
  };
  
  // 系统状态
  system: {
    online: boolean;
    serverStatus: 'connected' | 'disconnected' | 'error';
    lastSync: Date | null;
  };
  
  // 缓存状态
  cache: {
    enabled: boolean;
    size: number;
    hitRate: number;
  };
}

// 初始状态
const initialState: AppState = {
  connections: {
    list: [],
    selected: null,
    loading: false,
  },
  query: {
    current: '',
    history: [],
    loading: false,
    results: [],
    error: null,
  },
  ui: {
    sidebarCollapsed: false,
    activeTab: 'text2sql',
    theme: 'light',
    language: 'zh',
  },
  system: {
    online: navigator.onLine,
    serverStatus: 'disconnected',
    lastSync: null,
  },
  cache: {
    enabled: true,
    size: 0,
    hitRate: 0,
  },
};

// 状态管理器类
class StateManager {
  private state$ = new BehaviorSubject<AppState>(this.loadPersistedState());
  private persistKeys: (keyof AppState)[] = ['ui', 'connections'];

  constructor() {
    // 监听在线状态
    window.addEventListener('online', () => this.updateSystemState({ online: true }));
    window.addEventListener('offline', () => this.updateSystemState({ online: false }));
    
    // 自动持久化状态
    this.state$.subscribe(state => this.persistState(state));
  }

  // 获取完整状态流
  getState$(): Observable<AppState> {
    return this.state$.asObservable();
  }

  // 获取当前状态
  getCurrentState(): AppState {
    return this.state$.value;
  }

  // 获取特定部分的状态流
  select<K extends keyof AppState>(key: K): Observable<AppState[K]> {
    return this.state$.pipe(
      map(state => state[key]),
      distinctUntilChanged()
    );
  }

  // 获取深层状态流
  selectDeep<T>(selector: (state: AppState) => T): Observable<T> {
    return this.state$.pipe(
      map(selector),
      distinctUntilChanged()
    );
  }

  // 更新状态
  updateState(partialState: Partial<AppState>): void {
    const currentState = this.state$.value;
    const newState = this.deepMerge(currentState, partialState);
    this.state$.next(newState);
  }

  // 更新连接状态
  updateConnectionState(partialState: Partial<AppState['connections']>): void {
    this.updateState({
      connections: { ...this.state$.value.connections, ...partialState }
    });
  }

  // 更新查询状态
  updateQueryState(partialState: Partial<AppState['query']>): void {
    this.updateState({
      query: { ...this.state$.value.query, ...partialState }
    });
  }

  // 更新UI状态
  updateUIState(partialState: Partial<AppState['ui']>): void {
    this.updateState({
      ui: { ...this.state$.value.ui, ...partialState }
    });
  }

  // 更新系统状态
  updateSystemState(partialState: Partial<AppState['system']>): void {
    this.updateState({
      system: { ...this.state$.value.system, ...partialState }
    });
  }

  // 更新缓存状态
  updateCacheState(partialState: Partial<AppState['cache']>): void {
    this.updateState({
      cache: { ...this.state$.value.cache, ...partialState }
    });
  }

  // 添加查询到历史
  addQueryToHistory(query: string): void {
    const currentHistory = this.state$.value.query.history;
    const newHistory = [query, ...currentHistory.filter(q => q !== query)].slice(0, 50);
    this.updateQueryState({ history: newHistory });
  }

  // 设置选中的连接
  setSelectedConnection(connectionId: number | null): void {
    this.updateConnectionState({ selected: connectionId });
  }

  // 设置查询结果
  setQueryResults(results: any[], error: string | null = null): void {
    this.updateQueryState({ results, error, loading: false });
  }

  // 设置查询加载状态
  setQueryLoading(loading: boolean): void {
    this.updateQueryState({ loading });
  }

  // 设置查询错误
  setQueryError(error: string | null): void {
    this.updateQueryState({ error, loading: false });
  }

  // 重置查询状态
  resetQueryState(): void {
    this.updateQueryState({
      current: '',
      results: [],
      error: null,
      loading: false,
    });
  }

  // 深度合并对象
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  // 持久化状态
  private persistState(state: AppState): void {
    try {
      const persistedData: Partial<AppState> = {};
      
      this.persistKeys.forEach(key => {
        persistedData[key] = state[key];
      });
      
      localStorage.setItem('app-state', JSON.stringify(persistedData));
    } catch (error) {
      console.warn('状态持久化失败:', error);
    }
  }

  // 加载持久化状态
  private loadPersistedState(): AppState {
    try {
      const persistedData = localStorage.getItem('app-state');
      if (persistedData) {
        const parsed = JSON.parse(persistedData);
        return this.deepMerge(initialState, parsed);
      }
    } catch (error) {
      console.warn('加载持久化状态失败:', error);
    }
    
    return initialState;
  }

  // 清除持久化状态
  clearPersistedState(): void {
    localStorage.removeItem('app-state');
    this.state$.next(initialState);
  }

  // 导出状态（用于调试）
  exportState(): string {
    return JSON.stringify(this.state$.value, null, 2);
  }

  // 导入状态（用于调试）
  importState(stateJson: string): void {
    try {
      const importedState = JSON.parse(stateJson);
      this.state$.next(this.deepMerge(initialState, importedState));
    } catch (error) {
      console.error('导入状态失败:', error);
    }
  }
}

// 创建全局状态管理器实例
export const stateManager = new StateManager();

// React Hook for state management
export const useAppState = () => {
  const [state, setState] = React.useState(stateManager.getCurrentState());

  React.useEffect(() => {
    const subscription = stateManager.getState$().subscribe(setState);
    return () => subscription.unsubscribe();
  }, []);

  return {
    state,
    updateState: stateManager.updateState.bind(stateManager),
    updateConnectionState: stateManager.updateConnectionState.bind(stateManager),
    updateQueryState: stateManager.updateQueryState.bind(stateManager),
    updateUIState: stateManager.updateUIState.bind(stateManager),
    updateSystemState: stateManager.updateSystemState.bind(stateManager),
    updateCacheState: stateManager.updateCacheState.bind(stateManager),
    addQueryToHistory: stateManager.addQueryToHistory.bind(stateManager),
    setSelectedConnection: stateManager.setSelectedConnection.bind(stateManager),
    setQueryResults: stateManager.setQueryResults.bind(stateManager),
    setQueryLoading: stateManager.setQueryLoading.bind(stateManager),
    setQueryError: stateManager.setQueryError.bind(stateManager),
    resetQueryState: stateManager.resetQueryState.bind(stateManager),
  };
};

// 选择器函数
export const selectors = {
  getConnections: (state: AppState) => state.connections.list,
  getSelectedConnection: (state: AppState) => state.connections.selected,
  getCurrentQuery: (state: AppState) => state.query.current,
  getQueryResults: (state: AppState) => state.query.results,
  getQueryError: (state: AppState) => state.query.error,
  isQueryLoading: (state: AppState) => state.query.loading,
  getActiveTab: (state: AppState) => state.ui.activeTab,
  getTheme: (state: AppState) => state.ui.theme,
  isOnline: (state: AppState) => state.system.online,
  getServerStatus: (state: AppState) => state.system.serverStatus,
};

// 导出类型
export type { AppState };
