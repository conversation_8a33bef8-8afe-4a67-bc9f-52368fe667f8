{"ast": null, "code": "var parse = require('inline-style-parser');\n\n/**\n * Parses inline style to object.\n *\n * @example\n * // returns { 'line-height': '42' }\n * StyleToObject('line-height: 42;');\n *\n * @param  {String}      style      - The inline style.\n * @param  {Function}    [iterator] - The iterator function.\n * @return {null|Object}\n */\nfunction StyleToObject(style, iterator) {\n  var output = null;\n  if (!style || typeof style !== 'string') {\n    return output;\n  }\n  var declaration;\n  var declarations = parse(style);\n  var hasIterator = typeof iterator === 'function';\n  var property;\n  var value;\n  for (var i = 0, len = declarations.length; i < len; i++) {\n    declaration = declarations[i];\n    property = declaration.property;\n    value = declaration.value;\n    if (hasIterator) {\n      iterator(property, value, declaration);\n    } else if (value) {\n      output || (output = {});\n      output[property] = value;\n    }\n  }\n  return output;\n}\nmodule.exports = StyleToObject;\nmodule.exports.default = StyleToObject; // ESM support", "map": {"version": 3, "names": ["parse", "require", "StyleToObject", "style", "iterator", "output", "declaration", "declarations", "hasIterator", "property", "value", "i", "len", "length", "module", "exports", "default"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/style-to-object/index.js"], "sourcesContent": ["var parse = require('inline-style-parser');\n\n/**\n * Parses inline style to object.\n *\n * @example\n * // returns { 'line-height': '42' }\n * StyleToObject('line-height: 42;');\n *\n * @param  {String}      style      - The inline style.\n * @param  {Function}    [iterator] - The iterator function.\n * @return {null|Object}\n */\nfunction StyleToObject(style, iterator) {\n  var output = null;\n  if (!style || typeof style !== 'string') {\n    return output;\n  }\n\n  var declaration;\n  var declarations = parse(style);\n  var hasIterator = typeof iterator === 'function';\n  var property;\n  var value;\n\n  for (var i = 0, len = declarations.length; i < len; i++) {\n    declaration = declarations[i];\n    property = declaration.property;\n    value = declaration.value;\n\n    if (hasIterator) {\n      iterator(property, value, declaration);\n    } else if (value) {\n      output || (output = {});\n      output[property] = value;\n    }\n  }\n\n  return output;\n}\n\nmodule.exports = StyleToObject;\nmodule.exports.default = StyleToObject; // ESM support\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,qBAAqB,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACtC,IAAIC,MAAM,GAAG,IAAI;EACjB,IAAI,CAACF,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAOE,MAAM;EACf;EAEA,IAAIC,WAAW;EACf,IAAIC,YAAY,GAAGP,KAAK,CAACG,KAAK,CAAC;EAC/B,IAAIK,WAAW,GAAG,OAAOJ,QAAQ,KAAK,UAAU;EAChD,IAAIK,QAAQ;EACZ,IAAIC,KAAK;EAET,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGL,YAAY,CAACM,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IACvDL,WAAW,GAAGC,YAAY,CAACI,CAAC,CAAC;IAC7BF,QAAQ,GAAGH,WAAW,CAACG,QAAQ;IAC/BC,KAAK,GAAGJ,WAAW,CAACI,KAAK;IAEzB,IAAIF,WAAW,EAAE;MACfJ,QAAQ,CAACK,QAAQ,EAAEC,KAAK,EAAEJ,WAAW,CAAC;IACxC,CAAC,MAAM,IAAII,KAAK,EAAE;MAChBL,MAAM,KAAKA,MAAM,GAAG,CAAC,CAAC,CAAC;MACvBA,MAAM,CAACI,QAAQ,CAAC,GAAGC,KAAK;IAC1B;EACF;EAEA,OAAOL,MAAM;AACf;AAEAS,MAAM,CAACC,OAAO,GAAGb,aAAa;AAC9BY,MAAM,CAACC,OAAO,CAACC,OAAO,GAAGd,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}