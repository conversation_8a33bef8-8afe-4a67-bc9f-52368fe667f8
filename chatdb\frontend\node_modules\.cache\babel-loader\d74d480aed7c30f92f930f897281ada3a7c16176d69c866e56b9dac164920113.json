{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genPaginationStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    margin\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Pagination ==========================\n      [`${componentCls}-pagination${antCls}-pagination`]: {\n        margin: `${unit(margin)} 0`\n      },\n      [`${componentCls}-pagination`]: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        rowGap: token.paddingXS,\n        '> *': {\n          flex: 'none'\n        },\n        '&-left': {\n          justifyContent: 'flex-start'\n        },\n        '&-center': {\n          justifyContent: 'center'\n        },\n        '&-right': {\n          justifyContent: 'flex-end'\n        }\n      }\n    }\n  };\n};\nexport default genPaginationStyle;", "map": {"version": 3, "names": ["unit", "genPaginationStyle", "token", "componentCls", "antCls", "margin", "display", "flexWrap", "rowGap", "paddingXS", "flex", "justifyContent"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/table/style/pagination.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genPaginationStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    margin\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Pagination ==========================\n      [`${componentCls}-pagination${antCls}-pagination`]: {\n        margin: `${unit(margin)} 0`\n      },\n      [`${componentCls}-pagination`]: {\n        display: 'flex',\n        flexWrap: 'wrap',\n        rowGap: token.paddingXS,\n        '> *': {\n          flex: 'none'\n        },\n        '&-left': {\n          justifyContent: 'flex-start'\n        },\n        '&-center': {\n          justifyContent: 'center'\n        },\n        '&-right': {\n          justifyContent: 'flex-end'\n        }\n      }\n    }\n  };\n};\nexport default genPaginationStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,kBAAkB,GAAGC,KAAK,IAAI;EAClC,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC;EACF,CAAC,GAAGH,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,UAAU,GAAG;MAC3B;MACA,CAAC,GAAGA,YAAY,cAAcC,MAAM,aAAa,GAAG;QAClDC,MAAM,EAAE,GAAGL,IAAI,CAACK,MAAM,CAAC;MACzB,CAAC;MACD,CAAC,GAAGF,YAAY,aAAa,GAAG;QAC9BG,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAEN,KAAK,CAACO,SAAS;QACvB,KAAK,EAAE;UACLC,IAAI,EAAE;QACR,CAAC;QACD,QAAQ,EAAE;UACRC,cAAc,EAAE;QAClB,CAAC;QACD,UAAU,EAAE;UACVA,cAAc,EAAE;QAClB,CAAC;QACD,SAAS,EAAE;UACTA,cAAc,EAAE;QAClB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeV,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}