{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport Cell from './Cell';\nimport DescriptionsContext from './DescriptionsContext';\nfunction renderCells(items, _ref, _ref2) {\n  let {\n    colon,\n    prefixCls,\n    bordered\n  } = _ref;\n  let {\n    component,\n    type,\n    showLabel,\n    showContent,\n    labelStyle: rootLabelStyle,\n    contentStyle: rootContentStyle,\n    styles: rootStyles\n  } = _ref2;\n  return items.map((_ref3, index) => {\n    let {\n      label,\n      children,\n      prefixCls: itemPrefixCls = prefixCls,\n      className,\n      style,\n      labelStyle,\n      contentStyle,\n      span = 1,\n      key,\n      styles\n    } = _ref3;\n    if (typeof component === 'string') {\n      return /*#__PURE__*/React.createElement(Cell, {\n        key: `${type}-${key || index}`,\n        className: className,\n        style: style,\n        styles: {\n          label: Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n          content: Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n        },\n        span: span,\n        colon: colon,\n        component: component,\n        itemPrefixCls: itemPrefixCls,\n        bordered: bordered,\n        label: showLabel ? label : null,\n        content: showContent ? children : null,\n        type: type\n      });\n    }\n    return [/*#__PURE__*/React.createElement(Cell, {\n      key: `label-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), style), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n      span: 1,\n      colon: colon,\n      component: component[0],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      label: label,\n      type: \"label\"\n    }), /*#__PURE__*/React.createElement(Cell, {\n      key: `content-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), style), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content),\n      span: span * 2 - 1,\n      component: component[1],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      content: children,\n      type: \"content\"\n    })];\n  });\n}\nconst Row = props => {\n  const descContext = React.useContext(DescriptionsContext);\n  const {\n    prefixCls,\n    vertical,\n    row,\n    index,\n    bordered\n  } = props;\n  if (vertical) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"tr\", {\n      key: `label-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'th',\n      type: 'label',\n      showLabel: true\n    }, descContext))), /*#__PURE__*/React.createElement(\"tr\", {\n      key: `content-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'td',\n      type: 'content',\n      showContent: true\n    }, descContext))));\n  }\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    className: `${prefixCls}-row`\n  }, renderCells(row, props, Object.assign({\n    component: bordered ? ['th', 'td'] : 'td',\n    type: 'item',\n    showLabel: true,\n    showContent: true\n  }, descContext)));\n};\nexport default Row;", "map": {"version": 3, "names": ["React", "Cell", "DescriptionsContext", "renderCells", "items", "_ref", "_ref2", "colon", "prefixCls", "bordered", "component", "type", "showLabel", "showContent", "labelStyle", "rootLabelStyle", "contentStyle", "rootContentStyle", "styles", "rootStyles", "map", "_ref3", "index", "label", "children", "itemPrefixCls", "className", "style", "span", "key", "createElement", "Object", "assign", "content", "Row", "props", "descContext", "useContext", "vertical", "row", "Fragment"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/descriptions/Row.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport Cell from './Cell';\nimport DescriptionsContext from './DescriptionsContext';\nfunction renderCells(items, _ref, _ref2) {\n  let {\n    colon,\n    prefixCls,\n    bordered\n  } = _ref;\n  let {\n    component,\n    type,\n    showLabel,\n    showContent,\n    labelStyle: rootLabelStyle,\n    contentStyle: rootContentStyle,\n    styles: rootStyles\n  } = _ref2;\n  return items.map((_ref3, index) => {\n    let {\n      label,\n      children,\n      prefixCls: itemPrefixCls = prefixCls,\n      className,\n      style,\n      labelStyle,\n      contentStyle,\n      span = 1,\n      key,\n      styles\n    } = _ref3;\n    if (typeof component === 'string') {\n      return /*#__PURE__*/React.createElement(Cell, {\n        key: `${type}-${key || index}`,\n        className: className,\n        style: style,\n        styles: {\n          label: Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n          content: Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content)\n        },\n        span: span,\n        colon: colon,\n        component: component,\n        itemPrefixCls: itemPrefixCls,\n        bordered: bordered,\n        label: showLabel ? label : null,\n        content: showContent ? children : null,\n        type: type\n      });\n    }\n    return [/*#__PURE__*/React.createElement(Cell, {\n      key: `label-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootLabelStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.label), style), labelStyle), styles === null || styles === void 0 ? void 0 : styles.label),\n      span: 1,\n      colon: colon,\n      component: component[0],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      label: label,\n      type: \"label\"\n    }), /*#__PURE__*/React.createElement(Cell, {\n      key: `content-${key || index}`,\n      className: className,\n      style: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, rootContentStyle), rootStyles === null || rootStyles === void 0 ? void 0 : rootStyles.content), style), contentStyle), styles === null || styles === void 0 ? void 0 : styles.content),\n      span: span * 2 - 1,\n      component: component[1],\n      itemPrefixCls: itemPrefixCls,\n      bordered: bordered,\n      content: children,\n      type: \"content\"\n    })];\n  });\n}\nconst Row = props => {\n  const descContext = React.useContext(DescriptionsContext);\n  const {\n    prefixCls,\n    vertical,\n    row,\n    index,\n    bordered\n  } = props;\n  if (vertical) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"tr\", {\n      key: `label-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'th',\n      type: 'label',\n      showLabel: true\n    }, descContext))), /*#__PURE__*/React.createElement(\"tr\", {\n      key: `content-${index}`,\n      className: `${prefixCls}-row`\n    }, renderCells(row, props, Object.assign({\n      component: 'td',\n      type: 'content',\n      showContent: true\n    }, descContext))));\n  }\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    key: index,\n    className: `${prefixCls}-row`\n  }, renderCells(row, props, Object.assign({\n    component: bordered ? ['th', 'td'] : 'td',\n    type: 'item',\n    showLabel: true,\n    showContent: true\n  }, descContext)));\n};\nexport default Row;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,WAAWA,CAACC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACvC,IAAI;IACFC,KAAK;IACLC,SAAS;IACTC;EACF,CAAC,GAAGJ,IAAI;EACR,IAAI;IACFK,SAAS;IACTC,IAAI;IACJC,SAAS;IACTC,WAAW;IACXC,UAAU,EAAEC,cAAc;IAC1BC,YAAY,EAAEC,gBAAgB;IAC9BC,MAAM,EAAEC;EACV,CAAC,GAAGb,KAAK;EACT,OAAOF,KAAK,CAACgB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;IACjC,IAAI;MACFC,KAAK;MACLC,QAAQ;MACRhB,SAAS,EAAEiB,aAAa,GAAGjB,SAAS;MACpCkB,SAAS;MACTC,KAAK;MACLb,UAAU;MACVE,YAAY;MACZY,IAAI,GAAG,CAAC;MACRC,GAAG;MACHX;IACF,CAAC,GAAGG,KAAK;IACT,IAAI,OAAOX,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAO,aAAaV,KAAK,CAAC8B,aAAa,CAAC7B,IAAI,EAAE;QAC5C4B,GAAG,EAAE,GAAGlB,IAAI,IAAIkB,GAAG,IAAIP,KAAK,EAAE;QAC9BI,SAAS,EAAEA,SAAS;QACpBC,KAAK,EAAEA,KAAK;QACZT,MAAM,EAAE;UACNK,KAAK,EAAEQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,cAAc,CAAC,EAAEI,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACI,KAAK,CAAC,EAAET,UAAU,CAAC,EAAEI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACK,KAAK,CAAC;UACzOU,OAAO,EAAEF,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,gBAAgB,CAAC,EAAEE,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACc,OAAO,CAAC,EAAEjB,YAAY,CAAC,EAAEE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACe,OAAO;QACpP,CAAC;QACDL,IAAI,EAAEA,IAAI;QACVrB,KAAK,EAAEA,KAAK;QACZG,SAAS,EAAEA,SAAS;QACpBe,aAAa,EAAEA,aAAa;QAC5BhB,QAAQ,EAAEA,QAAQ;QAClBc,KAAK,EAAEX,SAAS,GAAGW,KAAK,GAAG,IAAI;QAC/BU,OAAO,EAAEpB,WAAW,GAAGW,QAAQ,GAAG,IAAI;QACtCb,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ;IACA,OAAO,CAAC,aAAaX,KAAK,CAAC8B,aAAa,CAAC7B,IAAI,EAAE;MAC7C4B,GAAG,EAAE,SAASA,GAAG,IAAIP,KAAK,EAAE;MAC5BI,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,cAAc,CAAC,EAAEI,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACI,KAAK,CAAC,EAAEI,KAAK,CAAC,EAAEb,UAAU,CAAC,EAAEI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACK,KAAK,CAAC;MAC/PK,IAAI,EAAE,CAAC;MACPrB,KAAK,EAAEA,KAAK;MACZG,SAAS,EAAEA,SAAS,CAAC,CAAC,CAAC;MACvBe,aAAa,EAAEA,aAAa;MAC5BhB,QAAQ,EAAEA,QAAQ;MAClBc,KAAK,EAAEA,KAAK;MACZZ,IAAI,EAAE;IACR,CAAC,CAAC,EAAE,aAAaX,KAAK,CAAC8B,aAAa,CAAC7B,IAAI,EAAE;MACzC4B,GAAG,EAAE,WAAWA,GAAG,IAAIP,KAAK,EAAE;MAC9BI,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,gBAAgB,CAAC,EAAEE,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACc,OAAO,CAAC,EAAEN,KAAK,CAAC,EAAEX,YAAY,CAAC,EAAEE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACe,OAAO,CAAC;MACvQL,IAAI,EAAEA,IAAI,GAAG,CAAC,GAAG,CAAC;MAClBlB,SAAS,EAAEA,SAAS,CAAC,CAAC,CAAC;MACvBe,aAAa,EAAEA,aAAa;MAC5BhB,QAAQ,EAAEA,QAAQ;MAClBwB,OAAO,EAAET,QAAQ;MACjBb,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,MAAMuB,GAAG,GAAGC,KAAK,IAAI;EACnB,MAAMC,WAAW,GAAGpC,KAAK,CAACqC,UAAU,CAACnC,mBAAmB,CAAC;EACzD,MAAM;IACJM,SAAS;IACT8B,QAAQ;IACRC,GAAG;IACHjB,KAAK;IACLb;EACF,CAAC,GAAG0B,KAAK;EACT,IAAIG,QAAQ,EAAE;IACZ,OAAO,aAAatC,KAAK,CAAC8B,aAAa,CAAC9B,KAAK,CAACwC,QAAQ,EAAE,IAAI,EAAE,aAAaxC,KAAK,CAAC8B,aAAa,CAAC,IAAI,EAAE;MACnGD,GAAG,EAAE,SAASP,KAAK,EAAE;MACrBI,SAAS,EAAE,GAAGlB,SAAS;IACzB,CAAC,EAAEL,WAAW,CAACoC,GAAG,EAAEJ,KAAK,EAAEJ,MAAM,CAACC,MAAM,CAAC;MACvCtB,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,OAAO;MACbC,SAAS,EAAE;IACb,CAAC,EAAEwB,WAAW,CAAC,CAAC,CAAC,EAAE,aAAapC,KAAK,CAAC8B,aAAa,CAAC,IAAI,EAAE;MACxDD,GAAG,EAAE,WAAWP,KAAK,EAAE;MACvBI,SAAS,EAAE,GAAGlB,SAAS;IACzB,CAAC,EAAEL,WAAW,CAACoC,GAAG,EAAEJ,KAAK,EAAEJ,MAAM,CAACC,MAAM,CAAC;MACvCtB,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,SAAS;MACfE,WAAW,EAAE;IACf,CAAC,EAAEuB,WAAW,CAAC,CAAC,CAAC,CAAC;EACpB;EACA,OAAO,aAAapC,KAAK,CAAC8B,aAAa,CAAC,IAAI,EAAE;IAC5CD,GAAG,EAAEP,KAAK;IACVI,SAAS,EAAE,GAAGlB,SAAS;EACzB,CAAC,EAAEL,WAAW,CAACoC,GAAG,EAAEJ,KAAK,EAAEJ,MAAM,CAACC,MAAM,CAAC;IACvCtB,SAAS,EAAED,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI;IACzCE,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE;EACf,CAAC,EAAEuB,WAAW,CAAC,CAAC,CAAC;AACnB,CAAC;AACD,eAAeF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}