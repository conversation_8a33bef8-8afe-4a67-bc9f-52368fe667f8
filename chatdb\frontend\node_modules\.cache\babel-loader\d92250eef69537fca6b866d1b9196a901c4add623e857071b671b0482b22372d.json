{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Styles ==============================\nconst genBaseStyle = token => {\n  const {\n    paddingXXS,\n    lineWidth,\n    tagPaddingHorizontal,\n    componentCls,\n    calc\n  } = token;\n  const paddingInline = calc(tagPaddingHorizontal).sub(lineWidth).equal();\n  const iconMarginInline = calc(paddingXXS).sub(lineWidth).equal();\n  return {\n    // Result\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'inline-block',\n      height: 'auto',\n      // https://github.com/ant-design/ant-design/pull/47504\n      marginInlineEnd: token.marginXS,\n      paddingInline,\n      fontSize: token.tagFontSize,\n      lineHeight: token.tagLineHeight,\n      whiteSpace: 'nowrap',\n      background: token.defaultBg,\n      border: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`,\n      borderRadius: token.borderRadiusSM,\n      opacity: 1,\n      transition: `all ${token.motionDurationMid}`,\n      textAlign: 'start',\n      position: 'relative',\n      // RTL\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      '&, a, a:hover': {\n        color: token.defaultColor\n      },\n      [`${componentCls}-close-icon`]: {\n        marginInlineStart: iconMarginInline,\n        fontSize: token.tagIconSize,\n        color: token.colorIcon,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationMid}`,\n        '&:hover': {\n          color: token.colorTextHeading\n        }\n      },\n      [`&${componentCls}-has-color`]: {\n        borderColor: 'transparent',\n        [`&, a, a:hover, ${token.iconCls}-close, ${token.iconCls}-close:hover`]: {\n          color: token.colorTextLightSolid\n        }\n      },\n      '&-checkable': {\n        backgroundColor: 'transparent',\n        borderColor: 'transparent',\n        cursor: 'pointer',\n        [`&:not(${componentCls}-checkable-checked):hover`]: {\n          color: token.colorPrimary,\n          backgroundColor: token.colorFillSecondary\n        },\n        '&:active, &-checked': {\n          color: token.colorTextLightSolid\n        },\n        '&-checked': {\n          backgroundColor: token.colorPrimary,\n          '&:hover': {\n            backgroundColor: token.colorPrimaryHover\n          }\n        },\n        '&:active': {\n          backgroundColor: token.colorPrimaryActive\n        }\n      },\n      '&-hidden': {\n        display: 'none'\n      },\n      // To ensure that a space will be placed between character and `Icon`.\n      [`> ${token.iconCls} + span, > span + ${token.iconCls}`]: {\n        marginInlineStart: paddingInline\n      }\n    }),\n    [`${componentCls}-borderless`]: {\n      borderColor: 'transparent',\n      background: token.tagBorderlessBg\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareToken = token => {\n  const {\n    lineWidth,\n    fontSizeIcon,\n    calc\n  } = token;\n  const tagFontSize = token.fontSizeSM;\n  const tagToken = mergeToken(token, {\n    tagFontSize,\n    tagLineHeight: unit(calc(token.lineHeightSM).mul(tagFontSize).equal()),\n    tagIconSize: calc(fontSizeIcon).sub(calc(lineWidth).mul(2)).equal(),\n    // Tag icon is much smaller\n    tagPaddingHorizontal: 8,\n    // Fixed padding.\n    tagBorderlessBg: token.defaultBg\n  });\n  return tagToken;\n};\nexport const prepareComponentToken = token => ({\n  defaultBg: new FastColor(token.colorFillQuaternary).onBackground(token.colorBgContainer).toHexString(),\n  defaultColor: token.colorText\n});\nexport default genStyleHooks('Tag', token => {\n  const tagToken = prepareToken(token);\n  return genBaseStyle(tagToken);\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "FastColor", "resetComponent", "genStyleHooks", "mergeToken", "genBaseStyle", "token", "paddingXXS", "lineWidth", "tagPaddingHorizontal", "componentCls", "calc", "paddingInline", "sub", "equal", "iconMarginInline", "Object", "assign", "display", "height", "marginInlineEnd", "marginXS", "fontSize", "tagFontSize", "lineHeight", "tagLineHeight", "whiteSpace", "background", "defaultBg", "border", "lineType", "colorBorder", "borderRadius", "borderRadiusSM", "opacity", "transition", "motionDurationMid", "textAlign", "position", "direction", "color", "defaultColor", "marginInlineStart", "tagIconSize", "colorIcon", "cursor", "colorTextHeading", "borderColor", "iconCls", "colorTextLightSolid", "backgroundColor", "colorPrimary", "colorFillSecondary", "colorPrimaryHover", "colorPrimaryActive", "tagBorderlessBg", "prepareToken", "fontSizeIcon", "fontSizeSM", "tagToken", "lineHeightSM", "mul", "prepareComponentToken", "colorFillQuaternary", "onBackground", "colorBgContainer", "toHexString", "colorText"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/tag/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { FastColor } from '@ant-design/fast-color';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Styles ==============================\nconst genBaseStyle = token => {\n  const {\n    paddingXXS,\n    lineWidth,\n    tagPaddingHorizontal,\n    componentCls,\n    calc\n  } = token;\n  const paddingInline = calc(tagPaddingHorizontal).sub(lineWidth).equal();\n  const iconMarginInline = calc(paddingXXS).sub(lineWidth).equal();\n  return {\n    // Result\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'inline-block',\n      height: 'auto',\n      // https://github.com/ant-design/ant-design/pull/47504\n      marginInlineEnd: token.marginXS,\n      paddingInline,\n      fontSize: token.tagFontSize,\n      lineHeight: token.tagLineHeight,\n      whiteSpace: 'nowrap',\n      background: token.defaultBg,\n      border: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`,\n      borderRadius: token.borderRadiusSM,\n      opacity: 1,\n      transition: `all ${token.motionDurationMid}`,\n      textAlign: 'start',\n      position: 'relative',\n      // RTL\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      '&, a, a:hover': {\n        color: token.defaultColor\n      },\n      [`${componentCls}-close-icon`]: {\n        marginInlineStart: iconMarginInline,\n        fontSize: token.tagIconSize,\n        color: token.colorIcon,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationMid}`,\n        '&:hover': {\n          color: token.colorTextHeading\n        }\n      },\n      [`&${componentCls}-has-color`]: {\n        borderColor: 'transparent',\n        [`&, a, a:hover, ${token.iconCls}-close, ${token.iconCls}-close:hover`]: {\n          color: token.colorTextLightSolid\n        }\n      },\n      '&-checkable': {\n        backgroundColor: 'transparent',\n        borderColor: 'transparent',\n        cursor: 'pointer',\n        [`&:not(${componentCls}-checkable-checked):hover`]: {\n          color: token.colorPrimary,\n          backgroundColor: token.colorFillSecondary\n        },\n        '&:active, &-checked': {\n          color: token.colorTextLightSolid\n        },\n        '&-checked': {\n          backgroundColor: token.colorPrimary,\n          '&:hover': {\n            backgroundColor: token.colorPrimaryHover\n          }\n        },\n        '&:active': {\n          backgroundColor: token.colorPrimaryActive\n        }\n      },\n      '&-hidden': {\n        display: 'none'\n      },\n      // To ensure that a space will be placed between character and `Icon`.\n      [`> ${token.iconCls} + span, > span + ${token.iconCls}`]: {\n        marginInlineStart: paddingInline\n      }\n    }),\n    [`${componentCls}-borderless`]: {\n      borderColor: 'transparent',\n      background: token.tagBorderlessBg\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareToken = token => {\n  const {\n    lineWidth,\n    fontSizeIcon,\n    calc\n  } = token;\n  const tagFontSize = token.fontSizeSM;\n  const tagToken = mergeToken(token, {\n    tagFontSize,\n    tagLineHeight: unit(calc(token.lineHeightSM).mul(tagFontSize).equal()),\n    tagIconSize: calc(fontSizeIcon).sub(calc(lineWidth).mul(2)).equal(),\n    // Tag icon is much smaller\n    tagPaddingHorizontal: 8,\n    // Fixed padding.\n    tagBorderlessBg: token.defaultBg\n  });\n  return tagToken;\n};\nexport const prepareComponentToken = token => ({\n  defaultBg: new FastColor(token.colorFillQuaternary).onBackground(token.colorBgContainer).toHexString(),\n  defaultColor: token.colorText\n});\nexport default genStyleHooks('Tag', token => {\n  const tagToken = prepareToken(token);\n  return genBaseStyle(tagToken);\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE;AACA,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,UAAU;IACVC,SAAS;IACTC,oBAAoB;IACpBC,YAAY;IACZC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,aAAa,GAAGD,IAAI,CAACF,oBAAoB,CAAC,CAACI,GAAG,CAACL,SAAS,CAAC,CAACM,KAAK,CAAC,CAAC;EACvE,MAAMC,gBAAgB,GAAGJ,IAAI,CAACJ,UAAU,CAAC,CAACM,GAAG,CAACL,SAAS,CAAC,CAACM,KAAK,CAAC,CAAC;EAChE,OAAO;IACL;IACA,CAACJ,YAAY,GAAGM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,cAAc,CAACI,KAAK,CAAC,CAAC,EAAE;MACtEY,OAAO,EAAE,cAAc;MACvBC,MAAM,EAAE,MAAM;MACd;MACAC,eAAe,EAAEd,KAAK,CAACe,QAAQ;MAC/BT,aAAa;MACbU,QAAQ,EAAEhB,KAAK,CAACiB,WAAW;MAC3BC,UAAU,EAAElB,KAAK,CAACmB,aAAa;MAC/BC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAErB,KAAK,CAACsB,SAAS;MAC3BC,MAAM,EAAE,GAAG7B,IAAI,CAACM,KAAK,CAACE,SAAS,CAAC,IAAIF,KAAK,CAACwB,QAAQ,IAAIxB,KAAK,CAACyB,WAAW,EAAE;MACzEC,YAAY,EAAE1B,KAAK,CAAC2B,cAAc;MAClCC,OAAO,EAAE,CAAC;MACVC,UAAU,EAAE,OAAO7B,KAAK,CAAC8B,iBAAiB,EAAE;MAC5CC,SAAS,EAAE,OAAO;MAClBC,QAAQ,EAAE,UAAU;MACpB;MACA,CAAC,IAAI5B,YAAY,MAAM,GAAG;QACxB6B,SAAS,EAAE;MACb,CAAC;MACD,eAAe,EAAE;QACfC,KAAK,EAAElC,KAAK,CAACmC;MACf,CAAC;MACD,CAAC,GAAG/B,YAAY,aAAa,GAAG;QAC9BgC,iBAAiB,EAAE3B,gBAAgB;QACnCO,QAAQ,EAAEhB,KAAK,CAACqC,WAAW;QAC3BH,KAAK,EAAElC,KAAK,CAACsC,SAAS;QACtBC,MAAM,EAAE,SAAS;QACjBV,UAAU,EAAE,OAAO7B,KAAK,CAAC8B,iBAAiB,EAAE;QAC5C,SAAS,EAAE;UACTI,KAAK,EAAElC,KAAK,CAACwC;QACf;MACF,CAAC;MACD,CAAC,IAAIpC,YAAY,YAAY,GAAG;QAC9BqC,WAAW,EAAE,aAAa;QAC1B,CAAC,kBAAkBzC,KAAK,CAAC0C,OAAO,WAAW1C,KAAK,CAAC0C,OAAO,cAAc,GAAG;UACvER,KAAK,EAAElC,KAAK,CAAC2C;QACf;MACF,CAAC;MACD,aAAa,EAAE;QACbC,eAAe,EAAE,aAAa;QAC9BH,WAAW,EAAE,aAAa;QAC1BF,MAAM,EAAE,SAAS;QACjB,CAAC,SAASnC,YAAY,2BAA2B,GAAG;UAClD8B,KAAK,EAAElC,KAAK,CAAC6C,YAAY;UACzBD,eAAe,EAAE5C,KAAK,CAAC8C;QACzB,CAAC;QACD,qBAAqB,EAAE;UACrBZ,KAAK,EAAElC,KAAK,CAAC2C;QACf,CAAC;QACD,WAAW,EAAE;UACXC,eAAe,EAAE5C,KAAK,CAAC6C,YAAY;UACnC,SAAS,EAAE;YACTD,eAAe,EAAE5C,KAAK,CAAC+C;UACzB;QACF,CAAC;QACD,UAAU,EAAE;UACVH,eAAe,EAAE5C,KAAK,CAACgD;QACzB;MACF,CAAC;MACD,UAAU,EAAE;QACVpC,OAAO,EAAE;MACX,CAAC;MACD;MACA,CAAC,KAAKZ,KAAK,CAAC0C,OAAO,qBAAqB1C,KAAK,CAAC0C,OAAO,EAAE,GAAG;QACxDN,iBAAiB,EAAE9B;MACrB;IACF,CAAC,CAAC;IACF,CAAC,GAAGF,YAAY,aAAa,GAAG;MAC9BqC,WAAW,EAAE,aAAa;MAC1BpB,UAAU,EAAErB,KAAK,CAACiD;IACpB;EACF,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMC,YAAY,GAAGlD,KAAK,IAAI;EACnC,MAAM;IACJE,SAAS;IACTiD,YAAY;IACZ9C;EACF,CAAC,GAAGL,KAAK;EACT,MAAMiB,WAAW,GAAGjB,KAAK,CAACoD,UAAU;EACpC,MAAMC,QAAQ,GAAGvD,UAAU,CAACE,KAAK,EAAE;IACjCiB,WAAW;IACXE,aAAa,EAAEzB,IAAI,CAACW,IAAI,CAACL,KAAK,CAACsD,YAAY,CAAC,CAACC,GAAG,CAACtC,WAAW,CAAC,CAACT,KAAK,CAAC,CAAC,CAAC;IACtE6B,WAAW,EAAEhC,IAAI,CAAC8C,YAAY,CAAC,CAAC5C,GAAG,CAACF,IAAI,CAACH,SAAS,CAAC,CAACqD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC/C,KAAK,CAAC,CAAC;IACnE;IACAL,oBAAoB,EAAE,CAAC;IACvB;IACA8C,eAAe,EAAEjD,KAAK,CAACsB;EACzB,CAAC,CAAC;EACF,OAAO+B,QAAQ;AACjB,CAAC;AACD,OAAO,MAAMG,qBAAqB,GAAGxD,KAAK,KAAK;EAC7CsB,SAAS,EAAE,IAAI3B,SAAS,CAACK,KAAK,CAACyD,mBAAmB,CAAC,CAACC,YAAY,CAAC1D,KAAK,CAAC2D,gBAAgB,CAAC,CAACC,WAAW,CAAC,CAAC;EACtGzB,YAAY,EAAEnC,KAAK,CAAC6D;AACtB,CAAC,CAAC;AACF,eAAehE,aAAa,CAAC,KAAK,EAAEG,KAAK,IAAI;EAC3C,MAAMqD,QAAQ,GAAGH,YAAY,CAAClD,KAAK,CAAC;EACpC,OAAOD,YAAY,CAACsD,QAAQ,CAAC;AAC/B,CAAC,EAAEG,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}