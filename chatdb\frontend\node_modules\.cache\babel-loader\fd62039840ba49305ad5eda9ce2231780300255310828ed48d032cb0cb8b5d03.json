{"ast": null, "code": "const genStepsRTLStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`&${componentCls}-rtl`]: {\n      direction: 'rtl',\n      [`${componentCls}-item`]: {\n        '&-subtitle': {\n          float: 'left'\n        }\n      },\n      // nav\n      [`&${componentCls}-navigation`]: {\n        [`${componentCls}-item::after`]: {\n          transform: 'rotate(-45deg)'\n        }\n      },\n      // vertical\n      [`&${componentCls}-vertical`]: {\n        [`> ${componentCls}-item`]: {\n          '&::after': {\n            transform: 'rotate(225deg)'\n          },\n          [`${componentCls}-item-icon`]: {\n            float: 'right'\n          }\n        }\n      },\n      // progress-dot\n      [`&${componentCls}-dot`]: {\n        [`${componentCls}-item-icon ${componentCls}-icon-dot, &${componentCls}-small ${componentCls}-item-icon ${componentCls}-icon-dot`]: {\n          float: 'right'\n        }\n      }\n    }\n  };\n};\nexport default genStepsRTLStyle;", "map": {"version": 3, "names": ["genStepsRTLStyle", "token", "componentCls", "direction", "float", "transform"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/steps/style/rtl.js"], "sourcesContent": ["const genStepsRTLStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`&${componentCls}-rtl`]: {\n      direction: 'rtl',\n      [`${componentCls}-item`]: {\n        '&-subtitle': {\n          float: 'left'\n        }\n      },\n      // nav\n      [`&${componentCls}-navigation`]: {\n        [`${componentCls}-item::after`]: {\n          transform: 'rotate(-45deg)'\n        }\n      },\n      // vertical\n      [`&${componentCls}-vertical`]: {\n        [`> ${componentCls}-item`]: {\n          '&::after': {\n            transform: 'rotate(225deg)'\n          },\n          [`${componentCls}-item-icon`]: {\n            float: 'right'\n          }\n        }\n      },\n      // progress-dot\n      [`&${componentCls}-dot`]: {\n        [`${componentCls}-item-icon ${componentCls}-icon-dot, &${componentCls}-small ${componentCls}-item-icon ${componentCls}-icon-dot`]: {\n          float: 'right'\n        }\n      }\n    }\n  };\n};\nexport default genStepsRTLStyle;"], "mappings": "AAAA,MAAMA,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAAC,IAAIC,YAAY,MAAM,GAAG;MACxBC,SAAS,EAAE,KAAK;MAChB,CAAC,GAAGD,YAAY,OAAO,GAAG;QACxB,YAAY,EAAE;UACZE,KAAK,EAAE;QACT;MACF,CAAC;MACD;MACA,CAAC,IAAIF,YAAY,aAAa,GAAG;QAC/B,CAAC,GAAGA,YAAY,cAAc,GAAG;UAC/BG,SAAS,EAAE;QACb;MACF,CAAC;MACD;MACA,CAAC,IAAIH,YAAY,WAAW,GAAG;QAC7B,CAAC,KAAKA,YAAY,OAAO,GAAG;UAC1B,UAAU,EAAE;YACVG,SAAS,EAAE;UACb,CAAC;UACD,CAAC,GAAGH,YAAY,YAAY,GAAG;YAC7BE,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACD;MACA,CAAC,IAAIF,YAAY,MAAM,GAAG;QACxB,CAAC,GAAGA,YAAY,cAAcA,YAAY,eAAeA,YAAY,UAAUA,YAAY,cAAcA,YAAY,WAAW,GAAG;UACjIE,KAAK,EAAE;QACT;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}