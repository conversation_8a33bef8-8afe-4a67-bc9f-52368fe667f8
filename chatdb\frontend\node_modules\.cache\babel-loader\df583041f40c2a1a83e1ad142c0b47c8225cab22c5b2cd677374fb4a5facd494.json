{"ast": null, "code": "/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return d;\n}", "map": {"version": 3, "names": ["color", "d"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/unist-util-visit-parents/lib/color.browser.js"], "sourcesContent": ["/**\n * @param {string} d\n * @returns {string}\n */\nexport function color(d) {\n  return d\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,KAAKA,CAACC,CAAC,EAAE;EACvB,OAAOA,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}