{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:8000/api';\n\n// 创建API实例\nconst api = axios.create({\n  baseURL: API_BASE_URL\n});\n\n// 聊天历史接口类型定义\n\n// 聊天历史服务类\nexport class ChatHistoryService {\n  /**\n   * 获取聊天历史列表\n   */\n  async getChatHistories(skip = 0, limit = 50, connectionId) {\n    try {\n      const params = {\n        skip,\n        limit\n      };\n      if (connectionId !== undefined) {\n        params.connection_id = connectionId;\n      }\n      const response = await api.get('/chat-history/', {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      console.error('获取聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 保存聊天历史\n   */\n  async saveChatHistory(request) {\n    try {\n      const response = await api.post('/chat-history/save', request);\n      return response.data;\n    } catch (error) {\n      console.error('保存聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 通过SSE端点保存聊天历史\n   */\n  async saveChatHistoryViaSSE(request) {\n    try {\n      const response = await api.post('/text2sql-sse/save-history', request);\n      return response.data;\n    } catch (error) {\n      console.error('通过SSE保存聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 获取指定会话的聊天历史\n   */\n  async getChatHistory(sessionId) {\n    try {\n      const response = await api.get(`/chat-history/${sessionId}`);\n      return response.data;\n    } catch (error) {\n      console.error('获取聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 删除聊天历史\n   */\n  async deleteChatHistory(sessionId) {\n    try {\n      const response = await api.delete(`/chat-history/${sessionId}`);\n      return response.data;\n    } catch (error) {\n      console.error('删除聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 恢复聊天历史\n   */\n  async restoreChatHistory(sessionId) {\n    try {\n      const response = await api.post(`/chat-history/${sessionId}/restore`);\n      return response.data;\n    } catch (error) {\n      console.error('恢复聊天历史失败:', error);\n      throw error;\n    }\n  }\n}\n\n// 创建服务实例\nexport const chatHistoryService = new ChatHistoryService();\n\n// 导出默认实例\nexport default chatHistoryService;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "ChatHistoryService", "getChatHistories", "skip", "limit", "connectionId", "params", "undefined", "connection_id", "response", "get", "data", "error", "console", "saveChatHistory", "request", "post", "saveChatHistoryViaSSE", "getChatHistory", "sessionId", "deleteChatHistory", "delete", "restoreChatHistory", "chatHistoryService"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/src/services/chatHistoryService.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\n// 创建API实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n});\n\n// 聊天历史接口类型定义\nexport interface ChatHistoryResponse {\n  id: string;\n  title: string;\n  timestamp: string;\n  query: string;\n  response: {\n    analysis: string;\n    sql: string;\n    explanation: string;\n    data: any[];\n    visualization: any;\n  };\n  connection_id?: number | null;\n}\n\nexport interface ChatHistoryListResponse {\n  sessions: ChatHistoryResponse[];\n  total: number;\n  page: number;\n  page_size: number;\n}\n\nexport interface SaveChatHistoryRequest {\n  session_id: string;\n  title: string;\n  query: string;\n  response: {\n    analysis: string;\n    sql: string;\n    explanation: string;\n    data: any[];\n    visualization: any;\n  };\n  connection_id?: number | null;\n}\n\nexport interface RestoreChatHistoryResponse {\n  session_id: string;\n  title: string;\n  query: string;\n  response: {\n    analysis: string;\n    sql: string;\n    explanation: string;\n    data: any[];\n    visualization: any;\n  };\n  connection_id?: number | null;\n  created_at: string;\n}\n\n// 聊天历史服务类\nexport class ChatHistoryService {\n  /**\n   * 获取聊天历史列表\n   */\n  async getChatHistories(\n    skip: number = 0,\n    limit: number = 50,\n    connectionId?: number\n  ): Promise<ChatHistoryListResponse> {\n    try {\n      const params: any = { skip, limit };\n      if (connectionId !== undefined) {\n        params.connection_id = connectionId;\n      }\n\n      const response = await api.get('/chat-history/', { params });\n      return response.data;\n    } catch (error) {\n      console.error('获取聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 保存聊天历史\n   */\n  async saveChatHistory(request: SaveChatHistoryRequest): Promise<{ status: string; message: string }> {\n    try {\n      const response = await api.post('/chat-history/save', request);\n      return response.data;\n    } catch (error) {\n      console.error('保存聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 通过SSE端点保存聊天历史\n   */\n  async saveChatHistoryViaSSE(request: SaveChatHistoryRequest): Promise<{ status: string; message: string }> {\n    try {\n      const response = await api.post('/text2sql-sse/save-history', request);\n      return response.data;\n    } catch (error) {\n      console.error('通过SSE保存聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 获取指定会话的聊天历史\n   */\n  async getChatHistory(sessionId: string): Promise<RestoreChatHistoryResponse> {\n    try {\n      const response = await api.get(`/chat-history/${sessionId}`);\n      return response.data;\n    } catch (error) {\n      console.error('获取聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 删除聊天历史\n   */\n  async deleteChatHistory(sessionId: string): Promise<{ status: string; message: string }> {\n    try {\n      const response = await api.delete(`/chat-history/${sessionId}`);\n      return response.data;\n    } catch (error) {\n      console.error('删除聊天历史失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 恢复聊天历史\n   */\n  async restoreChatHistory(sessionId: string): Promise<{ status: string; message: string }> {\n    try {\n      const response = await api.post(`/chat-history/${sessionId}/restore`);\n      return response.data;\n    } catch (error) {\n      console.error('恢复聊天历史失败:', error);\n      throw error;\n    }\n  }\n}\n\n// 创建服务实例\nexport const chatHistoryService = new ChatHistoryService();\n\n// 导出默认实例\nexport default chatHistoryService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH;AACX,CAAC,CAAC;;AAEF;;AAoDA;AACA,OAAO,MAAMI,kBAAkB,CAAC;EAC9B;AACF;AACA;EACE,MAAMC,gBAAgBA,CACpBC,IAAY,GAAG,CAAC,EAChBC,KAAa,GAAG,EAAE,EAClBC,YAAqB,EACa;IAClC,IAAI;MACF,MAAMC,MAAW,GAAG;QAAEH,IAAI;QAAEC;MAAM,CAAC;MACnC,IAAIC,YAAY,KAAKE,SAAS,EAAE;QAC9BD,MAAM,CAACE,aAAa,GAAGH,YAAY;MACrC;MAEA,MAAMI,QAAQ,GAAG,MAAMX,GAAG,CAACY,GAAG,CAAC,gBAAgB,EAAE;QAAEJ;MAAO,CAAC,CAAC;MAC5D,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAME,eAAeA,CAACC,OAA+B,EAAgD;IACnG,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMX,GAAG,CAACkB,IAAI,CAAC,oBAAoB,EAAED,OAAO,CAAC;MAC9D,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMK,qBAAqBA,CAACF,OAA+B,EAAgD;IACzG,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMX,GAAG,CAACkB,IAAI,CAAC,4BAA4B,EAAED,OAAO,CAAC;MACtE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMM,cAAcA,CAACC,SAAiB,EAAuC;IAC3E,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMX,GAAG,CAACY,GAAG,CAAC,iBAAiBS,SAAS,EAAE,CAAC;MAC5D,OAAOV,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMQ,iBAAiBA,CAACD,SAAiB,EAAgD;IACvF,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMX,GAAG,CAACuB,MAAM,CAAC,iBAAiBF,SAAS,EAAE,CAAC;MAC/D,OAAOV,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMU,kBAAkBA,CAACH,SAAiB,EAAgD;IACxF,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMX,GAAG,CAACkB,IAAI,CAAC,iBAAiBG,SAAS,UAAU,CAAC;MACrE,OAAOV,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,OAAO,MAAMW,kBAAkB,GAAG,IAAItB,kBAAkB,CAAC,CAAC;;AAE1D;AACA,eAAesB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}