{"ast": null, "code": "'use strict';\n\nvar refractorMarkupTemplating = require('./markup-templating.js');\nmodule.exports = twig;\ntwig.displayName = 'twig';\ntwig.aliases = [];\nfunction twig(Prism) {\n  Prism.register(refractorMarkupTemplating);\n  Prism.languages.twig = {\n    comment: /^\\{#[\\s\\S]*?#\\}$/,\n    'tag-name': {\n      pattern: /(^\\{%-?\\s*)\\w+/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    delimiter: {\n      pattern: /^\\{[{%]-?|-?[%}]\\}$/,\n      alias: 'punctuation'\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      inside: {\n        punctuation: /^['\"]|['\"]$/\n      }\n    },\n    keyword: /\\b(?:even|if|odd)\\b/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n    operator: [{\n      pattern: /(\\s)(?:and|b-and|b-or|b-xor|ends with|in|is|matches|not|or|same as|starts with)(?=\\s)/,\n      lookbehind: true\n    }, /[=<>]=?|!=|\\*\\*?|\\/\\/?|\\?:?|[-+~%|]/],\n    punctuation: /[()\\[\\]{}:.,]/\n  };\n  Prism.hooks.add('before-tokenize', function (env) {\n    if (env.language !== 'twig') {\n      return;\n    }\n    var pattern = /\\{(?:#[\\s\\S]*?#|%[\\s\\S]*?%|\\{[\\s\\S]*?\\})\\}/g;\n    Prism.languages['markup-templating'].buildPlaceholders(env, 'twig', pattern);\n  });\n  Prism.hooks.add('after-tokenize', function (env) {\n    Prism.languages['markup-templating'].tokenizePlaceholders(env, 'twig');\n  });\n}", "map": {"version": 3, "names": ["refractorMarkupTemplating", "require", "module", "exports", "twig", "displayName", "aliases", "Prism", "register", "languages", "comment", "pattern", "lookbehind", "alias", "delimiter", "string", "inside", "punctuation", "keyword", "boolean", "number", "operator", "hooks", "add", "env", "language", "buildPlaceholders", "tokenizePlaceholders"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/refractor/lang/twig.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = twig\ntwig.displayName = 'twig'\ntwig.aliases = []\nfunction twig(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  Prism.languages.twig = {\n    comment: /^\\{#[\\s\\S]*?#\\}$/,\n    'tag-name': {\n      pattern: /(^\\{%-?\\s*)\\w+/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    delimiter: {\n      pattern: /^\\{[{%]-?|-?[%}]\\}$/,\n      alias: 'punctuation'\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      inside: {\n        punctuation: /^['\"]|['\"]$/\n      }\n    },\n    keyword: /\\b(?:even|if|odd)\\b/,\n    boolean: /\\b(?:false|null|true)\\b/,\n    number: /\\b0x[\\dA-Fa-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[Ee][-+]?\\d+)?/,\n    operator: [\n      {\n        pattern:\n          /(\\s)(?:and|b-and|b-or|b-xor|ends with|in|is|matches|not|or|same as|starts with)(?=\\s)/,\n        lookbehind: true\n      },\n      /[=<>]=?|!=|\\*\\*?|\\/\\/?|\\?:?|[-+~%|]/\n    ],\n    punctuation: /[()\\[\\]{}:.,]/\n  }\n  Prism.hooks.add('before-tokenize', function (env) {\n    if (env.language !== 'twig') {\n      return\n    }\n    var pattern = /\\{(?:#[\\s\\S]*?#|%[\\s\\S]*?%|\\{[\\s\\S]*?\\})\\}/g\n    Prism.languages['markup-templating'].buildPlaceholders(env, 'twig', pattern)\n  })\n  Prism.hooks.add('after-tokenize', function (env) {\n    Prism.languages['markup-templating'].tokenizePlaceholders(env, 'twig')\n  })\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,yBAAyB,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AACjEC,MAAM,CAACC,OAAO,GAAGC,IAAI;AACrBA,IAAI,CAACC,WAAW,GAAG,MAAM;AACzBD,IAAI,CAACE,OAAO,GAAG,EAAE;AACjB,SAASF,IAAIA,CAACG,KAAK,EAAE;EACnBA,KAAK,CAACC,QAAQ,CAACR,yBAAyB,CAAC;EACzCO,KAAK,CAACE,SAAS,CAACL,IAAI,GAAG;IACrBM,OAAO,EAAE,kBAAkB;IAC3B,UAAU,EAAE;MACVC,OAAO,EAAE,gBAAgB;MACzBC,UAAU,EAAE,IAAI;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,SAAS,EAAE;MACTH,OAAO,EAAE,qBAAqB;MAC9BE,KAAK,EAAE;IACT,CAAC;IACDE,MAAM,EAAE;MACNJ,OAAO,EAAE,iCAAiC;MAC1CK,MAAM,EAAE;QACNC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,OAAO,EAAE,qBAAqB;IAC9BC,OAAO,EAAE,yBAAyB;IAClCC,MAAM,EAAE,8DAA8D;IACtEC,QAAQ,EAAE,CACR;MACEV,OAAO,EACL,uFAAuF;MACzFC,UAAU,EAAE;IACd,CAAC,EACD,qCAAqC,CACtC;IACDK,WAAW,EAAE;EACf,CAAC;EACDV,KAAK,CAACe,KAAK,CAACC,GAAG,CAAC,iBAAiB,EAAE,UAAUC,GAAG,EAAE;IAChD,IAAIA,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MAC3B;IACF;IACA,IAAId,OAAO,GAAG,6CAA6C;IAC3DJ,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACiB,iBAAiB,CAACF,GAAG,EAAE,MAAM,EAAEb,OAAO,CAAC;EAC9E,CAAC,CAAC;EACFJ,KAAK,CAACe,KAAK,CAACC,GAAG,CAAC,gBAAgB,EAAE,UAAUC,GAAG,EAAE;IAC/CjB,KAAK,CAACE,SAAS,CAAC,mBAAmB,CAAC,CAACkB,oBAAoB,CAACH,GAAG,EAAE,MAAM,CAAC;EACxE,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}