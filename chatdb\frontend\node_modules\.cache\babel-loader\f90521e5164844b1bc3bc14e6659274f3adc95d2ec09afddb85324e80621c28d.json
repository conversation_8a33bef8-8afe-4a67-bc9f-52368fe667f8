{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// TODO: https://www.w3.org/TR/2017/NOTE-wai-aria-practices-1.1-20171214/examples/treeview/treeview-2/treeview-2a.html\n// Fully accessibility support\n\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { TreeContext } from \"./contextTypes\";\nimport DropIndicator from \"./DropIndicator\";\nimport NodeList, { MOTION_KEY, MotionEntity } from \"./NodeList\";\nimport TreeNode from \"./TreeNode\";\nimport { arrAdd, arrDel, calcDropPosition, calcSelectedKeys, conductExpandParent, getDragChildrenKeys, parseCheckedKeys, posToArr } from \"./util\";\nimport { conductCheck } from \"./utils/conductUtil\";\nimport getEntity from \"./utils/keyUtil\";\nimport { convertDataToEntities, convertNodePropsToEventData, convertTreeToData, fillFieldNames, flattenTreeData, getTreeNodeProps, warningWithoutKey } from \"./utils/treeUtil\";\nvar MAX_RETRY_TIMES = 10;\nvar Tree = /*#__PURE__*/function (_React$Component) {\n  _inherits(Tree, _React$Component);\n  var _super = _createSuper(Tree);\n  function Tree() {\n    var _this;\n    _classCallCheck(this, Tree);\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(_args));\n    _defineProperty(_assertThisInitialized(_this), \"destroyed\", false);\n    _defineProperty(_assertThisInitialized(_this), \"delayedDragEnterLogic\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"loadingRetryTimes\", {});\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      keyEntities: {},\n      indent: null,\n      selectedKeys: [],\n      checkedKeys: [],\n      halfCheckedKeys: [],\n      loadedKeys: [],\n      loadingKeys: [],\n      expandedKeys: [],\n      draggingNodeKey: null,\n      dragChildrenKeys: [],\n      // dropTargetKey is the key of abstract-drop-node\n      // the abstract-drop-node is the real drop node when drag and drop\n      // not the DOM drag over node\n      dropTargetKey: null,\n      dropPosition: null,\n      // the drop position of abstract-drop-node, inside 0, top -1, bottom 1\n      dropContainerKey: null,\n      // the container key of abstract-drop-node if dropPosition is -1 or 1\n      dropLevelOffset: null,\n      // the drop level offset of abstract-drag-over-node\n      dropTargetPos: null,\n      // the pos of abstract-drop-node\n      dropAllowed: true,\n      // if drop to abstract-drop-node is allowed\n      // the abstract-drag-over-node\n      // if mouse is on the bottom of top dom node or no the top of the bottom dom node\n      // abstract-drag-over-node is the top node\n      dragOverNodeKey: null,\n      treeData: [],\n      flattenNodes: [],\n      focused: false,\n      activeKey: null,\n      listChanging: false,\n      prevProps: null,\n      fieldNames: fillFieldNames()\n    });\n    _defineProperty(_assertThisInitialized(_this), \"dragStartMousePosition\", null);\n    _defineProperty(_assertThisInitialized(_this), \"dragNodeProps\", null);\n    _defineProperty(_assertThisInitialized(_this), \"currentMouseOverDroppableNodeKey\", null);\n    _defineProperty(_assertThisInitialized(_this), \"listRef\", /*#__PURE__*/React.createRef());\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragStart\", function (event, nodeProps) {\n      var _this$state = _this.state,\n        expandedKeys = _this$state.expandedKeys,\n        keyEntities = _this$state.keyEntities;\n      var onDragStart = _this.props.onDragStart;\n      var eventKey = nodeProps.eventKey;\n      _this.dragNodeProps = nodeProps;\n      _this.dragStartMousePosition = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      var newExpandedKeys = arrDel(expandedKeys, eventKey);\n      _this.setState({\n        draggingNodeKey: eventKey,\n        dragChildrenKeys: getDragChildrenKeys(eventKey, keyEntities),\n        indent: _this.listRef.current.getIndentWidth()\n      });\n      _this.setExpandedKeys(newExpandedKeys);\n      window.addEventListener('dragend', _this.onWindowDragEnd);\n      onDragStart === null || onDragStart === void 0 || onDragStart({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps)\n      });\n    });\n    /**\n     * [Legacy] Select handler is smaller than node,\n     * so that this will trigger when drag enter node or select handler.\n     * This is a little tricky if customize css without padding.\n     * Better for use mouse move event to refresh drag state.\n     * But let's just keep it to avoid event trigger logic change.\n     */\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragEnter\", function (event, nodeProps) {\n      var _this$state2 = _this.state,\n        expandedKeys = _this$state2.expandedKeys,\n        keyEntities = _this$state2.keyEntities,\n        dragChildrenKeys = _this$state2.dragChildrenKeys,\n        flattenNodes = _this$state2.flattenNodes,\n        indent = _this$state2.indent;\n      var _this$props = _this.props,\n        onDragEnter = _this$props.onDragEnter,\n        onExpand = _this$props.onExpand,\n        allowDrop = _this$props.allowDrop,\n        direction = _this$props.direction;\n      var pos = nodeProps.pos,\n        eventKey = nodeProps.eventKey;\n\n      // record the key of node which is latest entered, used in dragleave event.\n      if (_this.currentMouseOverDroppableNodeKey !== eventKey) {\n        _this.currentMouseOverDroppableNodeKey = eventKey;\n      }\n      if (!_this.dragNodeProps) {\n        _this.resetDragState();\n        return;\n      }\n      var _calcDropPosition = calcDropPosition(event, _this.dragNodeProps, nodeProps, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition.dropPosition,\n        dropLevelOffset = _calcDropPosition.dropLevelOffset,\n        dropTargetKey = _calcDropPosition.dropTargetKey,\n        dropContainerKey = _calcDropPosition.dropContainerKey,\n        dropTargetPos = _calcDropPosition.dropTargetPos,\n        dropAllowed = _calcDropPosition.dropAllowed,\n        dragOverNodeKey = _calcDropPosition.dragOverNodeKey;\n      if (\n      // don't allow drop inside its children\n      dragChildrenKeys.includes(dropTargetKey) ||\n      // don't allow drop when drop is not allowed caculated by calcDropPosition\n      !dropAllowed) {\n        _this.resetDragState();\n        return;\n      }\n\n      // Side effect for delay drag\n      if (!_this.delayedDragEnterLogic) {\n        _this.delayedDragEnterLogic = {};\n      }\n      Object.keys(_this.delayedDragEnterLogic).forEach(function (key) {\n        clearTimeout(_this.delayedDragEnterLogic[key]);\n      });\n      if (_this.dragNodeProps.eventKey !== nodeProps.eventKey) {\n        // hoist expand logic here\n        // since if logic is on the bottom\n        // it will be blocked by abstract dragover node check\n        //   => if you dragenter from top, you mouse will still be consider as in the top node\n        event.persist();\n        _this.delayedDragEnterLogic[pos] = window.setTimeout(function () {\n          if (_this.state.draggingNodeKey === null) {\n            return;\n          }\n          var newExpandedKeys = _toConsumableArray(expandedKeys);\n          var entity = getEntity(keyEntities, nodeProps.eventKey);\n          if (entity && (entity.children || []).length) {\n            newExpandedKeys = arrAdd(expandedKeys, nodeProps.eventKey);\n          }\n          if (!_this.props.hasOwnProperty('expandedKeys')) {\n            _this.setExpandedKeys(newExpandedKeys);\n          }\n          onExpand === null || onExpand === void 0 || onExpand(newExpandedKeys, {\n            node: convertNodePropsToEventData(nodeProps),\n            expanded: true,\n            nativeEvent: event.nativeEvent\n          });\n        }, 800);\n      }\n\n      // Skip if drag node is self\n      if (_this.dragNodeProps.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        _this.resetDragState();\n        return;\n      }\n\n      // Update drag over node and drag state\n      _this.setState({\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        dropTargetKey: dropTargetKey,\n        dropContainerKey: dropContainerKey,\n        dropTargetPos: dropTargetPos,\n        dropAllowed: dropAllowed\n      });\n      onDragEnter === null || onDragEnter === void 0 || onDragEnter({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps),\n        expandedKeys: expandedKeys\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragOver\", function (event, nodeProps) {\n      var _this$state3 = _this.state,\n        dragChildrenKeys = _this$state3.dragChildrenKeys,\n        flattenNodes = _this$state3.flattenNodes,\n        keyEntities = _this$state3.keyEntities,\n        expandedKeys = _this$state3.expandedKeys,\n        indent = _this$state3.indent;\n      var _this$props2 = _this.props,\n        onDragOver = _this$props2.onDragOver,\n        allowDrop = _this$props2.allowDrop,\n        direction = _this$props2.direction;\n      if (!_this.dragNodeProps) {\n        return;\n      }\n      var _calcDropPosition2 = calcDropPosition(event, _this.dragNodeProps, nodeProps, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition2.dropPosition,\n        dropLevelOffset = _calcDropPosition2.dropLevelOffset,\n        dropTargetKey = _calcDropPosition2.dropTargetKey,\n        dropContainerKey = _calcDropPosition2.dropContainerKey,\n        dropTargetPos = _calcDropPosition2.dropTargetPos,\n        dropAllowed = _calcDropPosition2.dropAllowed,\n        dragOverNodeKey = _calcDropPosition2.dragOverNodeKey;\n      if (dragChildrenKeys.includes(dropTargetKey) || !dropAllowed) {\n        // don't allow drop inside its children\n        // don't allow drop when drop is not allowed calculated by calcDropPosition\n        return;\n      }\n\n      // Update drag position\n\n      if (_this.dragNodeProps.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        if (!(_this.state.dropPosition === null && _this.state.dropLevelOffset === null && _this.state.dropTargetKey === null && _this.state.dropContainerKey === null && _this.state.dropTargetPos === null && _this.state.dropAllowed === false && _this.state.dragOverNodeKey === null)) {\n          _this.resetDragState();\n        }\n      } else if (!(dropPosition === _this.state.dropPosition && dropLevelOffset === _this.state.dropLevelOffset && dropTargetKey === _this.state.dropTargetKey && dropContainerKey === _this.state.dropContainerKey && dropTargetPos === _this.state.dropTargetPos && dropAllowed === _this.state.dropAllowed && dragOverNodeKey === _this.state.dragOverNodeKey)) {\n        _this.setState({\n          dropPosition: dropPosition,\n          dropLevelOffset: dropLevelOffset,\n          dropTargetKey: dropTargetKey,\n          dropContainerKey: dropContainerKey,\n          dropTargetPos: dropTargetPos,\n          dropAllowed: dropAllowed,\n          dragOverNodeKey: dragOverNodeKey\n        });\n      }\n      onDragOver === null || onDragOver === void 0 || onDragOver({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps)\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragLeave\", function (event, nodeProps) {\n      // if it is outside the droppable area\n      // currentMouseOverDroppableNodeKey will be updated in dragenter event when into another droppable receiver.\n      if (_this.currentMouseOverDroppableNodeKey === nodeProps.eventKey && !event.currentTarget.contains(event.relatedTarget)) {\n        _this.resetDragState();\n        _this.currentMouseOverDroppableNodeKey = null;\n      }\n      var onDragLeave = _this.props.onDragLeave;\n      onDragLeave === null || onDragLeave === void 0 || onDragLeave({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps)\n      });\n    });\n    // since stopPropagation() is called in treeNode\n    // if onWindowDrag is called, whice means state is keeped, drag state should be cleared\n    _defineProperty(_assertThisInitialized(_this), \"onWindowDragEnd\", function (event) {\n      _this.onNodeDragEnd(event, null, true);\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    });\n    // if onNodeDragEnd is called, onWindowDragEnd won't be called since stopPropagation() is called\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragEnd\", function (event, nodeProps) {\n      var onDragEnd = _this.props.onDragEnd;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      onDragEnd === null || onDragEnd === void 0 || onDragEnd({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps)\n      });\n      _this.dragNodeProps = null;\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDrop\", function (event, _) {\n      var _this$getActiveItem;\n      var outsideTree = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var _this$state4 = _this.state,\n        dragChildrenKeys = _this$state4.dragChildrenKeys,\n        dropPosition = _this$state4.dropPosition,\n        dropTargetKey = _this$state4.dropTargetKey,\n        dropTargetPos = _this$state4.dropTargetPos,\n        dropAllowed = _this$state4.dropAllowed;\n      if (!dropAllowed) {\n        return;\n      }\n      var onDrop = _this.props.onDrop;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      if (dropTargetKey === null) return;\n      var abstractDropNodeProps = _objectSpread(_objectSpread({}, getTreeNodeProps(dropTargetKey, _this.getTreeNodeRequiredProps())), {}, {\n        active: ((_this$getActiveItem = _this.getActiveItem()) === null || _this$getActiveItem === void 0 ? void 0 : _this$getActiveItem.key) === dropTargetKey,\n        data: getEntity(_this.state.keyEntities, dropTargetKey).node\n      });\n      var dropToChild = dragChildrenKeys.includes(dropTargetKey);\n      warning(!dropToChild, \"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.\");\n      var posArr = posToArr(dropTargetPos);\n      var dropResult = {\n        event: event,\n        node: convertNodePropsToEventData(abstractDropNodeProps),\n        dragNode: _this.dragNodeProps ? convertNodePropsToEventData(_this.dragNodeProps) : null,\n        dragNodesKeys: [_this.dragNodeProps.eventKey].concat(dragChildrenKeys),\n        dropToGap: dropPosition !== 0,\n        dropPosition: dropPosition + Number(posArr[posArr.length - 1])\n      };\n      if (!outsideTree) {\n        onDrop === null || onDrop === void 0 || onDrop(dropResult);\n      }\n      _this.dragNodeProps = null;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"cleanDragState\", function () {\n      var draggingNodeKey = _this.state.draggingNodeKey;\n      if (draggingNodeKey !== null) {\n        _this.setState({\n          draggingNodeKey: null,\n          dropPosition: null,\n          dropContainerKey: null,\n          dropTargetKey: null,\n          dropLevelOffset: null,\n          dropAllowed: true,\n          dragOverNodeKey: null\n        });\n      }\n      _this.dragStartMousePosition = null;\n      _this.currentMouseOverDroppableNodeKey = null;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"triggerExpandActionExpand\", function (e, treeNode) {\n      var _this$state5 = _this.state,\n        expandedKeys = _this$state5.expandedKeys,\n        flattenNodes = _this$state5.flattenNodes;\n      var expanded = treeNode.expanded,\n        key = treeNode.key,\n        isLeaf = treeNode.isLeaf;\n      if (isLeaf || e.shiftKey || e.metaKey || e.ctrlKey) {\n        return;\n      }\n      var node = flattenNodes.filter(function (nodeItem) {\n        return nodeItem.key === key;\n      })[0];\n      var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(key, _this.getTreeNodeRequiredProps())), {}, {\n        data: node.data\n      }));\n      _this.setExpandedKeys(expanded ? arrDel(expandedKeys, key) : arrAdd(expandedKeys, key));\n      _this.onNodeExpand(e, eventNode);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeClick\", function (e, treeNode) {\n      var _this$props3 = _this.props,\n        onClick = _this$props3.onClick,\n        expandAction = _this$props3.expandAction;\n      if (expandAction === 'click') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onClick === null || onClick === void 0 || onClick(e, treeNode);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDoubleClick\", function (e, treeNode) {\n      var _this$props4 = _this.props,\n        onDoubleClick = _this$props4.onDoubleClick,\n        expandAction = _this$props4.expandAction;\n      if (expandAction === 'doubleClick') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onDoubleClick === null || onDoubleClick === void 0 || onDoubleClick(e, treeNode);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeSelect\", function (e, treeNode) {\n      var selectedKeys = _this.state.selectedKeys;\n      var _this$state6 = _this.state,\n        keyEntities = _this$state6.keyEntities,\n        fieldNames = _this$state6.fieldNames;\n      var _this$props5 = _this.props,\n        onSelect = _this$props5.onSelect,\n        multiple = _this$props5.multiple;\n      var selected = treeNode.selected;\n      var key = treeNode[fieldNames.key];\n      var targetSelected = !selected;\n\n      // Update selected keys\n      if (!targetSelected) {\n        selectedKeys = arrDel(selectedKeys, key);\n      } else if (!multiple) {\n        selectedKeys = [key];\n      } else {\n        selectedKeys = arrAdd(selectedKeys, key);\n      }\n\n      // [Legacy] Not found related usage in doc or upper libs\n      var selectedNodes = selectedKeys.map(function (selectedKey) {\n        var entity = getEntity(keyEntities, selectedKey);\n        return entity ? entity.node : null;\n      }).filter(Boolean);\n      _this.setUncontrolledState({\n        selectedKeys: selectedKeys\n      });\n      onSelect === null || onSelect === void 0 || onSelect(selectedKeys, {\n        event: 'select',\n        selected: targetSelected,\n        node: treeNode,\n        selectedNodes: selectedNodes,\n        nativeEvent: e.nativeEvent\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeCheck\", function (e, treeNode, checked) {\n      var _this$state7 = _this.state,\n        keyEntities = _this$state7.keyEntities,\n        oriCheckedKeys = _this$state7.checkedKeys,\n        oriHalfCheckedKeys = _this$state7.halfCheckedKeys;\n      var _this$props6 = _this.props,\n        checkStrictly = _this$props6.checkStrictly,\n        onCheck = _this$props6.onCheck;\n      var key = treeNode.key;\n\n      // Prepare trigger arguments\n      var checkedObj;\n      var eventObj = {\n        event: 'check',\n        node: treeNode,\n        checked: checked,\n        nativeEvent: e.nativeEvent\n      };\n      if (checkStrictly) {\n        var checkedKeys = checked ? arrAdd(oriCheckedKeys, key) : arrDel(oriCheckedKeys, key);\n        var halfCheckedKeys = arrDel(oriHalfCheckedKeys, key);\n        checkedObj = {\n          checked: checkedKeys,\n          halfChecked: halfCheckedKeys\n        };\n        eventObj.checkedNodes = checkedKeys.map(function (checkedKey) {\n          return getEntity(keyEntities, checkedKey);\n        }).filter(Boolean).map(function (entity) {\n          return entity.node;\n        });\n        _this.setUncontrolledState({\n          checkedKeys: checkedKeys\n        });\n      } else {\n        // Always fill first\n        var _conductCheck = conductCheck([].concat(_toConsumableArray(oriCheckedKeys), [key]), true, keyEntities),\n          _checkedKeys = _conductCheck.checkedKeys,\n          _halfCheckedKeys = _conductCheck.halfCheckedKeys;\n\n        // If remove, we do it again to correction\n        if (!checked) {\n          var keySet = new Set(_checkedKeys);\n          keySet.delete(key);\n          var _conductCheck2 = conductCheck(Array.from(keySet), {\n            checked: false,\n            halfCheckedKeys: _halfCheckedKeys\n          }, keyEntities);\n          _checkedKeys = _conductCheck2.checkedKeys;\n          _halfCheckedKeys = _conductCheck2.halfCheckedKeys;\n        }\n        checkedObj = _checkedKeys;\n\n        // [Legacy] This is used for `rc-tree-select`\n        eventObj.checkedNodes = [];\n        eventObj.checkedNodesPositions = [];\n        eventObj.halfCheckedKeys = _halfCheckedKeys;\n        _checkedKeys.forEach(function (checkedKey) {\n          var entity = getEntity(keyEntities, checkedKey);\n          if (!entity) return;\n          var node = entity.node,\n            pos = entity.pos;\n          eventObj.checkedNodes.push(node);\n          eventObj.checkedNodesPositions.push({\n            node: node,\n            pos: pos\n          });\n        });\n        _this.setUncontrolledState({\n          checkedKeys: _checkedKeys\n        }, false, {\n          halfCheckedKeys: _halfCheckedKeys\n        });\n      }\n      onCheck === null || onCheck === void 0 || onCheck(checkedObj, eventObj);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeLoad\", function (treeNode) {\n      var _entity$children;\n      var key = treeNode.key;\n      var keyEntities = _this.state.keyEntities;\n\n      // Skip if has children already\n      var entity = getEntity(keyEntities, key);\n      if (entity !== null && entity !== void 0 && (_entity$children = entity.children) !== null && _entity$children !== void 0 && _entity$children.length) {\n        return;\n      }\n      var loadPromise = new Promise(function (resolve, reject) {\n        // We need to get the latest state of loading/loaded keys\n        _this.setState(function (_ref) {\n          var _ref$loadedKeys = _ref.loadedKeys,\n            loadedKeys = _ref$loadedKeys === void 0 ? [] : _ref$loadedKeys,\n            _ref$loadingKeys = _ref.loadingKeys,\n            loadingKeys = _ref$loadingKeys === void 0 ? [] : _ref$loadingKeys;\n          var _this$props7 = _this.props,\n            loadData = _this$props7.loadData,\n            onLoad = _this$props7.onLoad;\n          if (!loadData || loadedKeys.includes(key) || loadingKeys.includes(key)) {\n            return null;\n          }\n\n          // Process load data\n          var promise = loadData(treeNode);\n          promise.then(function () {\n            var currentLoadedKeys = _this.state.loadedKeys;\n            var newLoadedKeys = arrAdd(currentLoadedKeys, key);\n\n            // onLoad should trigger before internal setState to avoid `loadData` trigger twice.\n            // https://github.com/ant-design/ant-design/issues/12464\n            onLoad === null || onLoad === void 0 || onLoad(newLoadedKeys, {\n              event: 'load',\n              node: treeNode\n            });\n            _this.setUncontrolledState({\n              loadedKeys: newLoadedKeys\n            });\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n            resolve();\n          }).catch(function (e) {\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n\n            // If exceed max retry times, we give up retry\n            _this.loadingRetryTimes[key] = (_this.loadingRetryTimes[key] || 0) + 1;\n            if (_this.loadingRetryTimes[key] >= MAX_RETRY_TIMES) {\n              var currentLoadedKeys = _this.state.loadedKeys;\n              warning(false, 'Retry for `loadData` many times but still failed. No more retry.');\n              _this.setUncontrolledState({\n                loadedKeys: arrAdd(currentLoadedKeys, key)\n              });\n              resolve();\n            }\n            reject(e);\n          });\n          return {\n            loadingKeys: arrAdd(loadingKeys, key)\n          };\n        });\n      });\n\n      // Not care warning if we ignore this\n      loadPromise.catch(function () {});\n      return loadPromise;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeMouseEnter\", function (event, node) {\n      var onMouseEnter = _this.props.onMouseEnter;\n      onMouseEnter === null || onMouseEnter === void 0 || onMouseEnter({\n        event: event,\n        node: node\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeMouseLeave\", function (event, node) {\n      var onMouseLeave = _this.props.onMouseLeave;\n      onMouseLeave === null || onMouseLeave === void 0 || onMouseLeave({\n        event: event,\n        node: node\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeContextMenu\", function (event, node) {\n      var onRightClick = _this.props.onRightClick;\n      if (onRightClick) {\n        event.preventDefault();\n        onRightClick({\n          event: event,\n          node: node\n        });\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onFocus\", function () {\n      var onFocus = _this.props.onFocus;\n      _this.setState({\n        focused: true\n      });\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      onFocus === null || onFocus === void 0 || onFocus.apply(void 0, args);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onBlur\", function () {\n      var onBlur = _this.props.onBlur;\n      _this.setState({\n        focused: false\n      });\n      _this.onActiveChange(null);\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      onBlur === null || onBlur === void 0 || onBlur.apply(void 0, args);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getTreeNodeRequiredProps\", function () {\n      var _this$state8 = _this.state,\n        expandedKeys = _this$state8.expandedKeys,\n        selectedKeys = _this$state8.selectedKeys,\n        loadedKeys = _this$state8.loadedKeys,\n        loadingKeys = _this$state8.loadingKeys,\n        checkedKeys = _this$state8.checkedKeys,\n        halfCheckedKeys = _this$state8.halfCheckedKeys,\n        dragOverNodeKey = _this$state8.dragOverNodeKey,\n        dropPosition = _this$state8.dropPosition,\n        keyEntities = _this$state8.keyEntities;\n      return {\n        expandedKeys: expandedKeys || [],\n        selectedKeys: selectedKeys || [],\n        loadedKeys: loadedKeys || [],\n        loadingKeys: loadingKeys || [],\n        checkedKeys: checkedKeys || [],\n        halfCheckedKeys: halfCheckedKeys || [],\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        keyEntities: keyEntities\n      };\n    });\n    // =========================== Expanded ===========================\n    /** Set uncontrolled `expandedKeys`. This will also auto update `flattenNodes`. */\n    _defineProperty(_assertThisInitialized(_this), \"setExpandedKeys\", function (expandedKeys) {\n      var _this$state9 = _this.state,\n        treeData = _this$state9.treeData,\n        fieldNames = _this$state9.fieldNames;\n      var flattenNodes = flattenTreeData(treeData, expandedKeys, fieldNames);\n      _this.setUncontrolledState({\n        expandedKeys: expandedKeys,\n        flattenNodes: flattenNodes\n      }, true);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeExpand\", function (e, treeNode) {\n      var expandedKeys = _this.state.expandedKeys;\n      var _this$state10 = _this.state,\n        listChanging = _this$state10.listChanging,\n        fieldNames = _this$state10.fieldNames;\n      var _this$props8 = _this.props,\n        onExpand = _this$props8.onExpand,\n        loadData = _this$props8.loadData;\n      var expanded = treeNode.expanded;\n      var key = treeNode[fieldNames.key];\n\n      // Do nothing when motion is in progress\n      if (listChanging) {\n        return;\n      }\n\n      // Update selected keys\n      var certain = expandedKeys.includes(key);\n      var targetExpanded = !expanded;\n      warning(expanded && certain || !expanded && !certain, 'Expand state not sync with index check');\n      expandedKeys = targetExpanded ? arrAdd(expandedKeys, key) : arrDel(expandedKeys, key);\n      _this.setExpandedKeys(expandedKeys);\n      onExpand === null || onExpand === void 0 || onExpand(expandedKeys, {\n        node: treeNode,\n        expanded: targetExpanded,\n        nativeEvent: e.nativeEvent\n      });\n\n      // Async Load data\n      if (targetExpanded && loadData) {\n        var loadPromise = _this.onNodeLoad(treeNode);\n        if (loadPromise) {\n          loadPromise.then(function () {\n            // [Legacy] Refresh logic\n            var newFlattenTreeData = flattenTreeData(_this.state.treeData, expandedKeys, fieldNames);\n            _this.setUncontrolledState({\n              flattenNodes: newFlattenTreeData\n            });\n          }).catch(function () {\n            var currentExpandedKeys = _this.state.expandedKeys;\n            var expandedKeysToRestore = arrDel(currentExpandedKeys, key);\n            _this.setExpandedKeys(expandedKeysToRestore);\n          });\n        }\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onListChangeStart\", function () {\n      _this.setUncontrolledState({\n        listChanging: true\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onListChangeEnd\", function () {\n      setTimeout(function () {\n        _this.setUncontrolledState({\n          listChanging: false\n        });\n      });\n    });\n    // =========================== Keyboard ===========================\n    _defineProperty(_assertThisInitialized(_this), \"onActiveChange\", function (newActiveKey) {\n      var activeKey = _this.state.activeKey;\n      var _this$props9 = _this.props,\n        onActiveChange = _this$props9.onActiveChange,\n        _this$props9$itemScro = _this$props9.itemScrollOffset,\n        itemScrollOffset = _this$props9$itemScro === void 0 ? 0 : _this$props9$itemScro;\n      if (activeKey === newActiveKey) {\n        return;\n      }\n      _this.setState({\n        activeKey: newActiveKey\n      });\n      if (newActiveKey !== null) {\n        _this.scrollTo({\n          key: newActiveKey,\n          offset: itemScrollOffset\n        });\n      }\n      onActiveChange === null || onActiveChange === void 0 || onActiveChange(newActiveKey);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getActiveItem\", function () {\n      var _this$state11 = _this.state,\n        activeKey = _this$state11.activeKey,\n        flattenNodes = _this$state11.flattenNodes;\n      if (activeKey === null) {\n        return null;\n      }\n      return flattenNodes.find(function (_ref2) {\n        var key = _ref2.key;\n        return key === activeKey;\n      }) || null;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"offsetActiveKey\", function (offset) {\n      var _this$state12 = _this.state,\n        flattenNodes = _this$state12.flattenNodes,\n        activeKey = _this$state12.activeKey;\n      var index = flattenNodes.findIndex(function (_ref3) {\n        var key = _ref3.key;\n        return key === activeKey;\n      });\n\n      // Align with index\n      if (index === -1 && offset < 0) {\n        index = flattenNodes.length;\n      }\n      index = (index + offset + flattenNodes.length) % flattenNodes.length;\n      var item = flattenNodes[index];\n      if (item) {\n        var _key4 = item.key;\n        _this.onActiveChange(_key4);\n      } else {\n        _this.onActiveChange(null);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onKeyDown\", function (event) {\n      var _this$state13 = _this.state,\n        activeKey = _this$state13.activeKey,\n        expandedKeys = _this$state13.expandedKeys,\n        checkedKeys = _this$state13.checkedKeys,\n        fieldNames = _this$state13.fieldNames;\n      var _this$props10 = _this.props,\n        onKeyDown = _this$props10.onKeyDown,\n        checkable = _this$props10.checkable,\n        selectable = _this$props10.selectable;\n\n      // >>>>>>>>>> Direction\n      switch (event.which) {\n        case KeyCode.UP:\n          {\n            _this.offsetActiveKey(-1);\n            event.preventDefault();\n            break;\n          }\n        case KeyCode.DOWN:\n          {\n            _this.offsetActiveKey(1);\n            event.preventDefault();\n            break;\n          }\n      }\n\n      // >>>>>>>>>> Expand & Selection\n      var activeItem = _this.getActiveItem();\n      if (activeItem && activeItem.data) {\n        var treeNodeRequiredProps = _this.getTreeNodeRequiredProps();\n        var expandable = activeItem.data.isLeaf === false || !!(activeItem.data[fieldNames.children] || []).length;\n        var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(activeKey, treeNodeRequiredProps)), {}, {\n          data: activeItem.data,\n          active: true\n        }));\n        switch (event.which) {\n          // >>> Expand\n          case KeyCode.LEFT:\n            {\n              // Collapse if possible\n              if (expandable && expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.parent) {\n                _this.onActiveChange(activeItem.parent.key);\n              }\n              event.preventDefault();\n              break;\n            }\n          case KeyCode.RIGHT:\n            {\n              // Expand if possible\n              if (expandable && !expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.children && activeItem.children.length) {\n                _this.onActiveChange(activeItem.children[0].key);\n              }\n              event.preventDefault();\n              break;\n            }\n\n          // Selection\n          case KeyCode.ENTER:\n          case KeyCode.SPACE:\n            {\n              if (checkable && !eventNode.disabled && eventNode.checkable !== false && !eventNode.disableCheckbox) {\n                _this.onNodeCheck({}, eventNode, !checkedKeys.includes(activeKey));\n              } else if (!checkable && selectable && !eventNode.disabled && eventNode.selectable !== false) {\n                _this.onNodeSelect({}, eventNode);\n              }\n              break;\n            }\n        }\n      }\n      onKeyDown === null || onKeyDown === void 0 || onKeyDown(event);\n    });\n    /**\n     * Only update the value which is not in props\n     */\n    _defineProperty(_assertThisInitialized(_this), \"setUncontrolledState\", function (state) {\n      var atomic = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var forceState = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      if (!_this.destroyed) {\n        var needSync = false;\n        var allPassed = true;\n        var newState = {};\n        Object.keys(state).forEach(function (name) {\n          if (_this.props.hasOwnProperty(name)) {\n            allPassed = false;\n            return;\n          }\n          needSync = true;\n          newState[name] = state[name];\n        });\n        if (needSync && (!atomic || allPassed)) {\n          _this.setState(_objectSpread(_objectSpread({}, newState), forceState));\n        }\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"scrollTo\", function (scroll) {\n      _this.listRef.current.scrollTo(scroll);\n    });\n    return _this;\n  }\n  _createClass(Tree, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.destroyed = false;\n      this.onUpdated();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.onUpdated();\n    }\n  }, {\n    key: \"onUpdated\",\n    value: function onUpdated() {\n      var _this$props11 = this.props,\n        activeKey = _this$props11.activeKey,\n        _this$props11$itemScr = _this$props11.itemScrollOffset,\n        itemScrollOffset = _this$props11$itemScr === void 0 ? 0 : _this$props11$itemScr;\n      if (activeKey !== undefined && activeKey !== this.state.activeKey) {\n        this.setState({\n          activeKey: activeKey\n        });\n        if (activeKey !== null) {\n          this.scrollTo({\n            key: activeKey,\n            offset: itemScrollOffset\n          });\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      window.removeEventListener('dragend', this.onWindowDragEnd);\n      this.destroyed = true;\n    }\n  }, {\n    key: \"resetDragState\",\n    value: function resetDragState() {\n      this.setState({\n        dragOverNodeKey: null,\n        dropPosition: null,\n        dropLevelOffset: null,\n        dropTargetKey: null,\n        dropContainerKey: null,\n        dropTargetPos: null,\n        dropAllowed: false\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state14 = this.state,\n        focused = _this$state14.focused,\n        flattenNodes = _this$state14.flattenNodes,\n        keyEntities = _this$state14.keyEntities,\n        draggingNodeKey = _this$state14.draggingNodeKey,\n        activeKey = _this$state14.activeKey,\n        dropLevelOffset = _this$state14.dropLevelOffset,\n        dropContainerKey = _this$state14.dropContainerKey,\n        dropTargetKey = _this$state14.dropTargetKey,\n        dropPosition = _this$state14.dropPosition,\n        dragOverNodeKey = _this$state14.dragOverNodeKey,\n        indent = _this$state14.indent;\n      var _this$props12 = this.props,\n        prefixCls = _this$props12.prefixCls,\n        className = _this$props12.className,\n        style = _this$props12.style,\n        showLine = _this$props12.showLine,\n        focusable = _this$props12.focusable,\n        _this$props12$tabInde = _this$props12.tabIndex,\n        tabIndex = _this$props12$tabInde === void 0 ? 0 : _this$props12$tabInde,\n        selectable = _this$props12.selectable,\n        showIcon = _this$props12.showIcon,\n        icon = _this$props12.icon,\n        switcherIcon = _this$props12.switcherIcon,\n        draggable = _this$props12.draggable,\n        checkable = _this$props12.checkable,\n        checkStrictly = _this$props12.checkStrictly,\n        disabled = _this$props12.disabled,\n        motion = _this$props12.motion,\n        loadData = _this$props12.loadData,\n        filterTreeNode = _this$props12.filterTreeNode,\n        height = _this$props12.height,\n        itemHeight = _this$props12.itemHeight,\n        scrollWidth = _this$props12.scrollWidth,\n        virtual = _this$props12.virtual,\n        titleRender = _this$props12.titleRender,\n        dropIndicatorRender = _this$props12.dropIndicatorRender,\n        onContextMenu = _this$props12.onContextMenu,\n        onScroll = _this$props12.onScroll,\n        direction = _this$props12.direction,\n        rootClassName = _this$props12.rootClassName,\n        rootStyle = _this$props12.rootStyle;\n      var domProps = pickAttrs(this.props, {\n        aria: true,\n        data: true\n      });\n\n      // It's better move to hooks but we just simply keep here\n      var draggableConfig;\n      if (draggable) {\n        if (_typeof(draggable) === 'object') {\n          draggableConfig = draggable;\n        } else if (typeof draggable === 'function') {\n          draggableConfig = {\n            nodeDraggable: draggable\n          };\n        } else {\n          draggableConfig = {};\n        }\n      }\n      var contextValue = {\n        prefixCls: prefixCls,\n        selectable: selectable,\n        showIcon: showIcon,\n        icon: icon,\n        switcherIcon: switcherIcon,\n        draggable: draggableConfig,\n        draggingNodeKey: draggingNodeKey,\n        checkable: checkable,\n        checkStrictly: checkStrictly,\n        disabled: disabled,\n        keyEntities: keyEntities,\n        dropLevelOffset: dropLevelOffset,\n        dropContainerKey: dropContainerKey,\n        dropTargetKey: dropTargetKey,\n        dropPosition: dropPosition,\n        dragOverNodeKey: dragOverNodeKey,\n        indent: indent,\n        direction: direction,\n        dropIndicatorRender: dropIndicatorRender,\n        loadData: loadData,\n        filterTreeNode: filterTreeNode,\n        titleRender: titleRender,\n        onNodeClick: this.onNodeClick,\n        onNodeDoubleClick: this.onNodeDoubleClick,\n        onNodeExpand: this.onNodeExpand,\n        onNodeSelect: this.onNodeSelect,\n        onNodeCheck: this.onNodeCheck,\n        onNodeLoad: this.onNodeLoad,\n        onNodeMouseEnter: this.onNodeMouseEnter,\n        onNodeMouseLeave: this.onNodeMouseLeave,\n        onNodeContextMenu: this.onNodeContextMenu,\n        onNodeDragStart: this.onNodeDragStart,\n        onNodeDragEnter: this.onNodeDragEnter,\n        onNodeDragOver: this.onNodeDragOver,\n        onNodeDragLeave: this.onNodeDragLeave,\n        onNodeDragEnd: this.onNodeDragEnd,\n        onNodeDrop: this.onNodeDrop\n      };\n      return /*#__PURE__*/React.createElement(TreeContext.Provider, {\n        value: contextValue\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(prefixCls, className, rootClassName, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-show-line\"), showLine), \"\".concat(prefixCls, \"-focused\"), focused), \"\".concat(prefixCls, \"-active-focused\"), activeKey !== null)),\n        style: rootStyle\n      }, /*#__PURE__*/React.createElement(NodeList, _extends({\n        ref: this.listRef,\n        prefixCls: prefixCls,\n        style: style,\n        data: flattenNodes,\n        disabled: disabled,\n        selectable: selectable,\n        checkable: !!checkable,\n        motion: motion,\n        dragging: draggingNodeKey !== null,\n        height: height,\n        itemHeight: itemHeight,\n        virtual: virtual,\n        focusable: focusable,\n        focused: focused,\n        tabIndex: tabIndex,\n        activeItem: this.getActiveItem(),\n        onFocus: this.onFocus,\n        onBlur: this.onBlur,\n        onKeyDown: this.onKeyDown,\n        onActiveChange: this.onActiveChange,\n        onListChangeStart: this.onListChangeStart,\n        onListChangeEnd: this.onListChangeEnd,\n        onContextMenu: onContextMenu,\n        onScroll: onScroll,\n        scrollWidth: scrollWidth\n      }, this.getTreeNodeRequiredProps(), domProps))));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var prevProps = prevState.prevProps;\n      var newState = {\n        prevProps: props\n      };\n      function needSync(name) {\n        return !prevProps && props.hasOwnProperty(name) || prevProps && prevProps[name] !== props[name];\n      }\n\n      // ================== Tree Node ==================\n      var treeData;\n\n      // fieldNames\n      var fieldNames = prevState.fieldNames;\n      if (needSync('fieldNames')) {\n        fieldNames = fillFieldNames(props.fieldNames);\n        newState.fieldNames = fieldNames;\n      }\n\n      // Check if `treeData` or `children` changed and save into the state.\n      if (needSync('treeData')) {\n        treeData = props.treeData;\n      } else if (needSync('children')) {\n        warning(false, '`children` of Tree is deprecated. Please use `treeData` instead.');\n        treeData = convertTreeToData(props.children);\n      }\n\n      // Save flatten nodes info and convert `treeData` into keyEntities\n      if (treeData) {\n        newState.treeData = treeData;\n        var entitiesMap = convertDataToEntities(treeData, {\n          fieldNames: fieldNames\n        });\n        newState.keyEntities = _objectSpread(_defineProperty({}, MOTION_KEY, MotionEntity), entitiesMap.keyEntities);\n\n        // Warning if treeNode not provide key\n        if (process.env.NODE_ENV !== 'production') {\n          warningWithoutKey(treeData, fieldNames);\n        }\n      }\n      var keyEntities = newState.keyEntities || prevState.keyEntities;\n\n      // ================ expandedKeys =================\n      if (needSync('expandedKeys') || prevProps && needSync('autoExpandParent')) {\n        newState.expandedKeys = props.autoExpandParent || !prevProps && props.defaultExpandParent ? conductExpandParent(props.expandedKeys, keyEntities) : props.expandedKeys;\n      } else if (!prevProps && props.defaultExpandAll) {\n        var cloneKeyEntities = _objectSpread({}, keyEntities);\n        delete cloneKeyEntities[MOTION_KEY];\n\n        // Only take the key who has the children to enhance the performance\n        var nextExpandedKeys = [];\n        Object.keys(cloneKeyEntities).forEach(function (key) {\n          var entity = cloneKeyEntities[key];\n          if (entity.children && entity.children.length) {\n            nextExpandedKeys.push(entity.key);\n          }\n        });\n        newState.expandedKeys = nextExpandedKeys;\n      } else if (!prevProps && props.defaultExpandedKeys) {\n        newState.expandedKeys = props.autoExpandParent || props.defaultExpandParent ? conductExpandParent(props.defaultExpandedKeys, keyEntities) : props.defaultExpandedKeys;\n      }\n      if (!newState.expandedKeys) {\n        delete newState.expandedKeys;\n      }\n\n      // ================ flattenNodes =================\n      if (treeData || newState.expandedKeys) {\n        var flattenNodes = flattenTreeData(treeData || prevState.treeData, newState.expandedKeys || prevState.expandedKeys, fieldNames);\n        newState.flattenNodes = flattenNodes;\n      }\n\n      // ================ selectedKeys =================\n      if (props.selectable) {\n        if (needSync('selectedKeys')) {\n          newState.selectedKeys = calcSelectedKeys(props.selectedKeys, props);\n        } else if (!prevProps && props.defaultSelectedKeys) {\n          newState.selectedKeys = calcSelectedKeys(props.defaultSelectedKeys, props);\n        }\n      }\n\n      // ================= checkedKeys =================\n      if (props.checkable) {\n        var checkedKeyEntity;\n        if (needSync('checkedKeys')) {\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {};\n        } else if (!prevProps && props.defaultCheckedKeys) {\n          checkedKeyEntity = parseCheckedKeys(props.defaultCheckedKeys) || {};\n        } else if (treeData) {\n          // If `treeData` changed, we also need check it\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {\n            checkedKeys: prevState.checkedKeys,\n            halfCheckedKeys: prevState.halfCheckedKeys\n          };\n        }\n        if (checkedKeyEntity) {\n          var _checkedKeyEntity = checkedKeyEntity,\n            _checkedKeyEntity$che = _checkedKeyEntity.checkedKeys,\n            checkedKeys = _checkedKeyEntity$che === void 0 ? [] : _checkedKeyEntity$che,\n            _checkedKeyEntity$hal = _checkedKeyEntity.halfCheckedKeys,\n            halfCheckedKeys = _checkedKeyEntity$hal === void 0 ? [] : _checkedKeyEntity$hal;\n          if (!props.checkStrictly) {\n            var conductKeys = conductCheck(checkedKeys, true, keyEntities);\n            checkedKeys = conductKeys.checkedKeys;\n            halfCheckedKeys = conductKeys.halfCheckedKeys;\n          }\n          newState.checkedKeys = checkedKeys;\n          newState.halfCheckedKeys = halfCheckedKeys;\n        }\n      }\n\n      // ================= loadedKeys ==================\n      if (needSync('loadedKeys')) {\n        newState.loadedKeys = props.loadedKeys;\n      }\n      return newState;\n    }\n  }]);\n  return Tree;\n}(React.Component);\n_defineProperty(Tree, \"defaultProps\", {\n  prefixCls: 'rc-tree',\n  showLine: false,\n  showIcon: true,\n  selectable: true,\n  multiple: false,\n  checkable: false,\n  disabled: false,\n  checkStrictly: false,\n  draggable: false,\n  defaultExpandParent: true,\n  autoExpandParent: false,\n  defaultExpandAll: false,\n  defaultExpandedKeys: [],\n  defaultCheckedKeys: [],\n  defaultSelectedKeys: [],\n  dropIndicatorRender: DropIndicator,\n  allowDrop: function allowDrop() {\n    return true;\n  },\n  expandAction: false\n});\n_defineProperty(Tree, \"TreeNode\", TreeNode);\nexport default Tree;", "map": {"version": 3, "names": ["_extends", "_typeof", "_objectSpread", "_toConsumableArray", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "classNames", "KeyCode", "pickAttrs", "warning", "React", "TreeContext", "DropIndicator", "NodeList", "MOTION_KEY", "MotionEntity", "TreeNode", "arrAdd", "arr<PERSON><PERSON>", "calcDropPosition", "calcSelectedKeys", "conductExpandParent", "getDrag<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseCheckedKeys", "posToArr", "conduct<PERSON>heck", "getEntity", "convertDataToEntities", "convertNodePropsToEventData", "convertTreeToData", "fillFieldNames", "flattenTreeData", "getTreeNodeProps", "warningWithoutKey", "MAX_RETRY_TIMES", "Tree", "_React$Component", "_super", "_this", "_len", "arguments", "length", "_args", "Array", "_key", "call", "apply", "concat", "keyEntities", "indent", "<PERSON><PERSON><PERSON><PERSON>", "checked<PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "loadedKeys", "loadingKeys", "expandedKeys", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dropTargetKey", "dropPosition", "dropContainerKey", "dropLevelOffset", "dropTargetPos", "dropAllowed", "dragOverNodeKey", "treeData", "flattenNodes", "focused", "active<PERSON><PERSON>", "listChanging", "prevProps", "fieldNames", "createRef", "event", "nodeProps", "_this$state", "state", "onDragStart", "props", "eventKey", "dragNodeProps", "dragStartMousePosition", "x", "clientX", "y", "clientY", "newExpandedKeys", "setState", "listRef", "current", "getIndentWidth", "setExpandedKeys", "window", "addEventListener", "onWindowDragEnd", "node", "_this$state2", "_this$props", "onDragEnter", "onExpand", "allowDrop", "direction", "pos", "currentMouseOverDroppableNodeKey", "resetDragState", "_calcDropPosition", "includes", "delayedDragEnterLogic", "Object", "keys", "for<PERSON>ach", "key", "clearTimeout", "persist", "setTimeout", "entity", "children", "hasOwnProperty", "expanded", "nativeEvent", "_this$state3", "_this$props2", "onDragOver", "_calcDropPosition2", "currentTarget", "contains", "relatedTarget", "onDragLeave", "onNodeDragEnd", "removeEventListener", "onDragEnd", "cleanDragState", "_", "_this$getActiveItem", "outsideTree", "undefined", "_this$state4", "onDrop", "abstractDropNodeProps", "getTreeNodeRequiredProps", "active", "getActiveItem", "data", "drop<PERSON>oChild", "posArr", "dropResult", "dragNode", "dragNodesKeys", "dropToGap", "Number", "e", "treeNode", "_this$state5", "<PERSON><PERSON><PERSON><PERSON>", "shift<PERSON>ey", "metaKey", "ctrl<PERSON>ey", "filter", "nodeItem", "eventNode", "onNodeExpand", "_this$props3", "onClick", "expandAction", "triggerExpandActionExpand", "_this$props4", "onDoubleClick", "_this$state6", "_this$props5", "onSelect", "multiple", "selected", "targetSelected", "selectedNodes", "map", "<PERSON><PERSON><PERSON>", "Boolean", "setUncontrolledState", "checked", "_this$state7", "oriCheckedKeys", "oriHalfCheckedKeys", "_this$props6", "checkStrictly", "onCheck", "checked<PERSON>bj", "eventObj", "halfChecked", "checkedNodes", "<PERSON><PERSON><PERSON>", "_conductCheck", "_checked<PERSON><PERSON><PERSON>", "_half<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keySet", "Set", "delete", "_conductCheck2", "from", "checkedNodesPositions", "push", "_entity$children", "loadPromise", "Promise", "resolve", "reject", "_ref", "_ref$loadedKeys", "_ref$loadingKeys", "_this$props7", "loadData", "onLoad", "promise", "then", "currentLoaded<PERSON><PERSON><PERSON>", "new<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prevState", "catch", "loadingRetryTimes", "onMouseEnter", "onMouseLeave", "onRightClick", "preventDefault", "onFocus", "_len2", "args", "_key2", "onBlur", "onActiveChange", "_len3", "_key3", "_this$state8", "_this$state9", "_this$state10", "_this$props8", "certain", "targetExpanded", "onNodeLoad", "newFlattenTreeData", "currentExpandedKeys", "expandedKeysToRestore", "newActiveKey", "_this$props9", "_this$props9$itemScro", "itemScrollOffset", "scrollTo", "offset", "_this$state11", "find", "_ref2", "_this$state12", "index", "findIndex", "_ref3", "item", "_key4", "_this$state13", "_this$props10", "onKeyDown", "checkable", "selectable", "which", "UP", "offsetActiveKey", "DOWN", "activeItem", "treeNodeRequiredProps", "expandable", "LEFT", "parent", "RIGHT", "ENTER", "SPACE", "disabled", "disableCheckbox", "onNodeCheck", "onNodeSelect", "atomic", "forceState", "destroyed", "needSync", "allPassed", "newState", "name", "scroll", "value", "componentDidMount", "onUpdated", "componentDidUpdate", "_this$props11", "_this$props11$itemScr", "componentWillUnmount", "render", "_this$state14", "_this$props12", "prefixCls", "className", "style", "showLine", "focusable", "_this$props12$tabInde", "tabIndex", "showIcon", "icon", "switcherIcon", "draggable", "motion", "filterTreeNode", "height", "itemHeight", "scrollWidth", "virtual", "titleRender", "dropIndicatorRender", "onContextMenu", "onScroll", "rootClassName", "rootStyle", "domProps", "aria", "draggableConfig", "nodeDraggable", "contextValue", "onNodeClick", "onNodeDoubleClick", "onNodeMouseEnter", "onNodeMouseLeave", "onNodeContextMenu", "onNodeDragStart", "onNodeDragEnter", "onNodeDragOver", "onNodeDragLeave", "onNodeDrop", "createElement", "Provider", "ref", "dragging", "onListChangeStart", "onListChangeEnd", "getDerivedStateFromProps", "entitiesMap", "process", "env", "NODE_ENV", "autoExpandParent", "defaultExpandParent", "defaultExpandAll", "cloneKeyEntities", "nextExpandedKeys", "defaultExpandedKeys", "defaultSelectedKeys", "checkedKeyEntity", "defaultCheckedKeys", "_checkedKeyEntity", "_checkedKeyEntity$che", "_checkedKeyEntity$hal", "conductKeys", "Component"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/rc-tree/es/Tree.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// TODO: https://www.w3.org/TR/2017/NOTE-wai-aria-practices-1.1-20171214/examples/treeview/treeview-2/treeview-2a.html\n// Fully accessibility support\n\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { TreeContext } from \"./contextTypes\";\nimport DropIndicator from \"./DropIndicator\";\nimport NodeList, { MOTION_KEY, MotionEntity } from \"./NodeList\";\nimport TreeNode from \"./TreeNode\";\nimport { arrAdd, arrDel, calcDropPosition, calcSelectedKeys, conductExpandParent, getDragChildrenKeys, parseCheckedKeys, posToArr } from \"./util\";\nimport { conductCheck } from \"./utils/conductUtil\";\nimport getEntity from \"./utils/keyUtil\";\nimport { convertDataToEntities, convertNodePropsToEventData, convertTreeToData, fillFieldNames, flattenTreeData, getTreeNodeProps, warningWithoutKey } from \"./utils/treeUtil\";\nvar MAX_RETRY_TIMES = 10;\nvar Tree = /*#__PURE__*/function (_React$Component) {\n  _inherits(Tree, _React$Component);\n  var _super = _createSuper(Tree);\n  function Tree() {\n    var _this;\n    _classCallCheck(this, Tree);\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(_args));\n    _defineProperty(_assertThisInitialized(_this), \"destroyed\", false);\n    _defineProperty(_assertThisInitialized(_this), \"delayedDragEnterLogic\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"loadingRetryTimes\", {});\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      keyEntities: {},\n      indent: null,\n      selectedKeys: [],\n      checkedKeys: [],\n      halfCheckedKeys: [],\n      loadedKeys: [],\n      loadingKeys: [],\n      expandedKeys: [],\n      draggingNodeKey: null,\n      dragChildrenKeys: [],\n      // dropTargetKey is the key of abstract-drop-node\n      // the abstract-drop-node is the real drop node when drag and drop\n      // not the DOM drag over node\n      dropTargetKey: null,\n      dropPosition: null,\n      // the drop position of abstract-drop-node, inside 0, top -1, bottom 1\n      dropContainerKey: null,\n      // the container key of abstract-drop-node if dropPosition is -1 or 1\n      dropLevelOffset: null,\n      // the drop level offset of abstract-drag-over-node\n      dropTargetPos: null,\n      // the pos of abstract-drop-node\n      dropAllowed: true,\n      // if drop to abstract-drop-node is allowed\n      // the abstract-drag-over-node\n      // if mouse is on the bottom of top dom node or no the top of the bottom dom node\n      // abstract-drag-over-node is the top node\n      dragOverNodeKey: null,\n      treeData: [],\n      flattenNodes: [],\n      focused: false,\n      activeKey: null,\n      listChanging: false,\n      prevProps: null,\n      fieldNames: fillFieldNames()\n    });\n    _defineProperty(_assertThisInitialized(_this), \"dragStartMousePosition\", null);\n    _defineProperty(_assertThisInitialized(_this), \"dragNodeProps\", null);\n    _defineProperty(_assertThisInitialized(_this), \"currentMouseOverDroppableNodeKey\", null);\n    _defineProperty(_assertThisInitialized(_this), \"listRef\", /*#__PURE__*/React.createRef());\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragStart\", function (event, nodeProps) {\n      var _this$state = _this.state,\n        expandedKeys = _this$state.expandedKeys,\n        keyEntities = _this$state.keyEntities;\n      var onDragStart = _this.props.onDragStart;\n      var eventKey = nodeProps.eventKey;\n      _this.dragNodeProps = nodeProps;\n      _this.dragStartMousePosition = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      var newExpandedKeys = arrDel(expandedKeys, eventKey);\n      _this.setState({\n        draggingNodeKey: eventKey,\n        dragChildrenKeys: getDragChildrenKeys(eventKey, keyEntities),\n        indent: _this.listRef.current.getIndentWidth()\n      });\n      _this.setExpandedKeys(newExpandedKeys);\n      window.addEventListener('dragend', _this.onWindowDragEnd);\n      onDragStart === null || onDragStart === void 0 || onDragStart({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps)\n      });\n    });\n    /**\n     * [Legacy] Select handler is smaller than node,\n     * so that this will trigger when drag enter node or select handler.\n     * This is a little tricky if customize css without padding.\n     * Better for use mouse move event to refresh drag state.\n     * But let's just keep it to avoid event trigger logic change.\n     */\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragEnter\", function (event, nodeProps) {\n      var _this$state2 = _this.state,\n        expandedKeys = _this$state2.expandedKeys,\n        keyEntities = _this$state2.keyEntities,\n        dragChildrenKeys = _this$state2.dragChildrenKeys,\n        flattenNodes = _this$state2.flattenNodes,\n        indent = _this$state2.indent;\n      var _this$props = _this.props,\n        onDragEnter = _this$props.onDragEnter,\n        onExpand = _this$props.onExpand,\n        allowDrop = _this$props.allowDrop,\n        direction = _this$props.direction;\n      var pos = nodeProps.pos,\n        eventKey = nodeProps.eventKey;\n\n      // record the key of node which is latest entered, used in dragleave event.\n      if (_this.currentMouseOverDroppableNodeKey !== eventKey) {\n        _this.currentMouseOverDroppableNodeKey = eventKey;\n      }\n      if (!_this.dragNodeProps) {\n        _this.resetDragState();\n        return;\n      }\n      var _calcDropPosition = calcDropPosition(event, _this.dragNodeProps, nodeProps, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition.dropPosition,\n        dropLevelOffset = _calcDropPosition.dropLevelOffset,\n        dropTargetKey = _calcDropPosition.dropTargetKey,\n        dropContainerKey = _calcDropPosition.dropContainerKey,\n        dropTargetPos = _calcDropPosition.dropTargetPos,\n        dropAllowed = _calcDropPosition.dropAllowed,\n        dragOverNodeKey = _calcDropPosition.dragOverNodeKey;\n      if (\n      // don't allow drop inside its children\n      dragChildrenKeys.includes(dropTargetKey) ||\n      // don't allow drop when drop is not allowed caculated by calcDropPosition\n      !dropAllowed) {\n        _this.resetDragState();\n        return;\n      }\n\n      // Side effect for delay drag\n      if (!_this.delayedDragEnterLogic) {\n        _this.delayedDragEnterLogic = {};\n      }\n      Object.keys(_this.delayedDragEnterLogic).forEach(function (key) {\n        clearTimeout(_this.delayedDragEnterLogic[key]);\n      });\n      if (_this.dragNodeProps.eventKey !== nodeProps.eventKey) {\n        // hoist expand logic here\n        // since if logic is on the bottom\n        // it will be blocked by abstract dragover node check\n        //   => if you dragenter from top, you mouse will still be consider as in the top node\n        event.persist();\n        _this.delayedDragEnterLogic[pos] = window.setTimeout(function () {\n          if (_this.state.draggingNodeKey === null) {\n            return;\n          }\n          var newExpandedKeys = _toConsumableArray(expandedKeys);\n          var entity = getEntity(keyEntities, nodeProps.eventKey);\n          if (entity && (entity.children || []).length) {\n            newExpandedKeys = arrAdd(expandedKeys, nodeProps.eventKey);\n          }\n          if (!_this.props.hasOwnProperty('expandedKeys')) {\n            _this.setExpandedKeys(newExpandedKeys);\n          }\n          onExpand === null || onExpand === void 0 || onExpand(newExpandedKeys, {\n            node: convertNodePropsToEventData(nodeProps),\n            expanded: true,\n            nativeEvent: event.nativeEvent\n          });\n        }, 800);\n      }\n\n      // Skip if drag node is self\n      if (_this.dragNodeProps.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        _this.resetDragState();\n        return;\n      }\n\n      // Update drag over node and drag state\n      _this.setState({\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        dropLevelOffset: dropLevelOffset,\n        dropTargetKey: dropTargetKey,\n        dropContainerKey: dropContainerKey,\n        dropTargetPos: dropTargetPos,\n        dropAllowed: dropAllowed\n      });\n      onDragEnter === null || onDragEnter === void 0 || onDragEnter({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps),\n        expandedKeys: expandedKeys\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragOver\", function (event, nodeProps) {\n      var _this$state3 = _this.state,\n        dragChildrenKeys = _this$state3.dragChildrenKeys,\n        flattenNodes = _this$state3.flattenNodes,\n        keyEntities = _this$state3.keyEntities,\n        expandedKeys = _this$state3.expandedKeys,\n        indent = _this$state3.indent;\n      var _this$props2 = _this.props,\n        onDragOver = _this$props2.onDragOver,\n        allowDrop = _this$props2.allowDrop,\n        direction = _this$props2.direction;\n      if (!_this.dragNodeProps) {\n        return;\n      }\n      var _calcDropPosition2 = calcDropPosition(event, _this.dragNodeProps, nodeProps, indent, _this.dragStartMousePosition, allowDrop, flattenNodes, keyEntities, expandedKeys, direction),\n        dropPosition = _calcDropPosition2.dropPosition,\n        dropLevelOffset = _calcDropPosition2.dropLevelOffset,\n        dropTargetKey = _calcDropPosition2.dropTargetKey,\n        dropContainerKey = _calcDropPosition2.dropContainerKey,\n        dropTargetPos = _calcDropPosition2.dropTargetPos,\n        dropAllowed = _calcDropPosition2.dropAllowed,\n        dragOverNodeKey = _calcDropPosition2.dragOverNodeKey;\n      if (dragChildrenKeys.includes(dropTargetKey) || !dropAllowed) {\n        // don't allow drop inside its children\n        // don't allow drop when drop is not allowed calculated by calcDropPosition\n        return;\n      }\n\n      // Update drag position\n\n      if (_this.dragNodeProps.eventKey === dropTargetKey && dropLevelOffset === 0) {\n        if (!(_this.state.dropPosition === null && _this.state.dropLevelOffset === null && _this.state.dropTargetKey === null && _this.state.dropContainerKey === null && _this.state.dropTargetPos === null && _this.state.dropAllowed === false && _this.state.dragOverNodeKey === null)) {\n          _this.resetDragState();\n        }\n      } else if (!(dropPosition === _this.state.dropPosition && dropLevelOffset === _this.state.dropLevelOffset && dropTargetKey === _this.state.dropTargetKey && dropContainerKey === _this.state.dropContainerKey && dropTargetPos === _this.state.dropTargetPos && dropAllowed === _this.state.dropAllowed && dragOverNodeKey === _this.state.dragOverNodeKey)) {\n        _this.setState({\n          dropPosition: dropPosition,\n          dropLevelOffset: dropLevelOffset,\n          dropTargetKey: dropTargetKey,\n          dropContainerKey: dropContainerKey,\n          dropTargetPos: dropTargetPos,\n          dropAllowed: dropAllowed,\n          dragOverNodeKey: dragOverNodeKey\n        });\n      }\n      onDragOver === null || onDragOver === void 0 || onDragOver({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps)\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragLeave\", function (event, nodeProps) {\n      // if it is outside the droppable area\n      // currentMouseOverDroppableNodeKey will be updated in dragenter event when into another droppable receiver.\n      if (_this.currentMouseOverDroppableNodeKey === nodeProps.eventKey && !event.currentTarget.contains(event.relatedTarget)) {\n        _this.resetDragState();\n        _this.currentMouseOverDroppableNodeKey = null;\n      }\n      var onDragLeave = _this.props.onDragLeave;\n      onDragLeave === null || onDragLeave === void 0 || onDragLeave({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps)\n      });\n    });\n    // since stopPropagation() is called in treeNode\n    // if onWindowDrag is called, whice means state is keeped, drag state should be cleared\n    _defineProperty(_assertThisInitialized(_this), \"onWindowDragEnd\", function (event) {\n      _this.onNodeDragEnd(event, null, true);\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    });\n    // if onNodeDragEnd is called, onWindowDragEnd won't be called since stopPropagation() is called\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDragEnd\", function (event, nodeProps) {\n      var onDragEnd = _this.props.onDragEnd;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      onDragEnd === null || onDragEnd === void 0 || onDragEnd({\n        event: event,\n        node: convertNodePropsToEventData(nodeProps)\n      });\n      _this.dragNodeProps = null;\n      window.removeEventListener('dragend', _this.onWindowDragEnd);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDrop\", function (event, _) {\n      var _this$getActiveItem;\n      var outsideTree = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var _this$state4 = _this.state,\n        dragChildrenKeys = _this$state4.dragChildrenKeys,\n        dropPosition = _this$state4.dropPosition,\n        dropTargetKey = _this$state4.dropTargetKey,\n        dropTargetPos = _this$state4.dropTargetPos,\n        dropAllowed = _this$state4.dropAllowed;\n      if (!dropAllowed) {\n        return;\n      }\n      var onDrop = _this.props.onDrop;\n      _this.setState({\n        dragOverNodeKey: null\n      });\n      _this.cleanDragState();\n      if (dropTargetKey === null) return;\n      var abstractDropNodeProps = _objectSpread(_objectSpread({}, getTreeNodeProps(dropTargetKey, _this.getTreeNodeRequiredProps())), {}, {\n        active: ((_this$getActiveItem = _this.getActiveItem()) === null || _this$getActiveItem === void 0 ? void 0 : _this$getActiveItem.key) === dropTargetKey,\n        data: getEntity(_this.state.keyEntities, dropTargetKey).node\n      });\n      var dropToChild = dragChildrenKeys.includes(dropTargetKey);\n      warning(!dropToChild, \"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.\");\n      var posArr = posToArr(dropTargetPos);\n      var dropResult = {\n        event: event,\n        node: convertNodePropsToEventData(abstractDropNodeProps),\n        dragNode: _this.dragNodeProps ? convertNodePropsToEventData(_this.dragNodeProps) : null,\n        dragNodesKeys: [_this.dragNodeProps.eventKey].concat(dragChildrenKeys),\n        dropToGap: dropPosition !== 0,\n        dropPosition: dropPosition + Number(posArr[posArr.length - 1])\n      };\n      if (!outsideTree) {\n        onDrop === null || onDrop === void 0 || onDrop(dropResult);\n      }\n      _this.dragNodeProps = null;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"cleanDragState\", function () {\n      var draggingNodeKey = _this.state.draggingNodeKey;\n      if (draggingNodeKey !== null) {\n        _this.setState({\n          draggingNodeKey: null,\n          dropPosition: null,\n          dropContainerKey: null,\n          dropTargetKey: null,\n          dropLevelOffset: null,\n          dropAllowed: true,\n          dragOverNodeKey: null\n        });\n      }\n      _this.dragStartMousePosition = null;\n      _this.currentMouseOverDroppableNodeKey = null;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"triggerExpandActionExpand\", function (e, treeNode) {\n      var _this$state5 = _this.state,\n        expandedKeys = _this$state5.expandedKeys,\n        flattenNodes = _this$state5.flattenNodes;\n      var expanded = treeNode.expanded,\n        key = treeNode.key,\n        isLeaf = treeNode.isLeaf;\n      if (isLeaf || e.shiftKey || e.metaKey || e.ctrlKey) {\n        return;\n      }\n      var node = flattenNodes.filter(function (nodeItem) {\n        return nodeItem.key === key;\n      })[0];\n      var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(key, _this.getTreeNodeRequiredProps())), {}, {\n        data: node.data\n      }));\n      _this.setExpandedKeys(expanded ? arrDel(expandedKeys, key) : arrAdd(expandedKeys, key));\n      _this.onNodeExpand(e, eventNode);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeClick\", function (e, treeNode) {\n      var _this$props3 = _this.props,\n        onClick = _this$props3.onClick,\n        expandAction = _this$props3.expandAction;\n      if (expandAction === 'click') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onClick === null || onClick === void 0 || onClick(e, treeNode);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeDoubleClick\", function (e, treeNode) {\n      var _this$props4 = _this.props,\n        onDoubleClick = _this$props4.onDoubleClick,\n        expandAction = _this$props4.expandAction;\n      if (expandAction === 'doubleClick') {\n        _this.triggerExpandActionExpand(e, treeNode);\n      }\n      onDoubleClick === null || onDoubleClick === void 0 || onDoubleClick(e, treeNode);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeSelect\", function (e, treeNode) {\n      var selectedKeys = _this.state.selectedKeys;\n      var _this$state6 = _this.state,\n        keyEntities = _this$state6.keyEntities,\n        fieldNames = _this$state6.fieldNames;\n      var _this$props5 = _this.props,\n        onSelect = _this$props5.onSelect,\n        multiple = _this$props5.multiple;\n      var selected = treeNode.selected;\n      var key = treeNode[fieldNames.key];\n      var targetSelected = !selected;\n\n      // Update selected keys\n      if (!targetSelected) {\n        selectedKeys = arrDel(selectedKeys, key);\n      } else if (!multiple) {\n        selectedKeys = [key];\n      } else {\n        selectedKeys = arrAdd(selectedKeys, key);\n      }\n\n      // [Legacy] Not found related usage in doc or upper libs\n      var selectedNodes = selectedKeys.map(function (selectedKey) {\n        var entity = getEntity(keyEntities, selectedKey);\n        return entity ? entity.node : null;\n      }).filter(Boolean);\n      _this.setUncontrolledState({\n        selectedKeys: selectedKeys\n      });\n      onSelect === null || onSelect === void 0 || onSelect(selectedKeys, {\n        event: 'select',\n        selected: targetSelected,\n        node: treeNode,\n        selectedNodes: selectedNodes,\n        nativeEvent: e.nativeEvent\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeCheck\", function (e, treeNode, checked) {\n      var _this$state7 = _this.state,\n        keyEntities = _this$state7.keyEntities,\n        oriCheckedKeys = _this$state7.checkedKeys,\n        oriHalfCheckedKeys = _this$state7.halfCheckedKeys;\n      var _this$props6 = _this.props,\n        checkStrictly = _this$props6.checkStrictly,\n        onCheck = _this$props6.onCheck;\n      var key = treeNode.key;\n\n      // Prepare trigger arguments\n      var checkedObj;\n      var eventObj = {\n        event: 'check',\n        node: treeNode,\n        checked: checked,\n        nativeEvent: e.nativeEvent\n      };\n      if (checkStrictly) {\n        var checkedKeys = checked ? arrAdd(oriCheckedKeys, key) : arrDel(oriCheckedKeys, key);\n        var halfCheckedKeys = arrDel(oriHalfCheckedKeys, key);\n        checkedObj = {\n          checked: checkedKeys,\n          halfChecked: halfCheckedKeys\n        };\n        eventObj.checkedNodes = checkedKeys.map(function (checkedKey) {\n          return getEntity(keyEntities, checkedKey);\n        }).filter(Boolean).map(function (entity) {\n          return entity.node;\n        });\n        _this.setUncontrolledState({\n          checkedKeys: checkedKeys\n        });\n      } else {\n        // Always fill first\n        var _conductCheck = conductCheck([].concat(_toConsumableArray(oriCheckedKeys), [key]), true, keyEntities),\n          _checkedKeys = _conductCheck.checkedKeys,\n          _halfCheckedKeys = _conductCheck.halfCheckedKeys;\n\n        // If remove, we do it again to correction\n        if (!checked) {\n          var keySet = new Set(_checkedKeys);\n          keySet.delete(key);\n          var _conductCheck2 = conductCheck(Array.from(keySet), {\n            checked: false,\n            halfCheckedKeys: _halfCheckedKeys\n          }, keyEntities);\n          _checkedKeys = _conductCheck2.checkedKeys;\n          _halfCheckedKeys = _conductCheck2.halfCheckedKeys;\n        }\n        checkedObj = _checkedKeys;\n\n        // [Legacy] This is used for `rc-tree-select`\n        eventObj.checkedNodes = [];\n        eventObj.checkedNodesPositions = [];\n        eventObj.halfCheckedKeys = _halfCheckedKeys;\n        _checkedKeys.forEach(function (checkedKey) {\n          var entity = getEntity(keyEntities, checkedKey);\n          if (!entity) return;\n          var node = entity.node,\n            pos = entity.pos;\n          eventObj.checkedNodes.push(node);\n          eventObj.checkedNodesPositions.push({\n            node: node,\n            pos: pos\n          });\n        });\n        _this.setUncontrolledState({\n          checkedKeys: _checkedKeys\n        }, false, {\n          halfCheckedKeys: _halfCheckedKeys\n        });\n      }\n      onCheck === null || onCheck === void 0 || onCheck(checkedObj, eventObj);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeLoad\", function (treeNode) {\n      var _entity$children;\n      var key = treeNode.key;\n      var keyEntities = _this.state.keyEntities;\n\n      // Skip if has children already\n      var entity = getEntity(keyEntities, key);\n      if (entity !== null && entity !== void 0 && (_entity$children = entity.children) !== null && _entity$children !== void 0 && _entity$children.length) {\n        return;\n      }\n      var loadPromise = new Promise(function (resolve, reject) {\n        // We need to get the latest state of loading/loaded keys\n        _this.setState(function (_ref) {\n          var _ref$loadedKeys = _ref.loadedKeys,\n            loadedKeys = _ref$loadedKeys === void 0 ? [] : _ref$loadedKeys,\n            _ref$loadingKeys = _ref.loadingKeys,\n            loadingKeys = _ref$loadingKeys === void 0 ? [] : _ref$loadingKeys;\n          var _this$props7 = _this.props,\n            loadData = _this$props7.loadData,\n            onLoad = _this$props7.onLoad;\n          if (!loadData || loadedKeys.includes(key) || loadingKeys.includes(key)) {\n            return null;\n          }\n\n          // Process load data\n          var promise = loadData(treeNode);\n          promise.then(function () {\n            var currentLoadedKeys = _this.state.loadedKeys;\n            var newLoadedKeys = arrAdd(currentLoadedKeys, key);\n\n            // onLoad should trigger before internal setState to avoid `loadData` trigger twice.\n            // https://github.com/ant-design/ant-design/issues/12464\n            onLoad === null || onLoad === void 0 || onLoad(newLoadedKeys, {\n              event: 'load',\n              node: treeNode\n            });\n            _this.setUncontrolledState({\n              loadedKeys: newLoadedKeys\n            });\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n            resolve();\n          }).catch(function (e) {\n            _this.setState(function (prevState) {\n              return {\n                loadingKeys: arrDel(prevState.loadingKeys, key)\n              };\n            });\n\n            // If exceed max retry times, we give up retry\n            _this.loadingRetryTimes[key] = (_this.loadingRetryTimes[key] || 0) + 1;\n            if (_this.loadingRetryTimes[key] >= MAX_RETRY_TIMES) {\n              var currentLoadedKeys = _this.state.loadedKeys;\n              warning(false, 'Retry for `loadData` many times but still failed. No more retry.');\n              _this.setUncontrolledState({\n                loadedKeys: arrAdd(currentLoadedKeys, key)\n              });\n              resolve();\n            }\n            reject(e);\n          });\n          return {\n            loadingKeys: arrAdd(loadingKeys, key)\n          };\n        });\n      });\n\n      // Not care warning if we ignore this\n      loadPromise.catch(function () {});\n      return loadPromise;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeMouseEnter\", function (event, node) {\n      var onMouseEnter = _this.props.onMouseEnter;\n      onMouseEnter === null || onMouseEnter === void 0 || onMouseEnter({\n        event: event,\n        node: node\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeMouseLeave\", function (event, node) {\n      var onMouseLeave = _this.props.onMouseLeave;\n      onMouseLeave === null || onMouseLeave === void 0 || onMouseLeave({\n        event: event,\n        node: node\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeContextMenu\", function (event, node) {\n      var onRightClick = _this.props.onRightClick;\n      if (onRightClick) {\n        event.preventDefault();\n        onRightClick({\n          event: event,\n          node: node\n        });\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onFocus\", function () {\n      var onFocus = _this.props.onFocus;\n      _this.setState({\n        focused: true\n      });\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      onFocus === null || onFocus === void 0 || onFocus.apply(void 0, args);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onBlur\", function () {\n      var onBlur = _this.props.onBlur;\n      _this.setState({\n        focused: false\n      });\n      _this.onActiveChange(null);\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      onBlur === null || onBlur === void 0 || onBlur.apply(void 0, args);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getTreeNodeRequiredProps\", function () {\n      var _this$state8 = _this.state,\n        expandedKeys = _this$state8.expandedKeys,\n        selectedKeys = _this$state8.selectedKeys,\n        loadedKeys = _this$state8.loadedKeys,\n        loadingKeys = _this$state8.loadingKeys,\n        checkedKeys = _this$state8.checkedKeys,\n        halfCheckedKeys = _this$state8.halfCheckedKeys,\n        dragOverNodeKey = _this$state8.dragOverNodeKey,\n        dropPosition = _this$state8.dropPosition,\n        keyEntities = _this$state8.keyEntities;\n      return {\n        expandedKeys: expandedKeys || [],\n        selectedKeys: selectedKeys || [],\n        loadedKeys: loadedKeys || [],\n        loadingKeys: loadingKeys || [],\n        checkedKeys: checkedKeys || [],\n        halfCheckedKeys: halfCheckedKeys || [],\n        dragOverNodeKey: dragOverNodeKey,\n        dropPosition: dropPosition,\n        keyEntities: keyEntities\n      };\n    });\n    // =========================== Expanded ===========================\n    /** Set uncontrolled `expandedKeys`. This will also auto update `flattenNodes`. */\n    _defineProperty(_assertThisInitialized(_this), \"setExpandedKeys\", function (expandedKeys) {\n      var _this$state9 = _this.state,\n        treeData = _this$state9.treeData,\n        fieldNames = _this$state9.fieldNames;\n      var flattenNodes = flattenTreeData(treeData, expandedKeys, fieldNames);\n      _this.setUncontrolledState({\n        expandedKeys: expandedKeys,\n        flattenNodes: flattenNodes\n      }, true);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onNodeExpand\", function (e, treeNode) {\n      var expandedKeys = _this.state.expandedKeys;\n      var _this$state10 = _this.state,\n        listChanging = _this$state10.listChanging,\n        fieldNames = _this$state10.fieldNames;\n      var _this$props8 = _this.props,\n        onExpand = _this$props8.onExpand,\n        loadData = _this$props8.loadData;\n      var expanded = treeNode.expanded;\n      var key = treeNode[fieldNames.key];\n\n      // Do nothing when motion is in progress\n      if (listChanging) {\n        return;\n      }\n\n      // Update selected keys\n      var certain = expandedKeys.includes(key);\n      var targetExpanded = !expanded;\n      warning(expanded && certain || !expanded && !certain, 'Expand state not sync with index check');\n      expandedKeys = targetExpanded ? arrAdd(expandedKeys, key) : arrDel(expandedKeys, key);\n      _this.setExpandedKeys(expandedKeys);\n      onExpand === null || onExpand === void 0 || onExpand(expandedKeys, {\n        node: treeNode,\n        expanded: targetExpanded,\n        nativeEvent: e.nativeEvent\n      });\n\n      // Async Load data\n      if (targetExpanded && loadData) {\n        var loadPromise = _this.onNodeLoad(treeNode);\n        if (loadPromise) {\n          loadPromise.then(function () {\n            // [Legacy] Refresh logic\n            var newFlattenTreeData = flattenTreeData(_this.state.treeData, expandedKeys, fieldNames);\n            _this.setUncontrolledState({\n              flattenNodes: newFlattenTreeData\n            });\n          }).catch(function () {\n            var currentExpandedKeys = _this.state.expandedKeys;\n            var expandedKeysToRestore = arrDel(currentExpandedKeys, key);\n            _this.setExpandedKeys(expandedKeysToRestore);\n          });\n        }\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onListChangeStart\", function () {\n      _this.setUncontrolledState({\n        listChanging: true\n      });\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onListChangeEnd\", function () {\n      setTimeout(function () {\n        _this.setUncontrolledState({\n          listChanging: false\n        });\n      });\n    });\n    // =========================== Keyboard ===========================\n    _defineProperty(_assertThisInitialized(_this), \"onActiveChange\", function (newActiveKey) {\n      var activeKey = _this.state.activeKey;\n      var _this$props9 = _this.props,\n        onActiveChange = _this$props9.onActiveChange,\n        _this$props9$itemScro = _this$props9.itemScrollOffset,\n        itemScrollOffset = _this$props9$itemScro === void 0 ? 0 : _this$props9$itemScro;\n      if (activeKey === newActiveKey) {\n        return;\n      }\n      _this.setState({\n        activeKey: newActiveKey\n      });\n      if (newActiveKey !== null) {\n        _this.scrollTo({\n          key: newActiveKey,\n          offset: itemScrollOffset\n        });\n      }\n      onActiveChange === null || onActiveChange === void 0 || onActiveChange(newActiveKey);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"getActiveItem\", function () {\n      var _this$state11 = _this.state,\n        activeKey = _this$state11.activeKey,\n        flattenNodes = _this$state11.flattenNodes;\n      if (activeKey === null) {\n        return null;\n      }\n      return flattenNodes.find(function (_ref2) {\n        var key = _ref2.key;\n        return key === activeKey;\n      }) || null;\n    });\n    _defineProperty(_assertThisInitialized(_this), \"offsetActiveKey\", function (offset) {\n      var _this$state12 = _this.state,\n        flattenNodes = _this$state12.flattenNodes,\n        activeKey = _this$state12.activeKey;\n      var index = flattenNodes.findIndex(function (_ref3) {\n        var key = _ref3.key;\n        return key === activeKey;\n      });\n\n      // Align with index\n      if (index === -1 && offset < 0) {\n        index = flattenNodes.length;\n      }\n      index = (index + offset + flattenNodes.length) % flattenNodes.length;\n      var item = flattenNodes[index];\n      if (item) {\n        var _key4 = item.key;\n        _this.onActiveChange(_key4);\n      } else {\n        _this.onActiveChange(null);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onKeyDown\", function (event) {\n      var _this$state13 = _this.state,\n        activeKey = _this$state13.activeKey,\n        expandedKeys = _this$state13.expandedKeys,\n        checkedKeys = _this$state13.checkedKeys,\n        fieldNames = _this$state13.fieldNames;\n      var _this$props10 = _this.props,\n        onKeyDown = _this$props10.onKeyDown,\n        checkable = _this$props10.checkable,\n        selectable = _this$props10.selectable;\n\n      // >>>>>>>>>> Direction\n      switch (event.which) {\n        case KeyCode.UP:\n          {\n            _this.offsetActiveKey(-1);\n            event.preventDefault();\n            break;\n          }\n        case KeyCode.DOWN:\n          {\n            _this.offsetActiveKey(1);\n            event.preventDefault();\n            break;\n          }\n      }\n\n      // >>>>>>>>>> Expand & Selection\n      var activeItem = _this.getActiveItem();\n      if (activeItem && activeItem.data) {\n        var treeNodeRequiredProps = _this.getTreeNodeRequiredProps();\n        var expandable = activeItem.data.isLeaf === false || !!(activeItem.data[fieldNames.children] || []).length;\n        var eventNode = convertNodePropsToEventData(_objectSpread(_objectSpread({}, getTreeNodeProps(activeKey, treeNodeRequiredProps)), {}, {\n          data: activeItem.data,\n          active: true\n        }));\n        switch (event.which) {\n          // >>> Expand\n          case KeyCode.LEFT:\n            {\n              // Collapse if possible\n              if (expandable && expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.parent) {\n                _this.onActiveChange(activeItem.parent.key);\n              }\n              event.preventDefault();\n              break;\n            }\n          case KeyCode.RIGHT:\n            {\n              // Expand if possible\n              if (expandable && !expandedKeys.includes(activeKey)) {\n                _this.onNodeExpand({}, eventNode);\n              } else if (activeItem.children && activeItem.children.length) {\n                _this.onActiveChange(activeItem.children[0].key);\n              }\n              event.preventDefault();\n              break;\n            }\n\n          // Selection\n          case KeyCode.ENTER:\n          case KeyCode.SPACE:\n            {\n              if (checkable && !eventNode.disabled && eventNode.checkable !== false && !eventNode.disableCheckbox) {\n                _this.onNodeCheck({}, eventNode, !checkedKeys.includes(activeKey));\n              } else if (!checkable && selectable && !eventNode.disabled && eventNode.selectable !== false) {\n                _this.onNodeSelect({}, eventNode);\n              }\n              break;\n            }\n        }\n      }\n      onKeyDown === null || onKeyDown === void 0 || onKeyDown(event);\n    });\n    /**\n     * Only update the value which is not in props\n     */\n    _defineProperty(_assertThisInitialized(_this), \"setUncontrolledState\", function (state) {\n      var atomic = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var forceState = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      if (!_this.destroyed) {\n        var needSync = false;\n        var allPassed = true;\n        var newState = {};\n        Object.keys(state).forEach(function (name) {\n          if (_this.props.hasOwnProperty(name)) {\n            allPassed = false;\n            return;\n          }\n          needSync = true;\n          newState[name] = state[name];\n        });\n        if (needSync && (!atomic || allPassed)) {\n          _this.setState(_objectSpread(_objectSpread({}, newState), forceState));\n        }\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"scrollTo\", function (scroll) {\n      _this.listRef.current.scrollTo(scroll);\n    });\n    return _this;\n  }\n  _createClass(Tree, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this.destroyed = false;\n      this.onUpdated();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate() {\n      this.onUpdated();\n    }\n  }, {\n    key: \"onUpdated\",\n    value: function onUpdated() {\n      var _this$props11 = this.props,\n        activeKey = _this$props11.activeKey,\n        _this$props11$itemScr = _this$props11.itemScrollOffset,\n        itemScrollOffset = _this$props11$itemScr === void 0 ? 0 : _this$props11$itemScr;\n      if (activeKey !== undefined && activeKey !== this.state.activeKey) {\n        this.setState({\n          activeKey: activeKey\n        });\n        if (activeKey !== null) {\n          this.scrollTo({\n            key: activeKey,\n            offset: itemScrollOffset\n          });\n        }\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      window.removeEventListener('dragend', this.onWindowDragEnd);\n      this.destroyed = true;\n    }\n  }, {\n    key: \"resetDragState\",\n    value: function resetDragState() {\n      this.setState({\n        dragOverNodeKey: null,\n        dropPosition: null,\n        dropLevelOffset: null,\n        dropTargetKey: null,\n        dropContainerKey: null,\n        dropTargetPos: null,\n        dropAllowed: false\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$state14 = this.state,\n        focused = _this$state14.focused,\n        flattenNodes = _this$state14.flattenNodes,\n        keyEntities = _this$state14.keyEntities,\n        draggingNodeKey = _this$state14.draggingNodeKey,\n        activeKey = _this$state14.activeKey,\n        dropLevelOffset = _this$state14.dropLevelOffset,\n        dropContainerKey = _this$state14.dropContainerKey,\n        dropTargetKey = _this$state14.dropTargetKey,\n        dropPosition = _this$state14.dropPosition,\n        dragOverNodeKey = _this$state14.dragOverNodeKey,\n        indent = _this$state14.indent;\n      var _this$props12 = this.props,\n        prefixCls = _this$props12.prefixCls,\n        className = _this$props12.className,\n        style = _this$props12.style,\n        showLine = _this$props12.showLine,\n        focusable = _this$props12.focusable,\n        _this$props12$tabInde = _this$props12.tabIndex,\n        tabIndex = _this$props12$tabInde === void 0 ? 0 : _this$props12$tabInde,\n        selectable = _this$props12.selectable,\n        showIcon = _this$props12.showIcon,\n        icon = _this$props12.icon,\n        switcherIcon = _this$props12.switcherIcon,\n        draggable = _this$props12.draggable,\n        checkable = _this$props12.checkable,\n        checkStrictly = _this$props12.checkStrictly,\n        disabled = _this$props12.disabled,\n        motion = _this$props12.motion,\n        loadData = _this$props12.loadData,\n        filterTreeNode = _this$props12.filterTreeNode,\n        height = _this$props12.height,\n        itemHeight = _this$props12.itemHeight,\n        scrollWidth = _this$props12.scrollWidth,\n        virtual = _this$props12.virtual,\n        titleRender = _this$props12.titleRender,\n        dropIndicatorRender = _this$props12.dropIndicatorRender,\n        onContextMenu = _this$props12.onContextMenu,\n        onScroll = _this$props12.onScroll,\n        direction = _this$props12.direction,\n        rootClassName = _this$props12.rootClassName,\n        rootStyle = _this$props12.rootStyle;\n      var domProps = pickAttrs(this.props, {\n        aria: true,\n        data: true\n      });\n\n      // It's better move to hooks but we just simply keep here\n      var draggableConfig;\n      if (draggable) {\n        if (_typeof(draggable) === 'object') {\n          draggableConfig = draggable;\n        } else if (typeof draggable === 'function') {\n          draggableConfig = {\n            nodeDraggable: draggable\n          };\n        } else {\n          draggableConfig = {};\n        }\n      }\n      var contextValue = {\n        prefixCls: prefixCls,\n        selectable: selectable,\n        showIcon: showIcon,\n        icon: icon,\n        switcherIcon: switcherIcon,\n        draggable: draggableConfig,\n        draggingNodeKey: draggingNodeKey,\n        checkable: checkable,\n        checkStrictly: checkStrictly,\n        disabled: disabled,\n        keyEntities: keyEntities,\n        dropLevelOffset: dropLevelOffset,\n        dropContainerKey: dropContainerKey,\n        dropTargetKey: dropTargetKey,\n        dropPosition: dropPosition,\n        dragOverNodeKey: dragOverNodeKey,\n        indent: indent,\n        direction: direction,\n        dropIndicatorRender: dropIndicatorRender,\n        loadData: loadData,\n        filterTreeNode: filterTreeNode,\n        titleRender: titleRender,\n        onNodeClick: this.onNodeClick,\n        onNodeDoubleClick: this.onNodeDoubleClick,\n        onNodeExpand: this.onNodeExpand,\n        onNodeSelect: this.onNodeSelect,\n        onNodeCheck: this.onNodeCheck,\n        onNodeLoad: this.onNodeLoad,\n        onNodeMouseEnter: this.onNodeMouseEnter,\n        onNodeMouseLeave: this.onNodeMouseLeave,\n        onNodeContextMenu: this.onNodeContextMenu,\n        onNodeDragStart: this.onNodeDragStart,\n        onNodeDragEnter: this.onNodeDragEnter,\n        onNodeDragOver: this.onNodeDragOver,\n        onNodeDragLeave: this.onNodeDragLeave,\n        onNodeDragEnd: this.onNodeDragEnd,\n        onNodeDrop: this.onNodeDrop\n      };\n      return /*#__PURE__*/React.createElement(TreeContext.Provider, {\n        value: contextValue\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(prefixCls, className, rootClassName, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-show-line\"), showLine), \"\".concat(prefixCls, \"-focused\"), focused), \"\".concat(prefixCls, \"-active-focused\"), activeKey !== null)),\n        style: rootStyle\n      }, /*#__PURE__*/React.createElement(NodeList, _extends({\n        ref: this.listRef,\n        prefixCls: prefixCls,\n        style: style,\n        data: flattenNodes,\n        disabled: disabled,\n        selectable: selectable,\n        checkable: !!checkable,\n        motion: motion,\n        dragging: draggingNodeKey !== null,\n        height: height,\n        itemHeight: itemHeight,\n        virtual: virtual,\n        focusable: focusable,\n        focused: focused,\n        tabIndex: tabIndex,\n        activeItem: this.getActiveItem(),\n        onFocus: this.onFocus,\n        onBlur: this.onBlur,\n        onKeyDown: this.onKeyDown,\n        onActiveChange: this.onActiveChange,\n        onListChangeStart: this.onListChangeStart,\n        onListChangeEnd: this.onListChangeEnd,\n        onContextMenu: onContextMenu,\n        onScroll: onScroll,\n        scrollWidth: scrollWidth\n      }, this.getTreeNodeRequiredProps(), domProps))));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var prevProps = prevState.prevProps;\n      var newState = {\n        prevProps: props\n      };\n      function needSync(name) {\n        return !prevProps && props.hasOwnProperty(name) || prevProps && prevProps[name] !== props[name];\n      }\n\n      // ================== Tree Node ==================\n      var treeData;\n\n      // fieldNames\n      var fieldNames = prevState.fieldNames;\n      if (needSync('fieldNames')) {\n        fieldNames = fillFieldNames(props.fieldNames);\n        newState.fieldNames = fieldNames;\n      }\n\n      // Check if `treeData` or `children` changed and save into the state.\n      if (needSync('treeData')) {\n        treeData = props.treeData;\n      } else if (needSync('children')) {\n        warning(false, '`children` of Tree is deprecated. Please use `treeData` instead.');\n        treeData = convertTreeToData(props.children);\n      }\n\n      // Save flatten nodes info and convert `treeData` into keyEntities\n      if (treeData) {\n        newState.treeData = treeData;\n        var entitiesMap = convertDataToEntities(treeData, {\n          fieldNames: fieldNames\n        });\n        newState.keyEntities = _objectSpread(_defineProperty({}, MOTION_KEY, MotionEntity), entitiesMap.keyEntities);\n\n        // Warning if treeNode not provide key\n        if (process.env.NODE_ENV !== 'production') {\n          warningWithoutKey(treeData, fieldNames);\n        }\n      }\n      var keyEntities = newState.keyEntities || prevState.keyEntities;\n\n      // ================ expandedKeys =================\n      if (needSync('expandedKeys') || prevProps && needSync('autoExpandParent')) {\n        newState.expandedKeys = props.autoExpandParent || !prevProps && props.defaultExpandParent ? conductExpandParent(props.expandedKeys, keyEntities) : props.expandedKeys;\n      } else if (!prevProps && props.defaultExpandAll) {\n        var cloneKeyEntities = _objectSpread({}, keyEntities);\n        delete cloneKeyEntities[MOTION_KEY];\n\n        // Only take the key who has the children to enhance the performance\n        var nextExpandedKeys = [];\n        Object.keys(cloneKeyEntities).forEach(function (key) {\n          var entity = cloneKeyEntities[key];\n          if (entity.children && entity.children.length) {\n            nextExpandedKeys.push(entity.key);\n          }\n        });\n        newState.expandedKeys = nextExpandedKeys;\n      } else if (!prevProps && props.defaultExpandedKeys) {\n        newState.expandedKeys = props.autoExpandParent || props.defaultExpandParent ? conductExpandParent(props.defaultExpandedKeys, keyEntities) : props.defaultExpandedKeys;\n      }\n      if (!newState.expandedKeys) {\n        delete newState.expandedKeys;\n      }\n\n      // ================ flattenNodes =================\n      if (treeData || newState.expandedKeys) {\n        var flattenNodes = flattenTreeData(treeData || prevState.treeData, newState.expandedKeys || prevState.expandedKeys, fieldNames);\n        newState.flattenNodes = flattenNodes;\n      }\n\n      // ================ selectedKeys =================\n      if (props.selectable) {\n        if (needSync('selectedKeys')) {\n          newState.selectedKeys = calcSelectedKeys(props.selectedKeys, props);\n        } else if (!prevProps && props.defaultSelectedKeys) {\n          newState.selectedKeys = calcSelectedKeys(props.defaultSelectedKeys, props);\n        }\n      }\n\n      // ================= checkedKeys =================\n      if (props.checkable) {\n        var checkedKeyEntity;\n        if (needSync('checkedKeys')) {\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {};\n        } else if (!prevProps && props.defaultCheckedKeys) {\n          checkedKeyEntity = parseCheckedKeys(props.defaultCheckedKeys) || {};\n        } else if (treeData) {\n          // If `treeData` changed, we also need check it\n          checkedKeyEntity = parseCheckedKeys(props.checkedKeys) || {\n            checkedKeys: prevState.checkedKeys,\n            halfCheckedKeys: prevState.halfCheckedKeys\n          };\n        }\n        if (checkedKeyEntity) {\n          var _checkedKeyEntity = checkedKeyEntity,\n            _checkedKeyEntity$che = _checkedKeyEntity.checkedKeys,\n            checkedKeys = _checkedKeyEntity$che === void 0 ? [] : _checkedKeyEntity$che,\n            _checkedKeyEntity$hal = _checkedKeyEntity.halfCheckedKeys,\n            halfCheckedKeys = _checkedKeyEntity$hal === void 0 ? [] : _checkedKeyEntity$hal;\n          if (!props.checkStrictly) {\n            var conductKeys = conductCheck(checkedKeys, true, keyEntities);\n            checkedKeys = conductKeys.checkedKeys;\n            halfCheckedKeys = conductKeys.halfCheckedKeys;\n          }\n          newState.checkedKeys = checkedKeys;\n          newState.halfCheckedKeys = halfCheckedKeys;\n        }\n      }\n\n      // ================= loadedKeys ==================\n      if (needSync('loadedKeys')) {\n        newState.loadedKeys = props.loadedKeys;\n      }\n      return newState;\n    }\n  }]);\n  return Tree;\n}(React.Component);\n_defineProperty(Tree, \"defaultProps\", {\n  prefixCls: 'rc-tree',\n  showLine: false,\n  showIcon: true,\n  selectable: true,\n  multiple: false,\n  checkable: false,\n  disabled: false,\n  checkStrictly: false,\n  draggable: false,\n  defaultExpandParent: true,\n  autoExpandParent: false,\n  defaultExpandAll: false,\n  defaultExpandedKeys: [],\n  defaultCheckedKeys: [],\n  defaultSelectedKeys: [],\n  dropIndicatorRender: DropIndicator,\n  allowDrop: function allowDrop() {\n    return true;\n  },\n  expandAction: false\n});\n_defineProperty(Tree, \"TreeNode\", TreeNode);\nexport default Tree;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE;AACA;;AAEA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,IAAIC,UAAU,EAAEC,YAAY,QAAQ,YAAY;AAC/D,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,QAAQ;AACjJ,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,qBAAqB,EAAEC,2BAA2B,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,kBAAkB;AAC9K,IAAIC,eAAe,GAAG,EAAE;AACxB,IAAIC,IAAI,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAClDjC,SAAS,CAACgC,IAAI,EAAEC,gBAAgB,CAAC;EACjC,IAAIC,MAAM,GAAGjC,YAAY,CAAC+B,IAAI,CAAC;EAC/B,SAASA,IAAIA,CAAA,EAAG;IACd,IAAIG,KAAK;IACTtC,eAAe,CAAC,IAAI,EAAEmC,IAAI,CAAC;IAC3B,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,KAAK,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACxFF,KAAK,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC/B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,KAAK,CAAC,CAAC;IACvDrC,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,WAAW,EAAE,KAAK,CAAC;IAClEjC,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,uBAAuB,EAAE,KAAK,CAAC,CAAC;IAC/EjC,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,mBAAmB,EAAE,CAAC,CAAC,CAAC;IACvEjC,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDU,WAAW,EAAE,CAAC,CAAC;MACfC,MAAM,EAAE,IAAI;MACZC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE,IAAI;MACrBC,gBAAgB,EAAE,EAAE;MACpB;MACA;MACA;MACAC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClB;MACAC,gBAAgB,EAAE,IAAI;MACtB;MACAC,eAAe,EAAE,IAAI;MACrB;MACAC,aAAa,EAAE,IAAI;MACnB;MACAC,WAAW,EAAE,IAAI;MACjB;MACA;MACA;MACA;MACAC,eAAe,EAAE,IAAI;MACrBC,QAAQ,EAAE,EAAE;MACZC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAEzC,cAAc,CAAC;IAC7B,CAAC,CAAC;IACFzB,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,wBAAwB,EAAE,IAAI,CAAC;IAC9EjC,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,eAAe,EAAE,IAAI,CAAC;IACrEjC,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,kCAAkC,EAAE,IAAI,CAAC;IACxFjC,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,SAAS,EAAE,aAAa5B,KAAK,CAAC8D,SAAS,CAAC,CAAC,CAAC;IACzFnE,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUmC,KAAK,EAAEC,SAAS,EAAE;MAC5F,IAAIC,WAAW,GAAGrC,KAAK,CAACsC,KAAK;QAC3BrB,YAAY,GAAGoB,WAAW,CAACpB,YAAY;QACvCP,WAAW,GAAG2B,WAAW,CAAC3B,WAAW;MACvC,IAAI6B,WAAW,GAAGvC,KAAK,CAACwC,KAAK,CAACD,WAAW;MACzC,IAAIE,QAAQ,GAAGL,SAAS,CAACK,QAAQ;MACjCzC,KAAK,CAAC0C,aAAa,GAAGN,SAAS;MAC/BpC,KAAK,CAAC2C,sBAAsB,GAAG;QAC7BC,CAAC,EAAET,KAAK,CAACU,OAAO;QAChBC,CAAC,EAAEX,KAAK,CAACY;MACX,CAAC;MACD,IAAIC,eAAe,GAAGpE,MAAM,CAACqC,YAAY,EAAEwB,QAAQ,CAAC;MACpDzC,KAAK,CAACiD,QAAQ,CAAC;QACb/B,eAAe,EAAEuB,QAAQ;QACzBtB,gBAAgB,EAAEnC,mBAAmB,CAACyD,QAAQ,EAAE/B,WAAW,CAAC;QAC5DC,MAAM,EAAEX,KAAK,CAACkD,OAAO,CAACC,OAAO,CAACC,cAAc,CAAC;MAC/C,CAAC,CAAC;MACFpD,KAAK,CAACqD,eAAe,CAACL,eAAe,CAAC;MACtCM,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEvD,KAAK,CAACwD,eAAe,CAAC;MACzDjB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAAC;QAC5DJ,KAAK,EAAEA,KAAK;QACZsB,IAAI,EAAEnE,2BAA2B,CAAC8C,SAAS;MAC7C,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;AACJ;AACA;AACA;AACA;AACA;AACA;IACIrE,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUmC,KAAK,EAAEC,SAAS,EAAE;MAC5F,IAAIsB,YAAY,GAAG1D,KAAK,CAACsC,KAAK;QAC5BrB,YAAY,GAAGyC,YAAY,CAACzC,YAAY;QACxCP,WAAW,GAAGgD,YAAY,CAAChD,WAAW;QACtCS,gBAAgB,GAAGuC,YAAY,CAACvC,gBAAgB;QAChDS,YAAY,GAAG8B,YAAY,CAAC9B,YAAY;QACxCjB,MAAM,GAAG+C,YAAY,CAAC/C,MAAM;MAC9B,IAAIgD,WAAW,GAAG3D,KAAK,CAACwC,KAAK;QAC3BoB,WAAW,GAAGD,WAAW,CAACC,WAAW;QACrCC,QAAQ,GAAGF,WAAW,CAACE,QAAQ;QAC/BC,SAAS,GAAGH,WAAW,CAACG,SAAS;QACjCC,SAAS,GAAGJ,WAAW,CAACI,SAAS;MACnC,IAAIC,GAAG,GAAG5B,SAAS,CAAC4B,GAAG;QACrBvB,QAAQ,GAAGL,SAAS,CAACK,QAAQ;;MAE/B;MACA,IAAIzC,KAAK,CAACiE,gCAAgC,KAAKxB,QAAQ,EAAE;QACvDzC,KAAK,CAACiE,gCAAgC,GAAGxB,QAAQ;MACnD;MACA,IAAI,CAACzC,KAAK,CAAC0C,aAAa,EAAE;QACxB1C,KAAK,CAACkE,cAAc,CAAC,CAAC;QACtB;MACF;MACA,IAAIC,iBAAiB,GAAGtF,gBAAgB,CAACsD,KAAK,EAAEnC,KAAK,CAAC0C,aAAa,EAAEN,SAAS,EAAEzB,MAAM,EAAEX,KAAK,CAAC2C,sBAAsB,EAAEmB,SAAS,EAAElC,YAAY,EAAElB,WAAW,EAAEO,YAAY,EAAE8C,SAAS,CAAC;QAClL1C,YAAY,GAAG8C,iBAAiB,CAAC9C,YAAY;QAC7CE,eAAe,GAAG4C,iBAAiB,CAAC5C,eAAe;QACnDH,aAAa,GAAG+C,iBAAiB,CAAC/C,aAAa;QAC/CE,gBAAgB,GAAG6C,iBAAiB,CAAC7C,gBAAgB;QACrDE,aAAa,GAAG2C,iBAAiB,CAAC3C,aAAa;QAC/CC,WAAW,GAAG0C,iBAAiB,CAAC1C,WAAW;QAC3CC,eAAe,GAAGyC,iBAAiB,CAACzC,eAAe;MACrD;MACA;MACAP,gBAAgB,CAACiD,QAAQ,CAAChD,aAAa,CAAC;MACxC;MACA,CAACK,WAAW,EAAE;QACZzB,KAAK,CAACkE,cAAc,CAAC,CAAC;QACtB;MACF;;MAEA;MACA,IAAI,CAAClE,KAAK,CAACqE,qBAAqB,EAAE;QAChCrE,KAAK,CAACqE,qBAAqB,GAAG,CAAC,CAAC;MAClC;MACAC,MAAM,CAACC,IAAI,CAACvE,KAAK,CAACqE,qBAAqB,CAAC,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC9DC,YAAY,CAAC1E,KAAK,CAACqE,qBAAqB,CAACI,GAAG,CAAC,CAAC;MAChD,CAAC,CAAC;MACF,IAAIzE,KAAK,CAAC0C,aAAa,CAACD,QAAQ,KAAKL,SAAS,CAACK,QAAQ,EAAE;QACvD;QACA;QACA;QACA;QACAN,KAAK,CAACwC,OAAO,CAAC,CAAC;QACf3E,KAAK,CAACqE,qBAAqB,CAACL,GAAG,CAAC,GAAGV,MAAM,CAACsB,UAAU,CAAC,YAAY;UAC/D,IAAI5E,KAAK,CAACsC,KAAK,CAACpB,eAAe,KAAK,IAAI,EAAE;YACxC;UACF;UACA,IAAI8B,eAAe,GAAGvF,kBAAkB,CAACwD,YAAY,CAAC;UACtD,IAAI4D,MAAM,GAAGzF,SAAS,CAACsB,WAAW,EAAE0B,SAAS,CAACK,QAAQ,CAAC;UACvD,IAAIoC,MAAM,IAAI,CAACA,MAAM,CAACC,QAAQ,IAAI,EAAE,EAAE3E,MAAM,EAAE;YAC5C6C,eAAe,GAAGrE,MAAM,CAACsC,YAAY,EAAEmB,SAAS,CAACK,QAAQ,CAAC;UAC5D;UACA,IAAI,CAACzC,KAAK,CAACwC,KAAK,CAACuC,cAAc,CAAC,cAAc,CAAC,EAAE;YAC/C/E,KAAK,CAACqD,eAAe,CAACL,eAAe,CAAC;UACxC;UACAa,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACb,eAAe,EAAE;YACpES,IAAI,EAAEnE,2BAA2B,CAAC8C,SAAS,CAAC;YAC5C4C,QAAQ,EAAE,IAAI;YACdC,WAAW,EAAE9C,KAAK,CAAC8C;UACrB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT;;MAEA;MACA,IAAIjF,KAAK,CAAC0C,aAAa,CAACD,QAAQ,KAAKrB,aAAa,IAAIG,eAAe,KAAK,CAAC,EAAE;QAC3EvB,KAAK,CAACkE,cAAc,CAAC,CAAC;QACtB;MACF;;MAEA;MACAlE,KAAK,CAACiD,QAAQ,CAAC;QACbvB,eAAe,EAAEA,eAAe;QAChCL,YAAY,EAAEA,YAAY;QAC1BE,eAAe,EAAEA,eAAe;QAChCH,aAAa,EAAEA,aAAa;QAC5BE,gBAAgB,EAAEA,gBAAgB;QAClCE,aAAa,EAAEA,aAAa;QAC5BC,WAAW,EAAEA;MACf,CAAC,CAAC;MACFmC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAAC;QAC5DzB,KAAK,EAAEA,KAAK;QACZsB,IAAI,EAAEnE,2BAA2B,CAAC8C,SAAS,CAAC;QAC5CnB,YAAY,EAAEA;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFlD,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,gBAAgB,EAAE,UAAUmC,KAAK,EAAEC,SAAS,EAAE;MAC3F,IAAI8C,YAAY,GAAGlF,KAAK,CAACsC,KAAK;QAC5BnB,gBAAgB,GAAG+D,YAAY,CAAC/D,gBAAgB;QAChDS,YAAY,GAAGsD,YAAY,CAACtD,YAAY;QACxClB,WAAW,GAAGwE,YAAY,CAACxE,WAAW;QACtCO,YAAY,GAAGiE,YAAY,CAACjE,YAAY;QACxCN,MAAM,GAAGuE,YAAY,CAACvE,MAAM;MAC9B,IAAIwE,YAAY,GAAGnF,KAAK,CAACwC,KAAK;QAC5B4C,UAAU,GAAGD,YAAY,CAACC,UAAU;QACpCtB,SAAS,GAAGqB,YAAY,CAACrB,SAAS;QAClCC,SAAS,GAAGoB,YAAY,CAACpB,SAAS;MACpC,IAAI,CAAC/D,KAAK,CAAC0C,aAAa,EAAE;QACxB;MACF;MACA,IAAI2C,kBAAkB,GAAGxG,gBAAgB,CAACsD,KAAK,EAAEnC,KAAK,CAAC0C,aAAa,EAAEN,SAAS,EAAEzB,MAAM,EAAEX,KAAK,CAAC2C,sBAAsB,EAAEmB,SAAS,EAAElC,YAAY,EAAElB,WAAW,EAAEO,YAAY,EAAE8C,SAAS,CAAC;QACnL1C,YAAY,GAAGgE,kBAAkB,CAAChE,YAAY;QAC9CE,eAAe,GAAG8D,kBAAkB,CAAC9D,eAAe;QACpDH,aAAa,GAAGiE,kBAAkB,CAACjE,aAAa;QAChDE,gBAAgB,GAAG+D,kBAAkB,CAAC/D,gBAAgB;QACtDE,aAAa,GAAG6D,kBAAkB,CAAC7D,aAAa;QAChDC,WAAW,GAAG4D,kBAAkB,CAAC5D,WAAW;QAC5CC,eAAe,GAAG2D,kBAAkB,CAAC3D,eAAe;MACtD,IAAIP,gBAAgB,CAACiD,QAAQ,CAAChD,aAAa,CAAC,IAAI,CAACK,WAAW,EAAE;QAC5D;QACA;QACA;MACF;;MAEA;;MAEA,IAAIzB,KAAK,CAAC0C,aAAa,CAACD,QAAQ,KAAKrB,aAAa,IAAIG,eAAe,KAAK,CAAC,EAAE;QAC3E,IAAI,EAAEvB,KAAK,CAACsC,KAAK,CAACjB,YAAY,KAAK,IAAI,IAAIrB,KAAK,CAACsC,KAAK,CAACf,eAAe,KAAK,IAAI,IAAIvB,KAAK,CAACsC,KAAK,CAAClB,aAAa,KAAK,IAAI,IAAIpB,KAAK,CAACsC,KAAK,CAAChB,gBAAgB,KAAK,IAAI,IAAItB,KAAK,CAACsC,KAAK,CAACd,aAAa,KAAK,IAAI,IAAIxB,KAAK,CAACsC,KAAK,CAACb,WAAW,KAAK,KAAK,IAAIzB,KAAK,CAACsC,KAAK,CAACZ,eAAe,KAAK,IAAI,CAAC,EAAE;UAClR1B,KAAK,CAACkE,cAAc,CAAC,CAAC;QACxB;MACF,CAAC,MAAM,IAAI,EAAE7C,YAAY,KAAKrB,KAAK,CAACsC,KAAK,CAACjB,YAAY,IAAIE,eAAe,KAAKvB,KAAK,CAACsC,KAAK,CAACf,eAAe,IAAIH,aAAa,KAAKpB,KAAK,CAACsC,KAAK,CAAClB,aAAa,IAAIE,gBAAgB,KAAKtB,KAAK,CAACsC,KAAK,CAAChB,gBAAgB,IAAIE,aAAa,KAAKxB,KAAK,CAACsC,KAAK,CAACd,aAAa,IAAIC,WAAW,KAAKzB,KAAK,CAACsC,KAAK,CAACb,WAAW,IAAIC,eAAe,KAAK1B,KAAK,CAACsC,KAAK,CAACZ,eAAe,CAAC,EAAE;QAC3V1B,KAAK,CAACiD,QAAQ,CAAC;UACb5B,YAAY,EAAEA,YAAY;UAC1BE,eAAe,EAAEA,eAAe;UAChCH,aAAa,EAAEA,aAAa;UAC5BE,gBAAgB,EAAEA,gBAAgB;UAClCE,aAAa,EAAEA,aAAa;UAC5BC,WAAW,EAAEA,WAAW;UACxBC,eAAe,EAAEA;QACnB,CAAC,CAAC;MACJ;MACA0D,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAAC;QACzDjD,KAAK,EAAEA,KAAK;QACZsB,IAAI,EAAEnE,2BAA2B,CAAC8C,SAAS;MAC7C,CAAC,CAAC;IACJ,CAAC,CAAC;IACFrE,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUmC,KAAK,EAAEC,SAAS,EAAE;MAC5F;MACA;MACA,IAAIpC,KAAK,CAACiE,gCAAgC,KAAK7B,SAAS,CAACK,QAAQ,IAAI,CAACN,KAAK,CAACmD,aAAa,CAACC,QAAQ,CAACpD,KAAK,CAACqD,aAAa,CAAC,EAAE;QACvHxF,KAAK,CAACkE,cAAc,CAAC,CAAC;QACtBlE,KAAK,CAACiE,gCAAgC,GAAG,IAAI;MAC/C;MACA,IAAIwB,WAAW,GAAGzF,KAAK,CAACwC,KAAK,CAACiD,WAAW;MACzCA,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAAC;QAC5DtD,KAAK,EAAEA,KAAK;QACZsB,IAAI,EAAEnE,2BAA2B,CAAC8C,SAAS;MAC7C,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACA;IACArE,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUmC,KAAK,EAAE;MACjFnC,KAAK,CAAC0F,aAAa,CAACvD,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;MACtCmB,MAAM,CAACqC,mBAAmB,CAAC,SAAS,EAAE3F,KAAK,CAACwD,eAAe,CAAC;IAC9D,CAAC,CAAC;IACF;IACAzF,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,eAAe,EAAE,UAAUmC,KAAK,EAAEC,SAAS,EAAE;MAC1F,IAAIwD,SAAS,GAAG5F,KAAK,CAACwC,KAAK,CAACoD,SAAS;MACrC5F,KAAK,CAACiD,QAAQ,CAAC;QACbvB,eAAe,EAAE;MACnB,CAAC,CAAC;MACF1B,KAAK,CAAC6F,cAAc,CAAC,CAAC;MACtBD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAAC;QACtDzD,KAAK,EAAEA,KAAK;QACZsB,IAAI,EAAEnE,2BAA2B,CAAC8C,SAAS;MAC7C,CAAC,CAAC;MACFpC,KAAK,CAAC0C,aAAa,GAAG,IAAI;MAC1BY,MAAM,CAACqC,mBAAmB,CAAC,SAAS,EAAE3F,KAAK,CAACwD,eAAe,CAAC;IAC9D,CAAC,CAAC;IACFzF,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,YAAY,EAAE,UAAUmC,KAAK,EAAE2D,CAAC,EAAE;MAC/E,IAAIC,mBAAmB;MACvB,IAAIC,WAAW,GAAG9F,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK+F,SAAS,GAAG/F,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC3F,IAAIgG,YAAY,GAAGlG,KAAK,CAACsC,KAAK;QAC5BnB,gBAAgB,GAAG+E,YAAY,CAAC/E,gBAAgB;QAChDE,YAAY,GAAG6E,YAAY,CAAC7E,YAAY;QACxCD,aAAa,GAAG8E,YAAY,CAAC9E,aAAa;QAC1CI,aAAa,GAAG0E,YAAY,CAAC1E,aAAa;QAC1CC,WAAW,GAAGyE,YAAY,CAACzE,WAAW;MACxC,IAAI,CAACA,WAAW,EAAE;QAChB;MACF;MACA,IAAI0E,MAAM,GAAGnG,KAAK,CAACwC,KAAK,CAAC2D,MAAM;MAC/BnG,KAAK,CAACiD,QAAQ,CAAC;QACbvB,eAAe,EAAE;MACnB,CAAC,CAAC;MACF1B,KAAK,CAAC6F,cAAc,CAAC,CAAC;MACtB,IAAIzE,aAAa,KAAK,IAAI,EAAE;MAC5B,IAAIgF,qBAAqB,GAAG5I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkC,gBAAgB,CAAC0B,aAAa,EAAEpB,KAAK,CAACqG,wBAAwB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAClIC,MAAM,EAAE,CAAC,CAACP,mBAAmB,GAAG/F,KAAK,CAACuG,aAAa,CAAC,CAAC,MAAM,IAAI,IAAIR,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACtB,GAAG,MAAMrD,aAAa;QACvJoF,IAAI,EAAEpH,SAAS,CAACY,KAAK,CAACsC,KAAK,CAAC5B,WAAW,EAAEU,aAAa,CAAC,CAACqC;MAC1D,CAAC,CAAC;MACF,IAAIgD,WAAW,GAAGtF,gBAAgB,CAACiD,QAAQ,CAAChD,aAAa,CAAC;MAC1DjD,OAAO,CAAC,CAACsI,WAAW,EAAE,6FAA6F,CAAC;MACpH,IAAIC,MAAM,GAAGxH,QAAQ,CAACsC,aAAa,CAAC;MACpC,IAAImF,UAAU,GAAG;QACfxE,KAAK,EAAEA,KAAK;QACZsB,IAAI,EAAEnE,2BAA2B,CAAC8G,qBAAqB,CAAC;QACxDQ,QAAQ,EAAE5G,KAAK,CAAC0C,aAAa,GAAGpD,2BAA2B,CAACU,KAAK,CAAC0C,aAAa,CAAC,GAAG,IAAI;QACvFmE,aAAa,EAAE,CAAC7G,KAAK,CAAC0C,aAAa,CAACD,QAAQ,CAAC,CAAChC,MAAM,CAACU,gBAAgB,CAAC;QACtE2F,SAAS,EAAEzF,YAAY,KAAK,CAAC;QAC7BA,YAAY,EAAEA,YAAY,GAAG0F,MAAM,CAACL,MAAM,CAACA,MAAM,CAACvG,MAAM,GAAG,CAAC,CAAC;MAC/D,CAAC;MACD,IAAI,CAAC6F,WAAW,EAAE;QAChBG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACQ,UAAU,CAAC;MAC5D;MACA3G,KAAK,CAAC0C,aAAa,GAAG,IAAI;IAC5B,CAAC,CAAC;IACF3E,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,gBAAgB,EAAE,YAAY;MAC3E,IAAIkB,eAAe,GAAGlB,KAAK,CAACsC,KAAK,CAACpB,eAAe;MACjD,IAAIA,eAAe,KAAK,IAAI,EAAE;QAC5BlB,KAAK,CAACiD,QAAQ,CAAC;UACb/B,eAAe,EAAE,IAAI;UACrBG,YAAY,EAAE,IAAI;UAClBC,gBAAgB,EAAE,IAAI;UACtBF,aAAa,EAAE,IAAI;UACnBG,eAAe,EAAE,IAAI;UACrBE,WAAW,EAAE,IAAI;UACjBC,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;MACA1B,KAAK,CAAC2C,sBAAsB,GAAG,IAAI;MACnC3C,KAAK,CAACiE,gCAAgC,GAAG,IAAI;IAC/C,CAAC,CAAC;IACFlG,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,2BAA2B,EAAE,UAAUgH,CAAC,EAAEC,QAAQ,EAAE;MACjG,IAAIC,YAAY,GAAGlH,KAAK,CAACsC,KAAK;QAC5BrB,YAAY,GAAGiG,YAAY,CAACjG,YAAY;QACxCW,YAAY,GAAGsF,YAAY,CAACtF,YAAY;MAC1C,IAAIoD,QAAQ,GAAGiC,QAAQ,CAACjC,QAAQ;QAC9BP,GAAG,GAAGwC,QAAQ,CAACxC,GAAG;QAClB0C,MAAM,GAAGF,QAAQ,CAACE,MAAM;MAC1B,IAAIA,MAAM,IAAIH,CAAC,CAACI,QAAQ,IAAIJ,CAAC,CAACK,OAAO,IAAIL,CAAC,CAACM,OAAO,EAAE;QAClD;MACF;MACA,IAAI7D,IAAI,GAAG7B,YAAY,CAAC2F,MAAM,CAAC,UAAUC,QAAQ,EAAE;QACjD,OAAOA,QAAQ,CAAC/C,GAAG,KAAKA,GAAG;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,IAAIgD,SAAS,GAAGnI,2BAA2B,CAAC9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkC,gBAAgB,CAAC+E,GAAG,EAAEzE,KAAK,CAACqG,wBAAwB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACxIG,IAAI,EAAE/C,IAAI,CAAC+C;MACb,CAAC,CAAC,CAAC;MACHxG,KAAK,CAACqD,eAAe,CAAC2B,QAAQ,GAAGpG,MAAM,CAACqC,YAAY,EAAEwD,GAAG,CAAC,GAAG9F,MAAM,CAACsC,YAAY,EAAEwD,GAAG,CAAC,CAAC;MACvFzE,KAAK,CAAC0H,YAAY,CAACV,CAAC,EAAES,SAAS,CAAC;IAClC,CAAC,CAAC;IACF1J,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,aAAa,EAAE,UAAUgH,CAAC,EAAEC,QAAQ,EAAE;MACnF,IAAIU,YAAY,GAAG3H,KAAK,CAACwC,KAAK;QAC5BoF,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BC,YAAY,GAAGF,YAAY,CAACE,YAAY;MAC1C,IAAIA,YAAY,KAAK,OAAO,EAAE;QAC5B7H,KAAK,CAAC8H,yBAAyB,CAACd,CAAC,EAAEC,QAAQ,CAAC;MAC9C;MACAW,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACZ,CAAC,EAAEC,QAAQ,CAAC;IAChE,CAAC,CAAC;IACFlJ,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,mBAAmB,EAAE,UAAUgH,CAAC,EAAEC,QAAQ,EAAE;MACzF,IAAIc,YAAY,GAAG/H,KAAK,CAACwC,KAAK;QAC5BwF,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CH,YAAY,GAAGE,YAAY,CAACF,YAAY;MAC1C,IAAIA,YAAY,KAAK,aAAa,EAAE;QAClC7H,KAAK,CAAC8H,yBAAyB,CAACd,CAAC,EAAEC,QAAQ,CAAC;MAC9C;MACAe,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,IAAIA,aAAa,CAAChB,CAAC,EAAEC,QAAQ,CAAC;IAClF,CAAC,CAAC;IACFlJ,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,cAAc,EAAE,UAAUgH,CAAC,EAAEC,QAAQ,EAAE;MACpF,IAAIrG,YAAY,GAAGZ,KAAK,CAACsC,KAAK,CAAC1B,YAAY;MAC3C,IAAIqH,YAAY,GAAGjI,KAAK,CAACsC,KAAK;QAC5B5B,WAAW,GAAGuH,YAAY,CAACvH,WAAW;QACtCuB,UAAU,GAAGgG,YAAY,CAAChG,UAAU;MACtC,IAAIiG,YAAY,GAAGlI,KAAK,CAACwC,KAAK;QAC5B2F,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;MAClC,IAAIC,QAAQ,GAAGpB,QAAQ,CAACoB,QAAQ;MAChC,IAAI5D,GAAG,GAAGwC,QAAQ,CAAChF,UAAU,CAACwC,GAAG,CAAC;MAClC,IAAI6D,cAAc,GAAG,CAACD,QAAQ;;MAE9B;MACA,IAAI,CAACC,cAAc,EAAE;QACnB1H,YAAY,GAAGhC,MAAM,CAACgC,YAAY,EAAE6D,GAAG,CAAC;MAC1C,CAAC,MAAM,IAAI,CAAC2D,QAAQ,EAAE;QACpBxH,YAAY,GAAG,CAAC6D,GAAG,CAAC;MACtB,CAAC,MAAM;QACL7D,YAAY,GAAGjC,MAAM,CAACiC,YAAY,EAAE6D,GAAG,CAAC;MAC1C;;MAEA;MACA,IAAI8D,aAAa,GAAG3H,YAAY,CAAC4H,GAAG,CAAC,UAAUC,WAAW,EAAE;QAC1D,IAAI5D,MAAM,GAAGzF,SAAS,CAACsB,WAAW,EAAE+H,WAAW,CAAC;QAChD,OAAO5D,MAAM,GAAGA,MAAM,CAACpB,IAAI,GAAG,IAAI;MACpC,CAAC,CAAC,CAAC8D,MAAM,CAACmB,OAAO,CAAC;MAClB1I,KAAK,CAAC2I,oBAAoB,CAAC;QACzB/H,YAAY,EAAEA;MAChB,CAAC,CAAC;MACFuH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACvH,YAAY,EAAE;QACjEuB,KAAK,EAAE,QAAQ;QACfkG,QAAQ,EAAEC,cAAc;QACxB7E,IAAI,EAAEwD,QAAQ;QACdsB,aAAa,EAAEA,aAAa;QAC5BtD,WAAW,EAAE+B,CAAC,CAAC/B;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFlH,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,aAAa,EAAE,UAAUgH,CAAC,EAAEC,QAAQ,EAAE2B,OAAO,EAAE;MAC5F,IAAIC,YAAY,GAAG7I,KAAK,CAACsC,KAAK;QAC5B5B,WAAW,GAAGmI,YAAY,CAACnI,WAAW;QACtCoI,cAAc,GAAGD,YAAY,CAAChI,WAAW;QACzCkI,kBAAkB,GAAGF,YAAY,CAAC/H,eAAe;MACnD,IAAIkI,YAAY,GAAGhJ,KAAK,CAACwC,KAAK;QAC5ByG,aAAa,GAAGD,YAAY,CAACC,aAAa;QAC1CC,OAAO,GAAGF,YAAY,CAACE,OAAO;MAChC,IAAIzE,GAAG,GAAGwC,QAAQ,CAACxC,GAAG;;MAEtB;MACA,IAAI0E,UAAU;MACd,IAAIC,QAAQ,GAAG;QACbjH,KAAK,EAAE,OAAO;QACdsB,IAAI,EAAEwD,QAAQ;QACd2B,OAAO,EAAEA,OAAO;QAChB3D,WAAW,EAAE+B,CAAC,CAAC/B;MACjB,CAAC;MACD,IAAIgE,aAAa,EAAE;QACjB,IAAIpI,WAAW,GAAG+H,OAAO,GAAGjK,MAAM,CAACmK,cAAc,EAAErE,GAAG,CAAC,GAAG7F,MAAM,CAACkK,cAAc,EAAErE,GAAG,CAAC;QACrF,IAAI3D,eAAe,GAAGlC,MAAM,CAACmK,kBAAkB,EAAEtE,GAAG,CAAC;QACrD0E,UAAU,GAAG;UACXP,OAAO,EAAE/H,WAAW;UACpBwI,WAAW,EAAEvI;QACf,CAAC;QACDsI,QAAQ,CAACE,YAAY,GAAGzI,WAAW,CAAC2H,GAAG,CAAC,UAAUe,UAAU,EAAE;UAC5D,OAAOnK,SAAS,CAACsB,WAAW,EAAE6I,UAAU,CAAC;QAC3C,CAAC,CAAC,CAAChC,MAAM,CAACmB,OAAO,CAAC,CAACF,GAAG,CAAC,UAAU3D,MAAM,EAAE;UACvC,OAAOA,MAAM,CAACpB,IAAI;QACpB,CAAC,CAAC;QACFzD,KAAK,CAAC2I,oBAAoB,CAAC;UACzB9H,WAAW,EAAEA;QACf,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAI2I,aAAa,GAAGrK,YAAY,CAAC,EAAE,CAACsB,MAAM,CAAChD,kBAAkB,CAACqL,cAAc,CAAC,EAAE,CAACrE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE/D,WAAW,CAAC;UACvG+I,YAAY,GAAGD,aAAa,CAAC3I,WAAW;UACxC6I,gBAAgB,GAAGF,aAAa,CAAC1I,eAAe;;QAElD;QACA,IAAI,CAAC8H,OAAO,EAAE;UACZ,IAAIe,MAAM,GAAG,IAAIC,GAAG,CAACH,YAAY,CAAC;UAClCE,MAAM,CAACE,MAAM,CAACpF,GAAG,CAAC;UAClB,IAAIqF,cAAc,GAAG3K,YAAY,CAACkB,KAAK,CAAC0J,IAAI,CAACJ,MAAM,CAAC,EAAE;YACpDf,OAAO,EAAE,KAAK;YACd9H,eAAe,EAAE4I;UACnB,CAAC,EAAEhJ,WAAW,CAAC;UACf+I,YAAY,GAAGK,cAAc,CAACjJ,WAAW;UACzC6I,gBAAgB,GAAGI,cAAc,CAAChJ,eAAe;QACnD;QACAqI,UAAU,GAAGM,YAAY;;QAEzB;QACAL,QAAQ,CAACE,YAAY,GAAG,EAAE;QAC1BF,QAAQ,CAACY,qBAAqB,GAAG,EAAE;QACnCZ,QAAQ,CAACtI,eAAe,GAAG4I,gBAAgB;QAC3CD,YAAY,CAACjF,OAAO,CAAC,UAAU+E,UAAU,EAAE;UACzC,IAAI1E,MAAM,GAAGzF,SAAS,CAACsB,WAAW,EAAE6I,UAAU,CAAC;UAC/C,IAAI,CAAC1E,MAAM,EAAE;UACb,IAAIpB,IAAI,GAAGoB,MAAM,CAACpB,IAAI;YACpBO,GAAG,GAAGa,MAAM,CAACb,GAAG;UAClBoF,QAAQ,CAACE,YAAY,CAACW,IAAI,CAACxG,IAAI,CAAC;UAChC2F,QAAQ,CAACY,qBAAqB,CAACC,IAAI,CAAC;YAClCxG,IAAI,EAAEA,IAAI;YACVO,GAAG,EAAEA;UACP,CAAC,CAAC;QACJ,CAAC,CAAC;QACFhE,KAAK,CAAC2I,oBAAoB,CAAC;UACzB9H,WAAW,EAAE4I;QACf,CAAC,EAAE,KAAK,EAAE;UACR3I,eAAe,EAAE4I;QACnB,CAAC,CAAC;MACJ;MACAR,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,UAAU,EAAEC,QAAQ,CAAC;IACzE,CAAC,CAAC;IACFrL,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,YAAY,EAAE,UAAUiH,QAAQ,EAAE;MAC/E,IAAIiD,gBAAgB;MACpB,IAAIzF,GAAG,GAAGwC,QAAQ,CAACxC,GAAG;MACtB,IAAI/D,WAAW,GAAGV,KAAK,CAACsC,KAAK,CAAC5B,WAAW;;MAEzC;MACA,IAAImE,MAAM,GAAGzF,SAAS,CAACsB,WAAW,EAAE+D,GAAG,CAAC;MACxC,IAAII,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAI,CAACqF,gBAAgB,GAAGrF,MAAM,CAACC,QAAQ,MAAM,IAAI,IAAIoF,gBAAgB,KAAK,KAAK,CAAC,IAAIA,gBAAgB,CAAC/J,MAAM,EAAE;QACnJ;MACF;MACA,IAAIgK,WAAW,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;QACvD;QACAtK,KAAK,CAACiD,QAAQ,CAAC,UAAUsH,IAAI,EAAE;UAC7B,IAAIC,eAAe,GAAGD,IAAI,CAACxJ,UAAU;YACnCA,UAAU,GAAGyJ,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;YAC9DC,gBAAgB,GAAGF,IAAI,CAACvJ,WAAW;YACnCA,WAAW,GAAGyJ,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;UACnE,IAAIC,YAAY,GAAG1K,KAAK,CAACwC,KAAK;YAC5BmI,QAAQ,GAAGD,YAAY,CAACC,QAAQ;YAChCC,MAAM,GAAGF,YAAY,CAACE,MAAM;UAC9B,IAAI,CAACD,QAAQ,IAAI5J,UAAU,CAACqD,QAAQ,CAACK,GAAG,CAAC,IAAIzD,WAAW,CAACoD,QAAQ,CAACK,GAAG,CAAC,EAAE;YACtE,OAAO,IAAI;UACb;;UAEA;UACA,IAAIoG,OAAO,GAAGF,QAAQ,CAAC1D,QAAQ,CAAC;UAChC4D,OAAO,CAACC,IAAI,CAAC,YAAY;YACvB,IAAIC,iBAAiB,GAAG/K,KAAK,CAACsC,KAAK,CAACvB,UAAU;YAC9C,IAAIiK,aAAa,GAAGrM,MAAM,CAACoM,iBAAiB,EAAEtG,GAAG,CAAC;;YAElD;YACA;YACAmG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACI,aAAa,EAAE;cAC5D7I,KAAK,EAAE,MAAM;cACbsB,IAAI,EAAEwD;YACR,CAAC,CAAC;YACFjH,KAAK,CAAC2I,oBAAoB,CAAC;cACzB5H,UAAU,EAAEiK;YACd,CAAC,CAAC;YACFhL,KAAK,CAACiD,QAAQ,CAAC,UAAUgI,SAAS,EAAE;cAClC,OAAO;gBACLjK,WAAW,EAAEpC,MAAM,CAACqM,SAAS,CAACjK,WAAW,EAAEyD,GAAG;cAChD,CAAC;YACH,CAAC,CAAC;YACF4F,OAAO,CAAC,CAAC;UACX,CAAC,CAAC,CAACa,KAAK,CAAC,UAAUlE,CAAC,EAAE;YACpBhH,KAAK,CAACiD,QAAQ,CAAC,UAAUgI,SAAS,EAAE;cAClC,OAAO;gBACLjK,WAAW,EAAEpC,MAAM,CAACqM,SAAS,CAACjK,WAAW,EAAEyD,GAAG;cAChD,CAAC;YACH,CAAC,CAAC;;YAEF;YACAzE,KAAK,CAACmL,iBAAiB,CAAC1G,GAAG,CAAC,GAAG,CAACzE,KAAK,CAACmL,iBAAiB,CAAC1G,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;YACtE,IAAIzE,KAAK,CAACmL,iBAAiB,CAAC1G,GAAG,CAAC,IAAI7E,eAAe,EAAE;cACnD,IAAImL,iBAAiB,GAAG/K,KAAK,CAACsC,KAAK,CAACvB,UAAU;cAC9C5C,OAAO,CAAC,KAAK,EAAE,kEAAkE,CAAC;cAClF6B,KAAK,CAAC2I,oBAAoB,CAAC;gBACzB5H,UAAU,EAAEpC,MAAM,CAACoM,iBAAiB,EAAEtG,GAAG;cAC3C,CAAC,CAAC;cACF4F,OAAO,CAAC,CAAC;YACX;YACAC,MAAM,CAACtD,CAAC,CAAC;UACX,CAAC,CAAC;UACF,OAAO;YACLhG,WAAW,EAAErC,MAAM,CAACqC,WAAW,EAAEyD,GAAG;UACtC,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACA0F,WAAW,CAACe,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;MACjC,OAAOf,WAAW;IACpB,CAAC,CAAC;IACFpM,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAUmC,KAAK,EAAEsB,IAAI,EAAE;MACxF,IAAI2H,YAAY,GAAGpL,KAAK,CAACwC,KAAK,CAAC4I,YAAY;MAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAAC;QAC/DjJ,KAAK,EAAEA,KAAK;QACZsB,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACF1F,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,kBAAkB,EAAE,UAAUmC,KAAK,EAAEsB,IAAI,EAAE;MACxF,IAAI4H,YAAY,GAAGrL,KAAK,CAACwC,KAAK,CAAC6I,YAAY;MAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAAC;QAC/DlJ,KAAK,EAAEA,KAAK;QACZsB,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACF1F,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,mBAAmB,EAAE,UAAUmC,KAAK,EAAEsB,IAAI,EAAE;MACzF,IAAI6H,YAAY,GAAGtL,KAAK,CAACwC,KAAK,CAAC8I,YAAY;MAC3C,IAAIA,YAAY,EAAE;QAChBnJ,KAAK,CAACoJ,cAAc,CAAC,CAAC;QACtBD,YAAY,CAAC;UACXnJ,KAAK,EAAEA,KAAK;UACZsB,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF1F,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,SAAS,EAAE,YAAY;MACpE,IAAIwL,OAAO,GAAGxL,KAAK,CAACwC,KAAK,CAACgJ,OAAO;MACjCxL,KAAK,CAACiD,QAAQ,CAAC;QACbpB,OAAO,EAAE;MACX,CAAC,CAAC;MACF,KAAK,IAAI4J,KAAK,GAAGvL,SAAS,CAACC,MAAM,EAAEuL,IAAI,GAAG,IAAIrL,KAAK,CAACoL,KAAK,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;QAC7FD,IAAI,CAACC,KAAK,CAAC,GAAGzL,SAAS,CAACyL,KAAK,CAAC;MAChC;MACAH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAAChL,KAAK,CAAC,KAAK,CAAC,EAAEkL,IAAI,CAAC;IACvE,CAAC,CAAC;IACF3N,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,QAAQ,EAAE,YAAY;MACnE,IAAI4L,MAAM,GAAG5L,KAAK,CAACwC,KAAK,CAACoJ,MAAM;MAC/B5L,KAAK,CAACiD,QAAQ,CAAC;QACbpB,OAAO,EAAE;MACX,CAAC,CAAC;MACF7B,KAAK,CAAC6L,cAAc,CAAC,IAAI,CAAC;MAC1B,KAAK,IAAIC,KAAK,GAAG5L,SAAS,CAACC,MAAM,EAAEuL,IAAI,GAAG,IAAIrL,KAAK,CAACyL,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FL,IAAI,CAACK,KAAK,CAAC,GAAG7L,SAAS,CAAC6L,KAAK,CAAC;MAChC;MACAH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACpL,KAAK,CAAC,KAAK,CAAC,EAAEkL,IAAI,CAAC;IACpE,CAAC,CAAC;IACF3N,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,0BAA0B,EAAE,YAAY;MACrF,IAAIgM,YAAY,GAAGhM,KAAK,CAACsC,KAAK;QAC5BrB,YAAY,GAAG+K,YAAY,CAAC/K,YAAY;QACxCL,YAAY,GAAGoL,YAAY,CAACpL,YAAY;QACxCG,UAAU,GAAGiL,YAAY,CAACjL,UAAU;QACpCC,WAAW,GAAGgL,YAAY,CAAChL,WAAW;QACtCH,WAAW,GAAGmL,YAAY,CAACnL,WAAW;QACtCC,eAAe,GAAGkL,YAAY,CAAClL,eAAe;QAC9CY,eAAe,GAAGsK,YAAY,CAACtK,eAAe;QAC9CL,YAAY,GAAG2K,YAAY,CAAC3K,YAAY;QACxCX,WAAW,GAAGsL,YAAY,CAACtL,WAAW;MACxC,OAAO;QACLO,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCL,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCG,UAAU,EAAEA,UAAU,IAAI,EAAE;QAC5BC,WAAW,EAAEA,WAAW,IAAI,EAAE;QAC9BH,WAAW,EAAEA,WAAW,IAAI,EAAE;QAC9BC,eAAe,EAAEA,eAAe,IAAI,EAAE;QACtCY,eAAe,EAAEA,eAAe;QAChCL,YAAY,EAAEA,YAAY;QAC1BX,WAAW,EAAEA;MACf,CAAC;IACH,CAAC,CAAC;IACF;IACA;IACA3C,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAUiB,YAAY,EAAE;MACxF,IAAIgL,YAAY,GAAGjM,KAAK,CAACsC,KAAK;QAC5BX,QAAQ,GAAGsK,YAAY,CAACtK,QAAQ;QAChCM,UAAU,GAAGgK,YAAY,CAAChK,UAAU;MACtC,IAAIL,YAAY,GAAGnC,eAAe,CAACkC,QAAQ,EAAEV,YAAY,EAAEgB,UAAU,CAAC;MACtEjC,KAAK,CAAC2I,oBAAoB,CAAC;QACzB1H,YAAY,EAAEA,YAAY;QAC1BW,YAAY,EAAEA;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;IACF7D,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,cAAc,EAAE,UAAUgH,CAAC,EAAEC,QAAQ,EAAE;MACpF,IAAIhG,YAAY,GAAGjB,KAAK,CAACsC,KAAK,CAACrB,YAAY;MAC3C,IAAIiL,aAAa,GAAGlM,KAAK,CAACsC,KAAK;QAC7BP,YAAY,GAAGmK,aAAa,CAACnK,YAAY;QACzCE,UAAU,GAAGiK,aAAa,CAACjK,UAAU;MACvC,IAAIkK,YAAY,GAAGnM,KAAK,CAACwC,KAAK;QAC5BqB,QAAQ,GAAGsI,YAAY,CAACtI,QAAQ;QAChC8G,QAAQ,GAAGwB,YAAY,CAACxB,QAAQ;MAClC,IAAI3F,QAAQ,GAAGiC,QAAQ,CAACjC,QAAQ;MAChC,IAAIP,GAAG,GAAGwC,QAAQ,CAAChF,UAAU,CAACwC,GAAG,CAAC;;MAElC;MACA,IAAI1C,YAAY,EAAE;QAChB;MACF;;MAEA;MACA,IAAIqK,OAAO,GAAGnL,YAAY,CAACmD,QAAQ,CAACK,GAAG,CAAC;MACxC,IAAI4H,cAAc,GAAG,CAACrH,QAAQ;MAC9B7G,OAAO,CAAC6G,QAAQ,IAAIoH,OAAO,IAAI,CAACpH,QAAQ,IAAI,CAACoH,OAAO,EAAE,wCAAwC,CAAC;MAC/FnL,YAAY,GAAGoL,cAAc,GAAG1N,MAAM,CAACsC,YAAY,EAAEwD,GAAG,CAAC,GAAG7F,MAAM,CAACqC,YAAY,EAAEwD,GAAG,CAAC;MACrFzE,KAAK,CAACqD,eAAe,CAACpC,YAAY,CAAC;MACnC4C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAAC5C,YAAY,EAAE;QACjEwC,IAAI,EAAEwD,QAAQ;QACdjC,QAAQ,EAAEqH,cAAc;QACxBpH,WAAW,EAAE+B,CAAC,CAAC/B;MACjB,CAAC,CAAC;;MAEF;MACA,IAAIoH,cAAc,IAAI1B,QAAQ,EAAE;QAC9B,IAAIR,WAAW,GAAGnK,KAAK,CAACsM,UAAU,CAACrF,QAAQ,CAAC;QAC5C,IAAIkD,WAAW,EAAE;UACfA,WAAW,CAACW,IAAI,CAAC,YAAY;YAC3B;YACA,IAAIyB,kBAAkB,GAAG9M,eAAe,CAACO,KAAK,CAACsC,KAAK,CAACX,QAAQ,EAAEV,YAAY,EAAEgB,UAAU,CAAC;YACxFjC,KAAK,CAAC2I,oBAAoB,CAAC;cACzB/G,YAAY,EAAE2K;YAChB,CAAC,CAAC;UACJ,CAAC,CAAC,CAACrB,KAAK,CAAC,YAAY;YACnB,IAAIsB,mBAAmB,GAAGxM,KAAK,CAACsC,KAAK,CAACrB,YAAY;YAClD,IAAIwL,qBAAqB,GAAG7N,MAAM,CAAC4N,mBAAmB,EAAE/H,GAAG,CAAC;YAC5DzE,KAAK,CAACqD,eAAe,CAACoJ,qBAAqB,CAAC;UAC9C,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IACF1O,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,mBAAmB,EAAE,YAAY;MAC9EA,KAAK,CAAC2I,oBAAoB,CAAC;QACzB5G,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;IACFhE,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,iBAAiB,EAAE,YAAY;MAC5E4E,UAAU,CAAC,YAAY;QACrB5E,KAAK,CAAC2I,oBAAoB,CAAC;UACzB5G,YAAY,EAAE;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACAhE,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,gBAAgB,EAAE,UAAU0M,YAAY,EAAE;MACvF,IAAI5K,SAAS,GAAG9B,KAAK,CAACsC,KAAK,CAACR,SAAS;MACrC,IAAI6K,YAAY,GAAG3M,KAAK,CAACwC,KAAK;QAC5BqJ,cAAc,GAAGc,YAAY,CAACd,cAAc;QAC5Ce,qBAAqB,GAAGD,YAAY,CAACE,gBAAgB;QACrDA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;MACjF,IAAI9K,SAAS,KAAK4K,YAAY,EAAE;QAC9B;MACF;MACA1M,KAAK,CAACiD,QAAQ,CAAC;QACbnB,SAAS,EAAE4K;MACb,CAAC,CAAC;MACF,IAAIA,YAAY,KAAK,IAAI,EAAE;QACzB1M,KAAK,CAAC8M,QAAQ,CAAC;UACbrI,GAAG,EAAEiI,YAAY;UACjBK,MAAM,EAAEF;QACV,CAAC,CAAC;MACJ;MACAhB,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,IAAIA,cAAc,CAACa,YAAY,CAAC;IACtF,CAAC,CAAC;IACF3O,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,eAAe,EAAE,YAAY;MAC1E,IAAIgN,aAAa,GAAGhN,KAAK,CAACsC,KAAK;QAC7BR,SAAS,GAAGkL,aAAa,CAAClL,SAAS;QACnCF,YAAY,GAAGoL,aAAa,CAACpL,YAAY;MAC3C,IAAIE,SAAS,KAAK,IAAI,EAAE;QACtB,OAAO,IAAI;MACb;MACA,OAAOF,YAAY,CAACqL,IAAI,CAAC,UAAUC,KAAK,EAAE;QACxC,IAAIzI,GAAG,GAAGyI,KAAK,CAACzI,GAAG;QACnB,OAAOA,GAAG,KAAK3C,SAAS;MAC1B,CAAC,CAAC,IAAI,IAAI;IACZ,CAAC,CAAC;IACF/D,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,iBAAiB,EAAE,UAAU+M,MAAM,EAAE;MAClF,IAAII,aAAa,GAAGnN,KAAK,CAACsC,KAAK;QAC7BV,YAAY,GAAGuL,aAAa,CAACvL,YAAY;QACzCE,SAAS,GAAGqL,aAAa,CAACrL,SAAS;MACrC,IAAIsL,KAAK,GAAGxL,YAAY,CAACyL,SAAS,CAAC,UAAUC,KAAK,EAAE;QAClD,IAAI7I,GAAG,GAAG6I,KAAK,CAAC7I,GAAG;QACnB,OAAOA,GAAG,KAAK3C,SAAS;MAC1B,CAAC,CAAC;;MAEF;MACA,IAAIsL,KAAK,KAAK,CAAC,CAAC,IAAIL,MAAM,GAAG,CAAC,EAAE;QAC9BK,KAAK,GAAGxL,YAAY,CAACzB,MAAM;MAC7B;MACAiN,KAAK,GAAG,CAACA,KAAK,GAAGL,MAAM,GAAGnL,YAAY,CAACzB,MAAM,IAAIyB,YAAY,CAACzB,MAAM;MACpE,IAAIoN,IAAI,GAAG3L,YAAY,CAACwL,KAAK,CAAC;MAC9B,IAAIG,IAAI,EAAE;QACR,IAAIC,KAAK,GAAGD,IAAI,CAAC9I,GAAG;QACpBzE,KAAK,CAAC6L,cAAc,CAAC2B,KAAK,CAAC;MAC7B,CAAC,MAAM;QACLxN,KAAK,CAAC6L,cAAc,CAAC,IAAI,CAAC;MAC5B;IACF,CAAC,CAAC;IACF9N,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,WAAW,EAAE,UAAUmC,KAAK,EAAE;MAC3E,IAAIsL,aAAa,GAAGzN,KAAK,CAACsC,KAAK;QAC7BR,SAAS,GAAG2L,aAAa,CAAC3L,SAAS;QACnCb,YAAY,GAAGwM,aAAa,CAACxM,YAAY;QACzCJ,WAAW,GAAG4M,aAAa,CAAC5M,WAAW;QACvCoB,UAAU,GAAGwL,aAAa,CAACxL,UAAU;MACvC,IAAIyL,aAAa,GAAG1N,KAAK,CAACwC,KAAK;QAC7BmL,SAAS,GAAGD,aAAa,CAACC,SAAS;QACnCC,SAAS,GAAGF,aAAa,CAACE,SAAS;QACnCC,UAAU,GAAGH,aAAa,CAACG,UAAU;;MAEvC;MACA,QAAQ1L,KAAK,CAAC2L,KAAK;QACjB,KAAK7P,OAAO,CAAC8P,EAAE;UACb;YACE/N,KAAK,CAACgO,eAAe,CAAC,CAAC,CAAC,CAAC;YACzB7L,KAAK,CAACoJ,cAAc,CAAC,CAAC;YACtB;UACF;QACF,KAAKtN,OAAO,CAACgQ,IAAI;UACf;YACEjO,KAAK,CAACgO,eAAe,CAAC,CAAC,CAAC;YACxB7L,KAAK,CAACoJ,cAAc,CAAC,CAAC;YACtB;UACF;MACJ;;MAEA;MACA,IAAI2C,UAAU,GAAGlO,KAAK,CAACuG,aAAa,CAAC,CAAC;MACtC,IAAI2H,UAAU,IAAIA,UAAU,CAAC1H,IAAI,EAAE;QACjC,IAAI2H,qBAAqB,GAAGnO,KAAK,CAACqG,wBAAwB,CAAC,CAAC;QAC5D,IAAI+H,UAAU,GAAGF,UAAU,CAAC1H,IAAI,CAACW,MAAM,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC+G,UAAU,CAAC1H,IAAI,CAACvE,UAAU,CAAC6C,QAAQ,CAAC,IAAI,EAAE,EAAE3E,MAAM;QAC1G,IAAIsH,SAAS,GAAGnI,2BAA2B,CAAC9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkC,gBAAgB,CAACoC,SAAS,EAAEqM,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACnI3H,IAAI,EAAE0H,UAAU,CAAC1H,IAAI;UACrBF,MAAM,EAAE;QACV,CAAC,CAAC,CAAC;QACH,QAAQnE,KAAK,CAAC2L,KAAK;UACjB;UACA,KAAK7P,OAAO,CAACoQ,IAAI;YACf;cACE;cACA,IAAID,UAAU,IAAInN,YAAY,CAACmD,QAAQ,CAACtC,SAAS,CAAC,EAAE;gBAClD9B,KAAK,CAAC0H,YAAY,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC;cACnC,CAAC,MAAM,IAAIyG,UAAU,CAACI,MAAM,EAAE;gBAC5BtO,KAAK,CAAC6L,cAAc,CAACqC,UAAU,CAACI,MAAM,CAAC7J,GAAG,CAAC;cAC7C;cACAtC,KAAK,CAACoJ,cAAc,CAAC,CAAC;cACtB;YACF;UACF,KAAKtN,OAAO,CAACsQ,KAAK;YAChB;cACE;cACA,IAAIH,UAAU,IAAI,CAACnN,YAAY,CAACmD,QAAQ,CAACtC,SAAS,CAAC,EAAE;gBACnD9B,KAAK,CAAC0H,YAAY,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC;cACnC,CAAC,MAAM,IAAIyG,UAAU,CAACpJ,QAAQ,IAAIoJ,UAAU,CAACpJ,QAAQ,CAAC3E,MAAM,EAAE;gBAC5DH,KAAK,CAAC6L,cAAc,CAACqC,UAAU,CAACpJ,QAAQ,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC;cAClD;cACAtC,KAAK,CAACoJ,cAAc,CAAC,CAAC;cACtB;YACF;;UAEF;UACA,KAAKtN,OAAO,CAACuQ,KAAK;UAClB,KAAKvQ,OAAO,CAACwQ,KAAK;YAChB;cACE,IAAIb,SAAS,IAAI,CAACnG,SAAS,CAACiH,QAAQ,IAAIjH,SAAS,CAACmG,SAAS,KAAK,KAAK,IAAI,CAACnG,SAAS,CAACkH,eAAe,EAAE;gBACnG3O,KAAK,CAAC4O,WAAW,CAAC,CAAC,CAAC,EAAEnH,SAAS,EAAE,CAAC5G,WAAW,CAACuD,QAAQ,CAACtC,SAAS,CAAC,CAAC;cACpE,CAAC,MAAM,IAAI,CAAC8L,SAAS,IAAIC,UAAU,IAAI,CAACpG,SAAS,CAACiH,QAAQ,IAAIjH,SAAS,CAACoG,UAAU,KAAK,KAAK,EAAE;gBAC5F7N,KAAK,CAAC6O,YAAY,CAAC,CAAC,CAAC,EAAEpH,SAAS,CAAC;cACnC;cACA;YACF;QACJ;MACF;MACAkG,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAACxL,KAAK,CAAC;IAChE,CAAC,CAAC;IACF;AACJ;AACA;IACIpE,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,sBAAsB,EAAE,UAAUsC,KAAK,EAAE;MACtF,IAAIwM,MAAM,GAAG5O,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK+F,SAAS,GAAG/F,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MACtF,IAAI6O,UAAU,GAAG7O,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK+F,SAAS,GAAG/F,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;MACzF,IAAI,CAACF,KAAK,CAACgP,SAAS,EAAE;QACpB,IAAIC,QAAQ,GAAG,KAAK;QACpB,IAAIC,SAAS,GAAG,IAAI;QACpB,IAAIC,QAAQ,GAAG,CAAC,CAAC;QACjB7K,MAAM,CAACC,IAAI,CAACjC,KAAK,CAAC,CAACkC,OAAO,CAAC,UAAU4K,IAAI,EAAE;UACzC,IAAIpP,KAAK,CAACwC,KAAK,CAACuC,cAAc,CAACqK,IAAI,CAAC,EAAE;YACpCF,SAAS,GAAG,KAAK;YACjB;UACF;UACAD,QAAQ,GAAG,IAAI;UACfE,QAAQ,CAACC,IAAI,CAAC,GAAG9M,KAAK,CAAC8M,IAAI,CAAC;QAC9B,CAAC,CAAC;QACF,IAAIH,QAAQ,KAAK,CAACH,MAAM,IAAII,SAAS,CAAC,EAAE;UACtClP,KAAK,CAACiD,QAAQ,CAACzF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2R,QAAQ,CAAC,EAAEJ,UAAU,CAAC,CAAC;QACxE;MACF;IACF,CAAC,CAAC;IACFhR,eAAe,CAACH,sBAAsB,CAACoC,KAAK,CAAC,EAAE,UAAU,EAAE,UAAUqP,MAAM,EAAE;MAC3ErP,KAAK,CAACkD,OAAO,CAACC,OAAO,CAAC2J,QAAQ,CAACuC,MAAM,CAAC;IACxC,CAAC,CAAC;IACF,OAAOrP,KAAK;EACd;EACArC,YAAY,CAACkC,IAAI,EAAE,CAAC;IAClB4E,GAAG,EAAE,mBAAmB;IACxB6K,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACP,SAAS,GAAG,KAAK;MACtB,IAAI,CAACQ,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACD/K,GAAG,EAAE,oBAAoB;IACzB6K,KAAK,EAAE,SAASG,kBAAkBA,CAAA,EAAG;MACnC,IAAI,CAACD,SAAS,CAAC,CAAC;IAClB;EACF,CAAC,EAAE;IACD/K,GAAG,EAAE,WAAW;IAChB6K,KAAK,EAAE,SAASE,SAASA,CAAA,EAAG;MAC1B,IAAIE,aAAa,GAAG,IAAI,CAAClN,KAAK;QAC5BV,SAAS,GAAG4N,aAAa,CAAC5N,SAAS;QACnC6N,qBAAqB,GAAGD,aAAa,CAAC7C,gBAAgB;QACtDA,gBAAgB,GAAG8C,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;MACjF,IAAI7N,SAAS,KAAKmE,SAAS,IAAInE,SAAS,KAAK,IAAI,CAACQ,KAAK,CAACR,SAAS,EAAE;QACjE,IAAI,CAACmB,QAAQ,CAAC;UACZnB,SAAS,EAAEA;QACb,CAAC,CAAC;QACF,IAAIA,SAAS,KAAK,IAAI,EAAE;UACtB,IAAI,CAACgL,QAAQ,CAAC;YACZrI,GAAG,EAAE3C,SAAS;YACdiL,MAAM,EAAEF;UACV,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EAAE;IACDpI,GAAG,EAAE,sBAAsB;IAC3B6K,KAAK,EAAE,SAASM,oBAAoBA,CAAA,EAAG;MACrCtM,MAAM,CAACqC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACnC,eAAe,CAAC;MAC3D,IAAI,CAACwL,SAAS,GAAG,IAAI;IACvB;EACF,CAAC,EAAE;IACDvK,GAAG,EAAE,gBAAgB;IACrB6K,KAAK,EAAE,SAASpL,cAAcA,CAAA,EAAG;MAC/B,IAAI,CAACjB,QAAQ,CAAC;QACZvB,eAAe,EAAE,IAAI;QACrBL,YAAY,EAAE,IAAI;QAClBE,eAAe,EAAE,IAAI;QACrBH,aAAa,EAAE,IAAI;QACnBE,gBAAgB,EAAE,IAAI;QACtBE,aAAa,EAAE,IAAI;QACnBC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDgD,GAAG,EAAE,QAAQ;IACb6K,KAAK,EAAE,SAASO,MAAMA,CAAA,EAAG;MACvB,IAAIC,aAAa,GAAG,IAAI,CAACxN,KAAK;QAC5BT,OAAO,GAAGiO,aAAa,CAACjO,OAAO;QAC/BD,YAAY,GAAGkO,aAAa,CAAClO,YAAY;QACzClB,WAAW,GAAGoP,aAAa,CAACpP,WAAW;QACvCQ,eAAe,GAAG4O,aAAa,CAAC5O,eAAe;QAC/CY,SAAS,GAAGgO,aAAa,CAAChO,SAAS;QACnCP,eAAe,GAAGuO,aAAa,CAACvO,eAAe;QAC/CD,gBAAgB,GAAGwO,aAAa,CAACxO,gBAAgB;QACjDF,aAAa,GAAG0O,aAAa,CAAC1O,aAAa;QAC3CC,YAAY,GAAGyO,aAAa,CAACzO,YAAY;QACzCK,eAAe,GAAGoO,aAAa,CAACpO,eAAe;QAC/Cf,MAAM,GAAGmP,aAAa,CAACnP,MAAM;MAC/B,IAAIoP,aAAa,GAAG,IAAI,CAACvN,KAAK;QAC5BwN,SAAS,GAAGD,aAAa,CAACC,SAAS;QACnCC,SAAS,GAAGF,aAAa,CAACE,SAAS;QACnCC,KAAK,GAAGH,aAAa,CAACG,KAAK;QAC3BC,QAAQ,GAAGJ,aAAa,CAACI,QAAQ;QACjCC,SAAS,GAAGL,aAAa,CAACK,SAAS;QACnCC,qBAAqB,GAAGN,aAAa,CAACO,QAAQ;QAC9CA,QAAQ,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;QACvExC,UAAU,GAAGkC,aAAa,CAAClC,UAAU;QACrC0C,QAAQ,GAAGR,aAAa,CAACQ,QAAQ;QACjCC,IAAI,GAAGT,aAAa,CAACS,IAAI;QACzBC,YAAY,GAAGV,aAAa,CAACU,YAAY;QACzCC,SAAS,GAAGX,aAAa,CAACW,SAAS;QACnC9C,SAAS,GAAGmC,aAAa,CAACnC,SAAS;QACnC3E,aAAa,GAAG8G,aAAa,CAAC9G,aAAa;QAC3CyF,QAAQ,GAAGqB,aAAa,CAACrB,QAAQ;QACjCiC,MAAM,GAAGZ,aAAa,CAACY,MAAM;QAC7BhG,QAAQ,GAAGoF,aAAa,CAACpF,QAAQ;QACjCiG,cAAc,GAAGb,aAAa,CAACa,cAAc;QAC7CC,MAAM,GAAGd,aAAa,CAACc,MAAM;QAC7BC,UAAU,GAAGf,aAAa,CAACe,UAAU;QACrCC,WAAW,GAAGhB,aAAa,CAACgB,WAAW;QACvCC,OAAO,GAAGjB,aAAa,CAACiB,OAAO;QAC/BC,WAAW,GAAGlB,aAAa,CAACkB,WAAW;QACvCC,mBAAmB,GAAGnB,aAAa,CAACmB,mBAAmB;QACvDC,aAAa,GAAGpB,aAAa,CAACoB,aAAa;QAC3CC,QAAQ,GAAGrB,aAAa,CAACqB,QAAQ;QACjCrN,SAAS,GAAGgM,aAAa,CAAChM,SAAS;QACnCsN,aAAa,GAAGtB,aAAa,CAACsB,aAAa;QAC3CC,SAAS,GAAGvB,aAAa,CAACuB,SAAS;MACrC,IAAIC,QAAQ,GAAGrT,SAAS,CAAC,IAAI,CAACsE,KAAK,EAAE;QACnCgP,IAAI,EAAE,IAAI;QACVhL,IAAI,EAAE;MACR,CAAC,CAAC;;MAEF;MACA,IAAIiL,eAAe;MACnB,IAAIf,SAAS,EAAE;QACb,IAAInT,OAAO,CAACmT,SAAS,CAAC,KAAK,QAAQ,EAAE;UACnCe,eAAe,GAAGf,SAAS;QAC7B,CAAC,MAAM,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;UAC1Ce,eAAe,GAAG;YAChBC,aAAa,EAAEhB;UACjB,CAAC;QACH,CAAC,MAAM;UACLe,eAAe,GAAG,CAAC,CAAC;QACtB;MACF;MACA,IAAIE,YAAY,GAAG;QACjB3B,SAAS,EAAEA,SAAS;QACpBnC,UAAU,EAAEA,UAAU;QACtB0C,QAAQ,EAAEA,QAAQ;QAClBC,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY;QAC1BC,SAAS,EAAEe,eAAe;QAC1BvQ,eAAe,EAAEA,eAAe;QAChC0M,SAAS,EAAEA,SAAS;QACpB3E,aAAa,EAAEA,aAAa;QAC5ByF,QAAQ,EAAEA,QAAQ;QAClBhO,WAAW,EAAEA,WAAW;QACxBa,eAAe,EAAEA,eAAe;QAChCD,gBAAgB,EAAEA,gBAAgB;QAClCF,aAAa,EAAEA,aAAa;QAC5BC,YAAY,EAAEA,YAAY;QAC1BK,eAAe,EAAEA,eAAe;QAChCf,MAAM,EAAEA,MAAM;QACdoD,SAAS,EAAEA,SAAS;QACpBmN,mBAAmB,EAAEA,mBAAmB;QACxCvG,QAAQ,EAAEA,QAAQ;QAClBiG,cAAc,EAAEA,cAAc;QAC9BK,WAAW,EAAEA,WAAW;QACxBW,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;QACzCnK,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BmH,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BD,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BtC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BwF,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;QACvCC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;QACvCC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;QACzCC,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCC,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCC,eAAe,EAAE,IAAI,CAACA,eAAe;QACrC1M,aAAa,EAAE,IAAI,CAACA,aAAa;QACjC2M,UAAU,EAAE,IAAI,CAACA;MACnB,CAAC;MACD,OAAO,aAAajU,KAAK,CAACkU,aAAa,CAACjU,WAAW,CAACkU,QAAQ,EAAE;QAC5DjD,KAAK,EAAEqC;MACT,CAAC,EAAE,aAAavT,KAAK,CAACkU,aAAa,CAAC,KAAK,EAAE;QACzCrC,SAAS,EAAEjS,UAAU,CAACgS,SAAS,EAAEC,SAAS,EAAEoB,aAAa,EAAEtT,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC0C,MAAM,CAACuP,SAAS,EAAE,YAAY,CAAC,EAAEG,QAAQ,CAAC,EAAE,EAAE,CAAC1P,MAAM,CAACuP,SAAS,EAAE,UAAU,CAAC,EAAEnO,OAAO,CAAC,EAAE,EAAE,CAACpB,MAAM,CAACuP,SAAS,EAAE,iBAAiB,CAAC,EAAElO,SAAS,KAAK,IAAI,CAAC,CAAC;QACvQoO,KAAK,EAAEoB;MACT,CAAC,EAAE,aAAalT,KAAK,CAACkU,aAAa,CAAC/T,QAAQ,EAAEjB,QAAQ,CAAC;QACrDkV,GAAG,EAAE,IAAI,CAACtP,OAAO;QACjB8M,SAAS,EAAEA,SAAS;QACpBE,KAAK,EAAEA,KAAK;QACZ1J,IAAI,EAAE5E,YAAY;QAClB8M,QAAQ,EAAEA,QAAQ;QAClBb,UAAU,EAAEA,UAAU;QACtBD,SAAS,EAAE,CAAC,CAACA,SAAS;QACtB+C,MAAM,EAAEA,MAAM;QACd8B,QAAQ,EAAEvR,eAAe,KAAK,IAAI;QAClC2P,MAAM,EAAEA,MAAM;QACdC,UAAU,EAAEA,UAAU;QACtBE,OAAO,EAAEA,OAAO;QAChBZ,SAAS,EAAEA,SAAS;QACpBvO,OAAO,EAAEA,OAAO;QAChByO,QAAQ,EAAEA,QAAQ;QAClBpC,UAAU,EAAE,IAAI,CAAC3H,aAAa,CAAC,CAAC;QAChCiF,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBI,MAAM,EAAE,IAAI,CAACA,MAAM;QACnB+B,SAAS,EAAE,IAAI,CAACA,SAAS;QACzB9B,cAAc,EAAE,IAAI,CAACA,cAAc;QACnC6G,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;QACzCC,eAAe,EAAE,IAAI,CAACA,eAAe;QACrCxB,aAAa,EAAEA,aAAa;QAC5BC,QAAQ,EAAEA,QAAQ;QAClBL,WAAW,EAAEA;MACf,CAAC,EAAE,IAAI,CAAC1K,wBAAwB,CAAC,CAAC,EAAEkL,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClD;EACF,CAAC,CAAC,EAAE,CAAC;IACH9M,GAAG,EAAE,0BAA0B;IAC/B6K,KAAK,EAAE,SAASsD,wBAAwBA,CAACpQ,KAAK,EAAEyI,SAAS,EAAE;MACzD,IAAIjJ,SAAS,GAAGiJ,SAAS,CAACjJ,SAAS;MACnC,IAAImN,QAAQ,GAAG;QACbnN,SAAS,EAAEQ;MACb,CAAC;MACD,SAASyM,QAAQA,CAACG,IAAI,EAAE;QACtB,OAAO,CAACpN,SAAS,IAAIQ,KAAK,CAACuC,cAAc,CAACqK,IAAI,CAAC,IAAIpN,SAAS,IAAIA,SAAS,CAACoN,IAAI,CAAC,KAAK5M,KAAK,CAAC4M,IAAI,CAAC;MACjG;;MAEA;MACA,IAAIzN,QAAQ;;MAEZ;MACA,IAAIM,UAAU,GAAGgJ,SAAS,CAAChJ,UAAU;MACrC,IAAIgN,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC1BhN,UAAU,GAAGzC,cAAc,CAACgD,KAAK,CAACP,UAAU,CAAC;QAC7CkN,QAAQ,CAAClN,UAAU,GAAGA,UAAU;MAClC;;MAEA;MACA,IAAIgN,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxBtN,QAAQ,GAAGa,KAAK,CAACb,QAAQ;MAC3B,CAAC,MAAM,IAAIsN,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC/B9Q,OAAO,CAAC,KAAK,EAAE,kEAAkE,CAAC;QAClFwD,QAAQ,GAAGpC,iBAAiB,CAACiD,KAAK,CAACsC,QAAQ,CAAC;MAC9C;;MAEA;MACA,IAAInD,QAAQ,EAAE;QACZwN,QAAQ,CAACxN,QAAQ,GAAGA,QAAQ;QAC5B,IAAIkR,WAAW,GAAGxT,qBAAqB,CAACsC,QAAQ,EAAE;UAChDM,UAAU,EAAEA;QACd,CAAC,CAAC;QACFkN,QAAQ,CAACzO,WAAW,GAAGlD,aAAa,CAACO,eAAe,CAAC,CAAC,CAAC,EAAES,UAAU,EAAEC,YAAY,CAAC,EAAEoU,WAAW,CAACnS,WAAW,CAAC;;QAE5G;QACA,IAAIoS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCrT,iBAAiB,CAACgC,QAAQ,EAAEM,UAAU,CAAC;QACzC;MACF;MACA,IAAIvB,WAAW,GAAGyO,QAAQ,CAACzO,WAAW,IAAIuK,SAAS,CAACvK,WAAW;;MAE/D;MACA,IAAIuO,QAAQ,CAAC,cAAc,CAAC,IAAIjN,SAAS,IAAIiN,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QACzEE,QAAQ,CAAClO,YAAY,GAAGuB,KAAK,CAACyQ,gBAAgB,IAAI,CAACjR,SAAS,IAAIQ,KAAK,CAAC0Q,mBAAmB,GAAGnU,mBAAmB,CAACyD,KAAK,CAACvB,YAAY,EAAEP,WAAW,CAAC,GAAG8B,KAAK,CAACvB,YAAY;MACvK,CAAC,MAAM,IAAI,CAACe,SAAS,IAAIQ,KAAK,CAAC2Q,gBAAgB,EAAE;QAC/C,IAAIC,gBAAgB,GAAG5V,aAAa,CAAC,CAAC,CAAC,EAAEkD,WAAW,CAAC;QACrD,OAAO0S,gBAAgB,CAAC5U,UAAU,CAAC;;QAEnC;QACA,IAAI6U,gBAAgB,GAAG,EAAE;QACzB/O,MAAM,CAACC,IAAI,CAAC6O,gBAAgB,CAAC,CAAC5O,OAAO,CAAC,UAAUC,GAAG,EAAE;UACnD,IAAII,MAAM,GAAGuO,gBAAgB,CAAC3O,GAAG,CAAC;UAClC,IAAII,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAAC3E,MAAM,EAAE;YAC7CkT,gBAAgB,CAACpJ,IAAI,CAACpF,MAAM,CAACJ,GAAG,CAAC;UACnC;QACF,CAAC,CAAC;QACF0K,QAAQ,CAAClO,YAAY,GAAGoS,gBAAgB;MAC1C,CAAC,MAAM,IAAI,CAACrR,SAAS,IAAIQ,KAAK,CAAC8Q,mBAAmB,EAAE;QAClDnE,QAAQ,CAAClO,YAAY,GAAGuB,KAAK,CAACyQ,gBAAgB,IAAIzQ,KAAK,CAAC0Q,mBAAmB,GAAGnU,mBAAmB,CAACyD,KAAK,CAAC8Q,mBAAmB,EAAE5S,WAAW,CAAC,GAAG8B,KAAK,CAAC8Q,mBAAmB;MACvK;MACA,IAAI,CAACnE,QAAQ,CAAClO,YAAY,EAAE;QAC1B,OAAOkO,QAAQ,CAAClO,YAAY;MAC9B;;MAEA;MACA,IAAIU,QAAQ,IAAIwN,QAAQ,CAAClO,YAAY,EAAE;QACrC,IAAIW,YAAY,GAAGnC,eAAe,CAACkC,QAAQ,IAAIsJ,SAAS,CAACtJ,QAAQ,EAAEwN,QAAQ,CAAClO,YAAY,IAAIgK,SAAS,CAAChK,YAAY,EAAEgB,UAAU,CAAC;QAC/HkN,QAAQ,CAACvN,YAAY,GAAGA,YAAY;MACtC;;MAEA;MACA,IAAIY,KAAK,CAACqL,UAAU,EAAE;QACpB,IAAIoB,QAAQ,CAAC,cAAc,CAAC,EAAE;UAC5BE,QAAQ,CAACvO,YAAY,GAAG9B,gBAAgB,CAAC0D,KAAK,CAAC5B,YAAY,EAAE4B,KAAK,CAAC;QACrE,CAAC,MAAM,IAAI,CAACR,SAAS,IAAIQ,KAAK,CAAC+Q,mBAAmB,EAAE;UAClDpE,QAAQ,CAACvO,YAAY,GAAG9B,gBAAgB,CAAC0D,KAAK,CAAC+Q,mBAAmB,EAAE/Q,KAAK,CAAC;QAC5E;MACF;;MAEA;MACA,IAAIA,KAAK,CAACoL,SAAS,EAAE;QACnB,IAAI4F,gBAAgB;QACpB,IAAIvE,QAAQ,CAAC,aAAa,CAAC,EAAE;UAC3BuE,gBAAgB,GAAGvU,gBAAgB,CAACuD,KAAK,CAAC3B,WAAW,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC,MAAM,IAAI,CAACmB,SAAS,IAAIQ,KAAK,CAACiR,kBAAkB,EAAE;UACjDD,gBAAgB,GAAGvU,gBAAgB,CAACuD,KAAK,CAACiR,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC,MAAM,IAAI9R,QAAQ,EAAE;UACnB;UACA6R,gBAAgB,GAAGvU,gBAAgB,CAACuD,KAAK,CAAC3B,WAAW,CAAC,IAAI;YACxDA,WAAW,EAAEoK,SAAS,CAACpK,WAAW;YAClCC,eAAe,EAAEmK,SAAS,CAACnK;UAC7B,CAAC;QACH;QACA,IAAI0S,gBAAgB,EAAE;UACpB,IAAIE,iBAAiB,GAAGF,gBAAgB;YACtCG,qBAAqB,GAAGD,iBAAiB,CAAC7S,WAAW;YACrDA,WAAW,GAAG8S,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;YAC3EC,qBAAqB,GAAGF,iBAAiB,CAAC5S,eAAe;YACzDA,eAAe,GAAG8S,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;UACjF,IAAI,CAACpR,KAAK,CAACyG,aAAa,EAAE;YACxB,IAAI4K,WAAW,GAAG1U,YAAY,CAAC0B,WAAW,EAAE,IAAI,EAAEH,WAAW,CAAC;YAC9DG,WAAW,GAAGgT,WAAW,CAAChT,WAAW;YACrCC,eAAe,GAAG+S,WAAW,CAAC/S,eAAe;UAC/C;UACAqO,QAAQ,CAACtO,WAAW,GAAGA,WAAW;UAClCsO,QAAQ,CAACrO,eAAe,GAAGA,eAAe;QAC5C;MACF;;MAEA;MACA,IAAImO,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC1BE,QAAQ,CAACpO,UAAU,GAAGyB,KAAK,CAACzB,UAAU;MACxC;MACA,OAAOoO,QAAQ;IACjB;EACF,CAAC,CAAC,CAAC;EACH,OAAOtP,IAAI;AACb,CAAC,CAACzB,KAAK,CAAC0V,SAAS,CAAC;AAClB/V,eAAe,CAAC8B,IAAI,EAAE,cAAc,EAAE;EACpCmQ,SAAS,EAAE,SAAS;EACpBG,QAAQ,EAAE,KAAK;EACfI,QAAQ,EAAE,IAAI;EACd1C,UAAU,EAAE,IAAI;EAChBzF,QAAQ,EAAE,KAAK;EACfwF,SAAS,EAAE,KAAK;EAChBc,QAAQ,EAAE,KAAK;EACfzF,aAAa,EAAE,KAAK;EACpByH,SAAS,EAAE,KAAK;EAChBwC,mBAAmB,EAAE,IAAI;EACzBD,gBAAgB,EAAE,KAAK;EACvBE,gBAAgB,EAAE,KAAK;EACvBG,mBAAmB,EAAE,EAAE;EACvBG,kBAAkB,EAAE,EAAE;EACtBF,mBAAmB,EAAE,EAAE;EACvBrC,mBAAmB,EAAE5S,aAAa;EAClCwF,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;IAC9B,OAAO,IAAI;EACb,CAAC;EACD+D,YAAY,EAAE;AAChB,CAAC,CAAC;AACF9J,eAAe,CAAC8B,IAAI,EAAE,UAAU,EAAEnB,QAAQ,CAAC;AAC3C,eAAemB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}