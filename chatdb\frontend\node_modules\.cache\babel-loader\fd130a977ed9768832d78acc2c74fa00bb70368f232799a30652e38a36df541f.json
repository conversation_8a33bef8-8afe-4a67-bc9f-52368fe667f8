{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nexport function file2Obj(file) {\n  return Object.assign(Object.assign({}, file), {\n    lastModified: file.lastModified,\n    lastModifiedDate: file.lastModifiedDate,\n    name: file.name,\n    size: file.size,\n    type: file.type,\n    uid: file.uid,\n    percent: 0,\n    originFileObj: file\n  });\n}\n/** Upload fileList. Replace file if exist or just push into it. */\nexport function updateFileList(file, fileList) {\n  const nextFileList = _toConsumableArray(fileList);\n  const fileIndex = nextFileList.findIndex(_ref => {\n    let {\n      uid\n    } = _ref;\n    return uid === file.uid;\n  });\n  if (fileIndex === -1) {\n    nextFileList.push(file);\n  } else {\n    nextFileList[fileIndex] = file;\n  }\n  return nextFileList;\n}\nexport function getFileItem(file, fileList) {\n  const matchKey = file.uid !== undefined ? 'uid' : 'name';\n  return fileList.filter(item => item[matchKey] === file[matchKey])[0];\n}\nexport function removeFileItem(file, fileList) {\n  const matchKey = file.uid !== undefined ? 'uid' : 'name';\n  const removed = fileList.filter(item => item[matchKey] !== file[matchKey]);\n  if (removed.length === fileList.length) {\n    return null;\n  }\n  return removed;\n}\n// ==================== Default Image Preview ====================\nconst extname = function () {\n  let url = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  const temp = url.split('/');\n  const filename = temp[temp.length - 1];\n  const filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n  return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n};\nconst isImageFileType = type => type.indexOf('image/') === 0;\nexport const isImageUrl = file => {\n  if (file.type && !file.thumbUrl) {\n    return isImageFileType(file.type);\n  }\n  const url = file.thumbUrl || file.url || '';\n  const extension = extname(url);\n  if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(extension)) {\n    return true;\n  }\n  if (/^data:/.test(url)) {\n    // other file types of base64\n    return false;\n  }\n  if (extension) {\n    // other file types which have extension\n    return false;\n  }\n  return true;\n};\nconst MEASURE_SIZE = 200;\nexport function previewImage(file) {\n  return new Promise(resolve => {\n    if (!file.type || !isImageFileType(file.type)) {\n      resolve('');\n      return;\n    }\n    const canvas = document.createElement('canvas');\n    canvas.width = MEASURE_SIZE;\n    canvas.height = MEASURE_SIZE;\n    canvas.style.cssText = `position: fixed; left: 0; top: 0; width: ${MEASURE_SIZE}px; height: ${MEASURE_SIZE}px; z-index: 9999; display: none;`;\n    document.body.appendChild(canvas);\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    img.onload = () => {\n      const {\n        width,\n        height\n      } = img;\n      let drawWidth = MEASURE_SIZE;\n      let drawHeight = MEASURE_SIZE;\n      let offsetX = 0;\n      let offsetY = 0;\n      if (width > height) {\n        drawHeight = height * (MEASURE_SIZE / width);\n        offsetY = -(drawHeight - drawWidth) / 2;\n      } else {\n        drawWidth = width * (MEASURE_SIZE / height);\n        offsetX = -(drawWidth - drawHeight) / 2;\n      }\n      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n      const dataURL = canvas.toDataURL();\n      document.body.removeChild(canvas);\n      window.URL.revokeObjectURL(img.src);\n      resolve(dataURL);\n    };\n    img.crossOrigin = 'anonymous';\n    if (file.type.startsWith('image/svg+xml')) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (reader.result && typeof reader.result === 'string') {\n          img.src = reader.result;\n        }\n      };\n      reader.readAsDataURL(file);\n    } else if (file.type.startsWith('image/gif')) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (reader.result) {\n          resolve(reader.result);\n        }\n      };\n      reader.readAsDataURL(file);\n    } else {\n      img.src = window.URL.createObjectURL(file);\n    }\n  });\n}", "map": {"version": 3, "names": ["_toConsumableArray", "file2Obj", "file", "Object", "assign", "lastModified", "lastModifiedDate", "name", "size", "type", "uid", "percent", "originFileObj", "updateFileList", "fileList", "nextFileList", "fileIndex", "findIndex", "_ref", "push", "getFileItem", "matchKey", "undefined", "filter", "item", "removeFileItem", "removed", "length", "extname", "url", "arguments", "temp", "split", "filename", "filenameWithoutSuffix", "exec", "isImageFileType", "indexOf", "isImageUrl", "thumbUrl", "extension", "test", "MEASURE_SIZE", "previewImage", "Promise", "resolve", "canvas", "document", "createElement", "width", "height", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "ctx", "getContext", "img", "Image", "onload", "drawWidth", "drawHeight", "offsetX", "offsetY", "drawImage", "dataURL", "toDataURL", "<PERSON><PERSON><PERSON><PERSON>", "window", "URL", "revokeObjectURL", "src", "crossOrigin", "startsWith", "reader", "FileReader", "result", "readAsDataURL", "createObjectURL"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/antd/es/upload/utils.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nexport function file2Obj(file) {\n  return Object.assign(Object.assign({}, file), {\n    lastModified: file.lastModified,\n    lastModifiedDate: file.lastModifiedDate,\n    name: file.name,\n    size: file.size,\n    type: file.type,\n    uid: file.uid,\n    percent: 0,\n    originFileObj: file\n  });\n}\n/** Upload fileList. Replace file if exist or just push into it. */\nexport function updateFileList(file, fileList) {\n  const nextFileList = _toConsumableArray(fileList);\n  const fileIndex = nextFileList.findIndex(_ref => {\n    let {\n      uid\n    } = _ref;\n    return uid === file.uid;\n  });\n  if (fileIndex === -1) {\n    nextFileList.push(file);\n  } else {\n    nextFileList[fileIndex] = file;\n  }\n  return nextFileList;\n}\nexport function getFileItem(file, fileList) {\n  const matchKey = file.uid !== undefined ? 'uid' : 'name';\n  return fileList.filter(item => item[matchKey] === file[matchKey])[0];\n}\nexport function removeFileItem(file, fileList) {\n  const matchKey = file.uid !== undefined ? 'uid' : 'name';\n  const removed = fileList.filter(item => item[matchKey] !== file[matchKey]);\n  if (removed.length === fileList.length) {\n    return null;\n  }\n  return removed;\n}\n// ==================== Default Image Preview ====================\nconst extname = function () {\n  let url = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  const temp = url.split('/');\n  const filename = temp[temp.length - 1];\n  const filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n  return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n};\nconst isImageFileType = type => type.indexOf('image/') === 0;\nexport const isImageUrl = file => {\n  if (file.type && !file.thumbUrl) {\n    return isImageFileType(file.type);\n  }\n  const url = file.thumbUrl || file.url || '';\n  const extension = extname(url);\n  if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg|ico|heic|heif)$/i.test(extension)) {\n    return true;\n  }\n  if (/^data:/.test(url)) {\n    // other file types of base64\n    return false;\n  }\n  if (extension) {\n    // other file types which have extension\n    return false;\n  }\n  return true;\n};\nconst MEASURE_SIZE = 200;\nexport function previewImage(file) {\n  return new Promise(resolve => {\n    if (!file.type || !isImageFileType(file.type)) {\n      resolve('');\n      return;\n    }\n    const canvas = document.createElement('canvas');\n    canvas.width = MEASURE_SIZE;\n    canvas.height = MEASURE_SIZE;\n    canvas.style.cssText = `position: fixed; left: 0; top: 0; width: ${MEASURE_SIZE}px; height: ${MEASURE_SIZE}px; z-index: 9999; display: none;`;\n    document.body.appendChild(canvas);\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    img.onload = () => {\n      const {\n        width,\n        height\n      } = img;\n      let drawWidth = MEASURE_SIZE;\n      let drawHeight = MEASURE_SIZE;\n      let offsetX = 0;\n      let offsetY = 0;\n      if (width > height) {\n        drawHeight = height * (MEASURE_SIZE / width);\n        offsetY = -(drawHeight - drawWidth) / 2;\n      } else {\n        drawWidth = width * (MEASURE_SIZE / height);\n        offsetX = -(drawWidth - drawHeight) / 2;\n      }\n      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n      const dataURL = canvas.toDataURL();\n      document.body.removeChild(canvas);\n      window.URL.revokeObjectURL(img.src);\n      resolve(dataURL);\n    };\n    img.crossOrigin = 'anonymous';\n    if (file.type.startsWith('image/svg+xml')) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (reader.result && typeof reader.result === 'string') {\n          img.src = reader.result;\n        }\n      };\n      reader.readAsDataURL(file);\n    } else if (file.type.startsWith('image/gif')) {\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (reader.result) {\n          resolve(reader.result);\n        }\n      };\n      reader.readAsDataURL(file);\n    } else {\n      img.src = window.URL.createObjectURL(file);\n    }\n  });\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAOC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,IAAI,CAAC,EAAE;IAC5CG,YAAY,EAAEH,IAAI,CAACG,YAAY;IAC/BC,gBAAgB,EAAEJ,IAAI,CAACI,gBAAgB;IACvCC,IAAI,EAAEL,IAAI,CAACK,IAAI;IACfC,IAAI,EAAEN,IAAI,CAACM,IAAI;IACfC,IAAI,EAAEP,IAAI,CAACO,IAAI;IACfC,GAAG,EAAER,IAAI,CAACQ,GAAG;IACbC,OAAO,EAAE,CAAC;IACVC,aAAa,EAAEV;EACjB,CAAC,CAAC;AACJ;AACA;AACA,OAAO,SAASW,cAAcA,CAACX,IAAI,EAAEY,QAAQ,EAAE;EAC7C,MAAMC,YAAY,GAAGf,kBAAkB,CAACc,QAAQ,CAAC;EACjD,MAAME,SAAS,GAAGD,YAAY,CAACE,SAAS,CAACC,IAAI,IAAI;IAC/C,IAAI;MACFR;IACF,CAAC,GAAGQ,IAAI;IACR,OAAOR,GAAG,KAAKR,IAAI,CAACQ,GAAG;EACzB,CAAC,CAAC;EACF,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;IACpBD,YAAY,CAACI,IAAI,CAACjB,IAAI,CAAC;EACzB,CAAC,MAAM;IACLa,YAAY,CAACC,SAAS,CAAC,GAAGd,IAAI;EAChC;EACA,OAAOa,YAAY;AACrB;AACA,OAAO,SAASK,WAAWA,CAAClB,IAAI,EAAEY,QAAQ,EAAE;EAC1C,MAAMO,QAAQ,GAAGnB,IAAI,CAACQ,GAAG,KAAKY,SAAS,GAAG,KAAK,GAAG,MAAM;EACxD,OAAOR,QAAQ,CAACS,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACH,QAAQ,CAAC,KAAKnB,IAAI,CAACmB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE;AACA,OAAO,SAASI,cAAcA,CAACvB,IAAI,EAAEY,QAAQ,EAAE;EAC7C,MAAMO,QAAQ,GAAGnB,IAAI,CAACQ,GAAG,KAAKY,SAAS,GAAG,KAAK,GAAG,MAAM;EACxD,MAAMI,OAAO,GAAGZ,QAAQ,CAACS,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACH,QAAQ,CAAC,KAAKnB,IAAI,CAACmB,QAAQ,CAAC,CAAC;EAC1E,IAAIK,OAAO,CAACC,MAAM,KAAKb,QAAQ,CAACa,MAAM,EAAE;IACtC,OAAO,IAAI;EACb;EACA,OAAOD,OAAO;AAChB;AACA;AACA,MAAME,OAAO,GAAG,SAAAA,CAAA,EAAY;EAC1B,IAAIC,GAAG,GAAGC,SAAS,CAACH,MAAM,GAAG,CAAC,IAAIG,SAAS,CAAC,CAAC,CAAC,KAAKR,SAAS,GAAGQ,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAChF,MAAMC,IAAI,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;EAC3B,MAAMC,QAAQ,GAAGF,IAAI,CAACA,IAAI,CAACJ,MAAM,GAAG,CAAC,CAAC;EACtC,MAAMO,qBAAqB,GAAGD,QAAQ,CAACD,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EACvD,OAAO,CAAC,aAAa,CAACG,IAAI,CAACD,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/D,CAAC;AACD,MAAME,eAAe,GAAG3B,IAAI,IAAIA,IAAI,CAAC4B,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC5D,OAAO,MAAMC,UAAU,GAAGpC,IAAI,IAAI;EAChC,IAAIA,IAAI,CAACO,IAAI,IAAI,CAACP,IAAI,CAACqC,QAAQ,EAAE;IAC/B,OAAOH,eAAe,CAAClC,IAAI,CAACO,IAAI,CAAC;EACnC;EACA,MAAMoB,GAAG,GAAG3B,IAAI,CAACqC,QAAQ,IAAIrC,IAAI,CAAC2B,GAAG,IAAI,EAAE;EAC3C,MAAMW,SAAS,GAAGZ,OAAO,CAACC,GAAG,CAAC;EAC9B,IAAI,eAAe,CAACY,IAAI,CAACZ,GAAG,CAAC,IAAI,0DAA0D,CAACY,IAAI,CAACD,SAAS,CAAC,EAAE;IAC3G,OAAO,IAAI;EACb;EACA,IAAI,QAAQ,CAACC,IAAI,CAACZ,GAAG,CAAC,EAAE;IACtB;IACA,OAAO,KAAK;EACd;EACA,IAAIW,SAAS,EAAE;IACb;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;AACD,MAAME,YAAY,GAAG,GAAG;AACxB,OAAO,SAASC,YAAYA,CAACzC,IAAI,EAAE;EACjC,OAAO,IAAI0C,OAAO,CAACC,OAAO,IAAI;IAC5B,IAAI,CAAC3C,IAAI,CAACO,IAAI,IAAI,CAAC2B,eAAe,CAAClC,IAAI,CAACO,IAAI,CAAC,EAAE;MAC7CoC,OAAO,CAAC,EAAE,CAAC;MACX;IACF;IACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACG,KAAK,GAAGP,YAAY;IAC3BI,MAAM,CAACI,MAAM,GAAGR,YAAY;IAC5BI,MAAM,CAACK,KAAK,CAACC,OAAO,GAAG,4CAA4CV,YAAY,eAAeA,YAAY,mCAAmC;IAC7IK,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,MAAM,CAAC;IACjC,MAAMS,GAAG,GAAGT,MAAM,CAACU,UAAU,CAAC,IAAI,CAAC;IACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,MAAM,GAAG,MAAM;MACjB,MAAM;QACJV,KAAK;QACLC;MACF,CAAC,GAAGO,GAAG;MACP,IAAIG,SAAS,GAAGlB,YAAY;MAC5B,IAAImB,UAAU,GAAGnB,YAAY;MAC7B,IAAIoB,OAAO,GAAG,CAAC;MACf,IAAIC,OAAO,GAAG,CAAC;MACf,IAAId,KAAK,GAAGC,MAAM,EAAE;QAClBW,UAAU,GAAGX,MAAM,IAAIR,YAAY,GAAGO,KAAK,CAAC;QAC5Cc,OAAO,GAAG,EAAEF,UAAU,GAAGD,SAAS,CAAC,GAAG,CAAC;MACzC,CAAC,MAAM;QACLA,SAAS,GAAGX,KAAK,IAAIP,YAAY,GAAGQ,MAAM,CAAC;QAC3CY,OAAO,GAAG,EAAEF,SAAS,GAAGC,UAAU,CAAC,GAAG,CAAC;MACzC;MACAN,GAAG,CAACS,SAAS,CAACP,GAAG,EAAEK,OAAO,EAAEC,OAAO,EAAEH,SAAS,EAAEC,UAAU,CAAC;MAC3D,MAAMI,OAAO,GAAGnB,MAAM,CAACoB,SAAS,CAAC,CAAC;MAClCnB,QAAQ,CAACM,IAAI,CAACc,WAAW,CAACrB,MAAM,CAAC;MACjCsB,MAAM,CAACC,GAAG,CAACC,eAAe,CAACb,GAAG,CAACc,GAAG,CAAC;MACnC1B,OAAO,CAACoB,OAAO,CAAC;IAClB,CAAC;IACDR,GAAG,CAACe,WAAW,GAAG,WAAW;IAC7B,IAAItE,IAAI,CAACO,IAAI,CAACgE,UAAU,CAAC,eAAe,CAAC,EAAE;MACzC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACf,MAAM,GAAG,MAAM;QACpB,IAAIe,MAAM,CAACE,MAAM,IAAI,OAAOF,MAAM,CAACE,MAAM,KAAK,QAAQ,EAAE;UACtDnB,GAAG,CAACc,GAAG,GAAGG,MAAM,CAACE,MAAM;QACzB;MACF,CAAC;MACDF,MAAM,CAACG,aAAa,CAAC3E,IAAI,CAAC;IAC5B,CAAC,MAAM,IAAIA,IAAI,CAACO,IAAI,CAACgE,UAAU,CAAC,WAAW,CAAC,EAAE;MAC5C,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACf,MAAM,GAAG,MAAM;QACpB,IAAIe,MAAM,CAACE,MAAM,EAAE;UACjB/B,OAAO,CAAC6B,MAAM,CAACE,MAAM,CAAC;QACxB;MACF,CAAC;MACDF,MAAM,CAACG,aAAa,CAAC3E,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLuD,GAAG,CAACc,GAAG,GAAGH,MAAM,CAACC,GAAG,CAACS,eAAe,CAAC5E,IAAI,CAAC;IAC5C;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}