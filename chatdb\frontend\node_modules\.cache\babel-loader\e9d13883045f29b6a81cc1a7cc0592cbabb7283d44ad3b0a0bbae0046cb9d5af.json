{"ast": null, "code": "/*\nLanguage: LDIF\nContributors: <PERSON> <<EMAIL>>\nCategory: enterprise, config\nWebsite: https://en.wikipedia.org/wiki/LDAP_Data_Interchange_Format\n*/\nfunction ldif(hljs) {\n  return {\n    name: 'LDIF',\n    contains: [{\n      className: 'attribute',\n      begin: '^dn',\n      end: ': ',\n      excludeEnd: true,\n      starts: {\n        end: '$',\n        relevance: 0\n      },\n      relevance: 10\n    }, {\n      className: 'attribute',\n      begin: '^\\\\w',\n      end: ': ',\n      excludeEnd: true,\n      starts: {\n        end: '$',\n        relevance: 0\n      }\n    }, {\n      className: 'literal',\n      begin: '^-',\n      end: '$'\n    }, hljs.HASH_COMMENT_MODE]\n  };\n}\nmodule.exports = ldif;", "map": {"version": 3, "names": ["ldif", "hljs", "name", "contains", "className", "begin", "end", "excludeEnd", "starts", "relevance", "HASH_COMMENT_MODE", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/智能数据分析系统/chatdb/frontend/node_modules/highlight.js/lib/languages/ldif.js"], "sourcesContent": ["/*\nLanguage: LDIF\nContributors: <PERSON> <<EMAIL>>\nCategory: enterprise, config\nWebsite: https://en.wikipedia.org/wiki/LDAP_Data_Interchange_Format\n*/\nfunction ldif(hljs) {\n  return {\n    name: 'LDIF',\n    contains: [\n      {\n        className: 'attribute',\n        begin: '^dn',\n        end: ': ',\n        excludeEnd: true,\n        starts: {\n          end: '$',\n          relevance: 0\n        },\n        relevance: 10\n      },\n      {\n        className: 'attribute',\n        begin: '^\\\\w',\n        end: ': ',\n        excludeEnd: true,\n        starts: {\n          end: '$',\n          relevance: 0\n        }\n      },\n      {\n        className: 'literal',\n        begin: '^-',\n        end: '$'\n      },\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = ldif;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,IAAI;MACTC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNF,GAAG,EAAE,GAAG;QACRG,SAAS,EAAE;MACb,CAAC;MACDA,SAAS,EAAE;IACb,CAAC,EACD;MACEL,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,IAAI;MACTC,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE;QACNF,GAAG,EAAE,GAAG;QACRG,SAAS,EAAE;MACb;IACF,CAAC,EACD;MACEL,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC,EACDL,IAAI,CAACS,iBAAiB;EAE1B,CAAC;AACH;AAEAC,MAAM,CAACC,OAAO,GAAGZ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}